import { Button } from '@repo/ui/components/button';
import Link from 'next/link';

type OopsContentProps = {
  title?: string | null;
  desc?: string | null;
  btnText?: string | null;
  btnLink?: string | null;
  children?: React.ReactNode;
};

export function OopsContent({ title, desc, btnText, btnLink, children }: OopsContentProps) {
  return (
    <div className="">
      {title && <h1 className="text-3xl font-bold md:text-6xl">{title}</h1>}
      {desc && <p className="">{desc}</p>}
      <div className="">
        {children}
        {btnText && btnLink && (
          <Button asChild>
            <Link href={btnLink}>{btnText}</Link>
          </Button>
        )}
      </div>
    </div>
  );
}
