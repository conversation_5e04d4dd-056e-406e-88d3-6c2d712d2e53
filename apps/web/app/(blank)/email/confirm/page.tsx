'use client';

import { Button } from '@repo/ui/components/button';
import { useSearchParams } from 'next/navigation';
import { Suspense, useState } from 'react';
import { OopsContent } from './oops-content';
import { toast } from '@repo/ui/components/sonner';

export default function EmailConfirmPage() {
  return (
    <Suspense>
      <ConfirmPage />
    </Suspense>
  );
}

function ConfirmPage() {
  const searchParams = useSearchParams();
  const title = searchParams.get('title');
  const desc = searchParams.get('desc');
  const btnText = searchParams.get('btnText');
  const subscriptionId = searchParams.get('subscriptionId');

  const [hide, setHide] = useState(false);
  const [loading, setLoading] = useState(false);

  return (
    <div className="leading-[1.5]">
      <OopsContent title={title} desc={desc}>
        {btnText && subscriptionId && !hide && (
          <Button
            disabled={loading}
            onClick={async () => {
              try {
                setLoading(true);
                // await MailService.confirmEmailSubscribe(projectId, subscriptionId);
                toast.success('Email Subscription Confirmed');
                setHide(true);
              } catch (error) {
                console.error(error);
                toast.error('Email Subscription Failed');
              } finally {
                setLoading(false);
              }
            }}
          >
            {btnText}
          </Button>
        )}
      </OopsContent>
    </div>
  );
}
