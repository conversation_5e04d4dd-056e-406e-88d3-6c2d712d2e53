'use client';

import EmptyMarketList from '@/components/common/empty-list';
import { ErrorBoundary } from '@/components/common/error-boundary';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelMarkets } from '@/hooks/query/channel';
import { useChannelId } from '@/hooks/use-channel-id';
import { GetChannelMarketsOrder } from '@/lib/api/channel/channel.schema.server';
import { GetMarketFilterEnum } from '@/lib/api/market/market.schema.server';
import { useEffect, useState } from 'react';

const filterOptions = [
  { value: 'LIVE', label: 'Live' },
  { value: 'ENDED', label: 'Resolved' },
];

const orderOptions = [
  { value: 'NEWEST', label: 'Newest' },
  { value: 'VOLUME', label: 'Volume' },
];

function ChannelDetailContent() {
  const channelId = useChannelId();
  const [filter, setFilter] = useState<GetMarketFilterEnum>('LIVE');
  const [order, setOrder] = useState<GetChannelMarketsOrder>('NEWEST');
  const {
    data: marketsData,
    isLoading,
    error,
    isError,
  } = useChannelMarkets(channelId, {
    status: filter,
    order,
    // TODO: add paginations
  });

  useEffect(() => {
    if (isError && error) {
      throw error;
    }
  }, [isError, error]);

  const markets = marketsData?.markets || [];
  return (
    <div className="flex flex-col">
      <div className="mb-space-20 mt-space-40 flex items-center gap-[10px]">
        {/* Filter */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as GetMarketFilterEnum)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* Order */}
        <BaseSelect
          value={order}
          onValueChange={value => setOrder(value as GetChannelMarketsOrder)}
        >
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {orderOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 마켓 카드 그리드 */}
      {isLoading ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <PredictionMarketCardSkeleton key={i} />
          ))}
        </div>
      ) : markets.length === 0 ? (
        <div className="flex min-h-[400px] items-center justify-center">
          <EmptyMarketList />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {markets.map(market => (
            <PredictionMarketCard key={market.marketId} item={market} />
          ))}
        </div>
      )}
    </div>
  );
}

export default function ChannelDetailPage() {
  return (
    <ErrorBoundary className="h-full min-h-[800px]">
      <ChannelDetailContent />
    </ErrorBoundary>
  );
}
