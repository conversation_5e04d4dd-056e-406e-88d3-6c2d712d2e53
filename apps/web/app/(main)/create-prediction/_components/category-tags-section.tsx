import { BaseInput } from '@/components/ui/base.input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { cn } from '@repo/ui/lib/utils';
import { Control, useFormState } from 'react-hook-form';
import { CreateMarketFormValues, PLACEHOLDER_TEXT } from '../types';
import { PlusIcon } from 'lucide-react';
import { useCategories } from '@/hooks/query/category';

interface CategoryTagsSectionProps {
  control: Control<CreateMarketFormValues>;
  onTagKeyPress: (e: React.KeyboardEvent) => void;
  onAddTag: () => void;
  tags: string[];
  currentTag: string;
  tagError: string;
  onTagInputChange: (value: string) => void;
  onRemoveTag: (tag: string) => void;
}

export function CategoryTagsSection({
  control,
  onTagKeyPress,
  onAddTag,
  tags,
  currentTag,
  tagError,
  onTagInputChange,
  onRemoveTag,
}: CategoryTagsSectionProps) {
  const { data: categories } = useCategories();
  const { errors } = useFormState({ control });

  const handleTagInputChangeEvent = (e: React.ChangeEvent<HTMLInputElement>) => {
    onTagInputChange(e.target.value);
  };

  return (
    <>
      <FormField
        control={control}
        name="category"
        render={({ field }) => (
          <FormItem data-role="select-category" className="flex-[0.25] gap-0">
            <FormLabel className="mb-[15px] h-[20px] text-sm font-semibold">Category</FormLabel>
            <FormControl>
              <BaseSelect onValueChange={field.onChange} value={field.value}>
                <BaseSelectTrigger
                  className={cn(
                    'bg-gray-2 w-full',
                    errors.category ? 'border-red-500 focus:border-red-500' : ''
                  )}
                >
                  <BaseSelectValue placeholder={PLACEHOLDER_TEXT.category} />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  {!categories || categories?.length === 0 ? (
                    <BaseSelectItem value="undefined" disabled></BaseSelectItem>
                  ) : (
                    categories.map(category => (
                      <BaseSelectItem key={category.categoryName} value={category.categoryName}>
                        {category.categoryName}
                      </BaseSelectItem>
                    ))
                  )}
                </BaseSelectContent>
              </BaseSelect>
            </FormControl>
            <FormMessage className="mt-2" />
          </FormItem>
        )}
      />

      <div className="flex flex-[0.5] flex-col">
        <div data-role="write-tag">
          <div className="mb-[15px] flex h-[20px] items-center">
            <label className="block text-sm font-semibold">Tags</label>
            &nbsp;
            <span className="text-size-xs text-yes-green font-medium">(Up to 3 tags allowed)</span>
          </div>
          <div className="flex w-full items-center gap-2">
            <BaseInput
              placeholder={PLACEHOLDER_TEXT.tag}
              value={currentTag}
              onChange={handleTagInputChangeEvent}
              onKeyDown={onTagKeyPress}
              disabled={tags.length >= 3}
              maxLength={20}
              className={cn('w-full flex-1', {
                'border-red-500 focus:border-red-500': tagError,
              })}
            />
            <button
              type="button"
              onClick={onAddTag}
              disabled={tags.length >= 3 || !currentTag.trim()}
              className="border-line text-gray-3 hover:bg-gray-1 flex size-[36px] items-center justify-center rounded border transition-colors disabled:cursor-not-allowed disabled:opacity-50"
              aria-label="Add tag"
            >
              <PlusIcon className="h-4 w-4" />
            </button>
          </div>
          {tagError && <p className="mt-1 text-sm text-red-500">{tagError}</p>}
          <div className="mt-2 flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <div
                key={index}
                className="rounded-round-md px-space-10 bg-gray-1 border-line flex h-(--tag-height) items-center border"
              >
                <span className="text-size-xs text-gray-3 mr-2">{tag}</span>
                <button
                  type="button"
                  onClick={() => onRemoveTag(tag)}
                  className="text-gray-500 hover:text-gray-700"
                  aria-label="Remove tag"
                >
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 3L3 9M9 9L3 3"
                      stroke="#3B424B"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
