import { BaseInput } from '@/components/ui/base.input';
import SimpleAlert from '@/components/ui/simple.alert';
import { FormControl, FormField, FormItem, FormLabel } from '@repo/ui/components/form';
import { toast } from '@repo/ui/components/sonner';
import { Control, UseFormGetValues, useFormState } from 'react-hook-form';
import { CreateMarketFormValues, PLACEHOLDER_TEXT } from '../types';
import { cn } from '@repo/ui/lib/utils';
import { TOAST_MESSAGE } from '@/lib/constants';

interface DatetimeSectionProps {
  control: Control<CreateMarketFormValues>;
  getValues: UseFormGetValues<CreateMarketFormValues>;
}
// Convert timestamp (seconds) to datetime-local format (YYYY-MM-DDTHH:MM)
const timestampToDatetimeLocal = (timestamp: number): string => {
  if (!timestamp || timestamp === 0) return '';
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Get current time in datetime-local format for min attribute
const getCurrentDatetimeLocal = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Get max time (30 days from now) in datetime-local format for max attribute
const getMaxDatetimeLocal = (): string => {
  const maxDate = new Date();
  maxDate.setDate(maxDate.getDate() + 30); // Add 30 days
  const year = maxDate.getFullYear();
  const month = String(maxDate.getMonth() + 1).padStart(2, '0');
  const day = String(maxDate.getDate()).padStart(2, '0');
  const hours = String(maxDate.getHours()).padStart(2, '0');
  const minutes = String(maxDate.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};
export function DatetimeSection({ control, getValues }: DatetimeSectionProps) {
  const { errors } = useFormState({ control });

  return (
    <section data-role="section" className="gap-space-30 flex items-start">
      <FormField
        control={control}
        name="predictionDeadline"
        render={({ field }) => (
          <FormItem data-role="select-prediction-end-time" className="w-full max-w-[320px] gap-0">
            <FormLabel className="mb-[15px] text-sm font-medium">Prediction end</FormLabel>
            <FormControl className="mb-[15px]">
              <BaseInput
                {...field}
                type="datetime-local"
                placeholder={PLACEHOLDER_TEXT['prediction-end-time']}
                value={timestampToDatetimeLocal(field.value)}
                min={getCurrentDatetimeLocal()}
                max={getMaxDatetimeLocal()}
                className={cn(
                  errors.predictionDeadline ? 'border-red-500 focus:border-red-500' : ''
                )}
                onChange={e => {
                  const dateValue = e.target.value;

                  if (dateValue) {
                    const selectedDate = new Date(dateValue);
                    const currentTime = new Date();

                    // Check minimum time: 10 minutes after current time
                    const minimumTime = new Date(currentTime.getTime() + 10 * 60 * 1000);
                    if (selectedDate <= minimumTime) {
                      toast.error(TOAST_MESSAGE.PREDICTION_END_TIME_MIN);
                      return;
                    }

                    // Check maximum time: 30 days from current time
                    const maximumTime = new Date(currentTime.getTime() + 30 * 24 * 60 * 60 * 1000);
                    if (selectedDate > maximumTime) {
                      toast.error(TOAST_MESSAGE.PREDICTION_END_TIME_MAX);
                      return;
                    }

                    const seconds = Math.floor(selectedDate.getTime() / 1000);
                    field.onChange(seconds);
                  } else {
                    field.onChange(0);
                  }
                }}
              />
            </FormControl>
            <SimpleAlert description="Prediction is not possible after the set time has passed." />
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="resultConfirmDeadline"
        render={({ field }) => (
          <FormItem
            data-role="select-result-confirm-end-time"
            className="w-full max-w-[320px] gap-0"
          >
            <FormLabel className="mb-[15px] text-sm font-medium">Result confirmation end</FormLabel>
            <FormControl className="mb-[15px]">
              <BaseInput
                {...field}
                type="datetime-local"
                placeholder={PLACEHOLDER_TEXT['confirmation-end-time']}
                value={timestampToDatetimeLocal(field.value)}
                min={
                  getValues('predictionDeadline') && getValues('predictionDeadline') > 0
                    ? timestampToDatetimeLocal(getValues('predictionDeadline'))
                    : getCurrentDatetimeLocal()
                }
                max={
                  getValues('predictionDeadline') && getValues('predictionDeadline') > 0
                    ? timestampToDatetimeLocal(getValues('predictionDeadline') + 7 * 24 * 60 * 60)
                    : getMaxDatetimeLocal()
                }
                className={cn(
                  errors.resultConfirmDeadline ? 'border-red-500 focus:border-red-500' : ''
                )}
                onChange={e => {
                  const dateValue = e.target.value;
                  const predictionDeadline = getValues('predictionDeadline');

                  if (dateValue) {
                    const selectedDate = new Date(dateValue);
                    const predictionEndDate = new Date(predictionDeadline * 1000);

                    // Check minimum time: 5 minutes after prediction end time
                    const minimumTime = new Date(predictionEndDate.getTime() + 5 * 60 * 1000);
                    if (selectedDate <= minimumTime) {
                      toast.error(TOAST_MESSAGE.RESULT_CONFIRMATION_TIME_MIN);
                      return;
                    }

                    // Check maximum time: 7 days after prediction end time
                    const maximumTime = new Date(
                      predictionEndDate.getTime() + 7 * 24 * 60 * 60 * 1000
                    );
                    if (selectedDate > maximumTime) {
                      toast.error(TOAST_MESSAGE.RESULT_CONFIRMATION_TIME_MAX);
                      return;
                    }

                    const seconds = Math.floor(selectedDate.getTime() / 1000);
                    field.onChange(seconds);
                  } else {
                    field.onChange(0);
                  }
                }}
              />
            </FormControl>
            <SimpleAlert description="If the result are not confirmed by this time, it will automatically be set to 'Void,' and you may lose your deposit." />
            {/* <FormMessage /> */}
          </FormItem>
        )}
      />

      <div data-role="select-dispute-period" className="space-y-space-15 w-full max-w-[340px]">
        <label className="block text-sm font-medium">Disputed Period</label>
        <div className="w-full">
          <div className="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-sm text-gray-500">
            30 minutes
          </div>
        </div>

        <SimpleAlert description="After the verifier confirms the result, it is the time during which an objection to the result can be filed. Users who wish to raise an objection must deposit a bond equal to the verifier's bond. If the dispute is upheld, the channel leader must determine the final result." />
      </div>
    </section>
  );
}
