import DepositChannelCollateralButton from '@/components/actions/deposit-channel-collateral-button';
import { DollarInput } from '@/components/ui/dollar-input';
import SimpleAlert from '@/components/ui/simple.alert';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { pxToRem } from '@repo/ui/lib/utils';
import { Control, useFormState } from 'react-hook-form';
import { RefObject } from 'react';
import { CreateMarketFormValues, PLACEHOLDER_TEXT } from '../types';
import { ChannelCollateralTransformed } from '@/lib/api/channel/channel.transform';
import { Skeleton } from '@repo/ui/components/skeleton';
import { formatCurrency } from '@/lib/format';
import { cn } from '@repo/ui/lib/utils';

interface DepositSectionProps {
  control: Control<CreateMarketFormValues>;
  collateral?: ChannelCollateralTransformed;
  addDepositButtonRef: RefObject<HTMLButtonElement | null>;
}

export function DepositSection({ control, collateral, addDepositButtonRef }: DepositSectionProps) {
  const minValue = 50;
  const maxValue = collateral?.available.formatted ? parseFloat(collateral.available.formatted) : 0;
  const { errors } = useFormState({ control });

  return (
    <div className="flex flex-[0.25] flex-col">
      <FormField
        control={control}
        name="collateralAmount"
        render={({ field }) => (
          <FormItem data-role="write-deposit" className="gap-0">
            <div className="mb-[15px] flex h-[20px] items-center justify-between">
              <FormLabel className="text-sm font-semibold">Deposit</FormLabel>
              <div className="gap-space-10 flex items-center">
                {collateral ? (
                  <div className="text-size-xxs text-gray-3 font-medium">
                    Deposit Balance {formatCurrency(collateral.available.formatted)}
                  </div>
                ) : (
                  <Skeleton className="h-[20px] w-[100px]" />
                )}
                <DepositChannelCollateralButton
                  ref={addDepositButtonRef}
                  style={{
                    height: '20px',
                    borderRadius: '4px',
                    fontSize: '10px',
                    width: pxToRem(80),
                  }}
                  variant="dark"
                >
                  Add Deposit
                </DepositChannelCollateralButton>
              </div>
            </div>
            <FormControl>
              <div className="relative">
                <DollarInput
                  placeholder={PLACEHOLDER_TEXT.deposit}
                  value={field.value || 0}
                  onChange={field.onChange}
                  minValue={minValue}
                  maxValue={maxValue}
                  className={cn(
                    errors.collateralAmount ? 'border-red-500 focus:border-red-500' : ''
                  )}
                />
                <button
                  className="text-size-xxs text-icon-dark border-line hover:bg-gray-1 hover:text-icon-light absolute top-1/2 right-2 h-[24px] w-[43px] -translate-y-1/2 border transition-colors"
                  onClick={e => {
                    e.preventDefault();
                    field.onChange(maxValue);
                  }}
                >
                  Max
                </button>
              </div>
            </FormControl>
            <FormMessage className="mt-2" />
          </FormItem>
        )}
      />
      <div className="py-space-10 px-[5px]">
        <SimpleAlert description="Twice the set deposit amount is the maximum volume for each option." />
      </div>
    </div>
  );
}
