import { AvatarImageUploader } from '@/components/ui/avatar-image-uploader';
import { useCreatePrediction } from '../_context/create-prediction-context';

interface MarketImageSectionProps {}

export function MarketImageSection({}: MarketImageSectionProps) {
  const { imagePreview, handleImageFileSelect } = useCreatePrediction();
  return (
    <section data-role="section" className="gap-space-30 flex items-center">
      <AvatarImageUploader
        imageUrl={imagePreview}
        onFileSelect={handleImageFileSelect}
        size={80}
        maxSize={1 * 1024 * 1024} // 1MB
        acceptedTypes={['image/jpeg', 'image/png']}
      />
      <div className="text-sm text-gray-500">
        <div className="mb-1">
          Please upload a representative image for the prediction to be generated. If not uploaded,
          it will be replaced with a default image.
        </div>
        <div>
          At least <b>80px X 80px</b> recommended.
          <br />
          <b>JPG or PNG</b> is allowed. File size up to <b>1MB.</b>
        </div>
      </div>
    </section>
  );
}
