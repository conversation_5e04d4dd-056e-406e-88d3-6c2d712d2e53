import { BaseButton, GreenButton, NeutralButton } from '@/components/ui/base.button';
import { InputWithCharacterCount } from '@/components/ui/input-with-character-count';
import { ICON_PATH } from '@/lib/constants';
import { FormControl, FormField, FormItem } from '@repo/ui/components/form';
import Image from 'next/image';
import { Control, useFormState } from 'react-hook-form';
import { CreateMarketFormValues, PLACEHOLDER_TEXT } from '../types';
import { useCreatePredictionStore } from '../_store/create-prediction.store';
import { cn } from '@repo/ui/lib/utils';

interface QuestSectionProps {
  control: Control<CreateMarketFormValues>;
  outcomes: string[];
  marketValidateIsPending: boolean;
  onTitleChange: (value: string) => void;
  onEthicalReview: (e: React.MouseEvent<HTMLButtonElement>) => void;
  onUpdateOutcome: (index: number, value: string) => void;
  onAddOutcome: () => void;
  onRemoveOutcome: (index: number) => void;
}

export function QuestionSection({
  control,
  outcomes,
  marketValidateIsPending,
  onTitleChange,
  onEthicalReview,
  onUpdateOutcome,
  onAddOutcome,
  onRemoveOutcome,
}: QuestSectionProps) {
  const { duplicateOutcomes, titleEthicalReviewFailed, validationId } = useCreatePredictionStore();
  const { errors } = useFormState({ control });

  return (
    <section data-role="section" className="">
      <header className="text-size-sm mb-[15px] flex items-center justify-between font-medium">
        <div>Question</div>
        <div className="gap-space-10 flex items-center">
          <p className="text-size-xs text-yes-green">
            The Void Outcome is automatically added during the resolution phase, so you don&apos;t
            need to add it manually.
          </p>
          <NeutralButton type="button">
            <Image src={ICON_PATH.LEARN_MORE} alt="Learn More" width={16} height={16} />
            Learn More
          </NeutralButton>
        </div>
      </header>
      <div className="">
        <div className="mb-[10px] flex justify-between gap-[8px]">
          <FormField
            control={control}
            name="title"
            render={({ field }) => (
              <FormItem data-role="write-market-title" className="flex-1 space-y-2">
                <FormControl>
                  <div className="relative">
                    <InputWithCharacterCount
                      placeholder={PLACEHOLDER_TEXT['market-title']}
                      maxLength={100}
                      value={field.value}
                      onChange={e => {
                        field.onChange(e.target.value);
                        onTitleChange(e.target.value);
                      }}
                      className={cn(
                        titleEthicalReviewFailed ? 'border-red-500 focus:border-red-500' : '',
                        errors.title ? 'border-red-500 focus:border-red-500' : ''
                      )}
                    />
                  </div>
                </FormControl>
              </FormItem>
            )}
          />
          <GreenButton
            type="button"
            onClick={onEthicalReview}
            disabled={marketValidateIsPending || !!validationId}
          >
            {marketValidateIsPending ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                Reviewing...
              </div>
            ) : validationId ? (
              <div className="flex items-center gap-2">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 6L9 17L4 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Reviewed
              </div>
            ) : (
              'Ethical Review'
            )}
          </GreenButton>
        </div>
        <p className="text-size-xs text-icon-dark mb-[20px] pl-3 font-semibold">
          Prediction can only be generated after the question passes{' '}
          <span className="text-no-red">an AI review for ethical compliance.</span>
        </p>
        <div data-role="market-outcomes" className="gap-space-10 flex flex-col">
          <div className="gap-space-10 flex flex-col">
            {outcomes.map((outcome, index) => (
              <div key={index} className="gap-space-10 flex items-center">
                <div className="text-size-xs font-semibold">{index + 1}</div>
                <div className="relative flex-1">
                  <InputWithCharacterCount
                    className={cn(
                      duplicateOutcomes.includes(index)
                        ? 'border-red-500 focus:border-red-500'
                        : '',
                      errors.outcomes?.[index] ? 'border-red-500 focus:border-red-500' : ''
                    )}
                    placeholder={PLACEHOLDER_TEXT['market-outcomes']}
                    value={outcome}
                    maxLength={50}
                    onValueChange={value => onUpdateOutcome(index, value)}
                    characterCountPosition="bottom-right"
                    characterCountClassName="bottom-3 right-3"
                  />
                </div>
                {outcomes.length > 2 && (
                  <button
                    type="button"
                    onClick={() => onRemoveOutcome(index)}
                    className="rounded-sm p-1 hover:bg-gray-100"
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="16" height="16" rx="8" fill="#3B424B" />
                      <path
                        d="M11.129 4.8712C11.3788 5.12101 11.3788 5.52649 11.129 5.7763L8.9051 8.00022L11.129 10.2241C11.3788 10.4739 11.3788 10.8794 11.129 11.1292C10.8792 11.379 10.4737 11.379 10.2239 11.1292L8 8.90532L5.77608 11.1292C5.52627 11.379 5.12079 11.379 4.87098 11.1292C4.62104 10.8793 4.62117 10.4739 4.87098 10.2241L7.0949 8.00022L4.87098 5.7763C4.62104 5.52636 4.62118 5.12101 4.87098 4.8712C5.12079 4.6214 5.52614 4.62126 5.77608 4.8712L8 7.09512L10.2239 4.8712C10.4737 4.6214 10.8791 4.62126 11.129 4.8712Z"
                        fill="white"
                      />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>
          {outcomes.length < 10 && (
            <BaseButton
              type="button"
              variant="neutral"
              size="sm"
              className="text-size-xs px-space-10 w-[125px] justify-center self-end"
              onClick={onAddOutcome}
            >
              <div>
                <Image src={ICON_PATH.BTN_ICON} alt="Add Outcome" width={20} height={20} />
              </div>
              Add Outcome
            </BaseButton>
          )}
        </div>
      </div>
    </section>
  );
}
