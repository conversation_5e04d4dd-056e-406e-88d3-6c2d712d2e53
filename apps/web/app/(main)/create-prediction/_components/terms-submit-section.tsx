import { Checkbox } from '@repo/ui/components/checkbox';

interface TermsSubmitSectionProps {
  isTermsAccepted: boolean;
  onTermsAcceptedChange: (accepted: boolean) => void;
}

export function TermsSubmitSection({
  isTermsAccepted,
  onTermsAcceptedChange,
}: TermsSubmitSectionProps) {
  return (
    <>
      <div className="my-8 border-t border-gray-200"></div>
      <div>
        <div className="flex flex-row items-start space-y-0 space-x-3">
          <Checkbox
            checked={isTermsAccepted}
            id="terms-accepted"
            onCheckedChange={v => {
              onTermsAcceptedChange(v === 'indeterminate' ? false : v);
            }}
          />
          <div className="space-y-1 leading-none select-none">
            <label htmlFor="terms-accepted" className="text-sm font-normal">
              I agree to the
              <span className="font-semibold underline">&nbsp;Terms of Use&nbsp;</span>
              of prediction creation and deposit system.
            </label>
          </div>
        </div>
      </div>
    </>
  );
}
