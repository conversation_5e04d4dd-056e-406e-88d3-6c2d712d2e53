import { BaseInput } from '@/components/ui/base.input';
import { NeutralButton } from '@/components/ui/base.button';
import { TextareaWithCharacterCount } from '@/components/ui/textarea-with-character-count';
import { ICON_PATH } from '@/lib/constants';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import Image from 'next/image';
import { Control, useFormState } from 'react-hook-form';
import { CreateMarketFormValues, PLACEHOLDER_TEXT } from '../types';
import { cn } from '@repo/ui/lib/utils';
import { useCreatePrediction } from '../_context/create-prediction-context';

interface UrlsDescriptionSectionProps {
  control: Control<CreateMarketFormValues>;
}

export function UrlsDescriptionSection({ control }: UrlsDescriptionSectionProps) {
  const { errors } = useFormState({ control });
  const { setShowExamplePopup } = useCreatePrediction();

  return (
    <section data-role="section" className="space-y-space-30">
      <FormField
        control={control}
        name="broadcastURL"
        render={({ field }) => (
          <FormItem data-role="write-broadcast-url" className="gap-0">
            <div className="mb-15px] flex items-center">
              <FormLabel className="text-sm font-semibold">Broadcast Video URL</FormLabel>
              &nbsp;
              <span className="text-yes-green text-size-xs">(Optional)</span>
            </div>
            <FormControl>
              <BaseInput
                placeholder={PLACEHOLDER_TEXT['broadcast-url']}
                {...field}
                className={cn(errors.broadcastURL ? 'border-red-500 focus:border-red-500' : '')}
              />
            </FormControl>
            <FormMessage className="mt-2" />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="referenceURL"
        render={({ field }) => (
          <FormItem data-role="write-reference-url" className="gap-0">
            <FormLabel className="mb-[15px] text-sm font-semibold">Reference URL</FormLabel>
            <FormControl>
              <BaseInput
                placeholder={PLACEHOLDER_TEXT['reference-url']}
                {...field}
                className={cn(errors.referenceURL ? 'border-red-500 focus:border-red-500' : '')}
              />
            </FormControl>
            <FormMessage className="mt-2" />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name="description"
        render={({ field }) => (
          <FormItem data-role="write-description" className="gap-0">
            <div className="mb-[15px] flex items-center justify-between">
              <FormLabel className="text-sm font-semibold">Description</FormLabel>
              <div className="gap-space-10 flex items-center">
                <p className="text-size-xs text-yes-green">
                  If the market resolution rules are not clear, the prediction may become void.
                </p>
                <NeutralButton
                  fontSize="xs"
                  className="px-space-10"
                  onClick={e => {
                    e.preventDefault();
                    setShowExamplePopup(true);
                  }}
                >
                  <Image src={ICON_PATH.EXAMPLE_DARK} alt="Example" width={16} height={16} />
                  Example
                </NeutralButton>
              </div>
            </div>
            <FormControl>
              <TextareaWithCharacterCount
                placeholder={PLACEHOLDER_TEXT.description}
                value={field.value}
                maxLength={2000}
                onValueChange={field.onChange}
                className={cn(errors.description ? 'border-red-500 focus:border-red-500' : '')}
              />
            </FormControl>
            <FormMessage className="mt-2" />
          </FormItem>
        )}
      />
    </section>
  );
}
