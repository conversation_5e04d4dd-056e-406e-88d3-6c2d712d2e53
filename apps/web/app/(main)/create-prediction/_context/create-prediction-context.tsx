'use client';

import React, { createContext, useContext, useReducer } from 'react';
import { initialState } from './types';
import { createPredictionReducer } from './reducer';
import { useTagManagement } from '../_hooks/use-tag-management';
import { useImageUpload } from '../_hooks/use-image-upload';
import { useValidationState } from '../_hooks/use-validation-state';
import { usePopupState } from '../_hooks/use-popup-state';

interface CreatePredictionContextType {
  // State access
  tags: string[];
  currentTag: string;
  tagError: string;
  imageFile: File | null;
  imagePreview: string;
  validationId: string | null;
  titleEthicalReviewFailed: boolean;
  showEthicalFailPopup: boolean;
  showExamplePopup: boolean;
  duplicateOutcomes: number[];
  isTermsAccepted: boolean;

  // Tag management
  addTag: () => void;
  removeTag: (tag: string) => void;
  handleTagInputChange: (value: string) => void;
  handleTagKeyPress: (e: React.KeyboardEvent) => void;

  // Image upload
  handleImageFileSelect: (file: File | null) => void;

  // Validation state
  setValidationId: (id: string | null) => void;
  setTitleEthicalReviewFailed: (failed: boolean) => void;
  setDuplicateOutcomes: (outcomes: number[]) => void;

  // Popup state
  setShowEthicalFailPopup: (show: boolean) => void;
  setShowExamplePopup: (show: boolean) => void;

  // Terms
  setIsTermsAccepted: (accepted: boolean) => void;
}

const CreatePredictionContext = createContext<CreatePredictionContextType | undefined>(undefined);

export const useCreatePrediction = () => {
  const context = useContext(CreatePredictionContext);
  if (!context) {
    throw new Error('useCreatePrediction must be used within CreatePredictionProvider');
  }
  return context;
};

export const CreatePredictionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(createPredictionReducer, initialState);

  // Custom hooks for different concerns
  const tagManagement = useTagManagement(state.tags, state.currentTag, state.tagError, dispatch);
  const imageUpload = useImageUpload(dispatch);
  const validationState = useValidationState(dispatch);
  const popupState = usePopupState(dispatch);

  const setIsTermsAccepted = (accepted: boolean) => {
    dispatch({ type: 'SET_IS_TERMS_ACCEPTED', payload: accepted });
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      tagManagement.addTag();
    }
  };

  const value: CreatePredictionContextType = {
    // State access
    tags: state.tags,
    currentTag: state.currentTag,
    tagError: state.tagError,
    imageFile: state.imageFile,
    imagePreview: state.imagePreview,
    validationId: state.validationId,
    titleEthicalReviewFailed: state.titleEthicalReviewFailed,
    showEthicalFailPopup: state.showEthicalFailPopup,
    showExamplePopup: state.showExamplePopup,
    duplicateOutcomes: state.duplicateOutcomes,
    isTermsAccepted: state.isTermsAccepted,

    // Actions
    ...tagManagement,
    ...imageUpload,
    ...validationState,
    ...popupState,
    handleTagKeyPress,
    setIsTermsAccepted,
  };

  return (
    <CreatePredictionContext.Provider value={value}>{children}</CreatePredictionContext.Provider>
  );
};
