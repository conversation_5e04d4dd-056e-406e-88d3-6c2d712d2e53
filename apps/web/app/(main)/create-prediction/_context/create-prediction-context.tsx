'use client';

import { createContext, useContext, useState, ReactNode } from 'react';
import { isForbiddenTag } from '../types';

interface CreatePredictionState {
  // Tags state
  tags: string[];
  currentTag: string;
  tagError: string;

  // Image state
  imageFile: File | null;
  imagePreview: string;

  // Validation state
  validationId: string | null;
  titleEthicalReviewFailed: boolean;

  // Popup states
  showEthicalFailPopup: boolean;
  showExamplePopup: boolean;

  // Outcomes state
  duplicateOutcomes: number[];

  // Terms state
  isTermsAccepted: boolean;
}

interface CreatePredictionActions {
  // Tag actions
  setTags: (tags: string[]) => void;
  setCurrentTag: (tag: string) => void;
  setTagError: (error: string) => void;
  addTag: () => void;
  removeTag: (tag: string) => void;
  handleTagInputChange: (value: string) => void;

  // Image actions
  setImageFile: (file: File | null) => void;
  setImagePreview: (preview: string) => void;
  handleImageFileSelect: (file: File | null) => void;

  // Validation actions
  setValidationId: (id: string | null) => void;
  setTitleEthicalReviewFailed: (failed: boolean) => void;

  // Popup actions
  setShowEthicalFailPopup: (show: boolean) => void;
  setShowExamplePopup: (show: boolean) => void;

  // Outcomes actions
  setDuplicateOutcomes: (outcomes: number[]) => void;
  validateOutcomes: (outcomes: string[]) => void;

  // Terms actions
  setIsTermsAccepted: (accepted: boolean) => void;

  // Reset
  resetAll: () => void;
}

type CreatePredictionContextType = CreatePredictionState & CreatePredictionActions;

const CreatePredictionContext = createContext<CreatePredictionContextType | null>(null);

const initialState: CreatePredictionState = {
  tags: [],
  currentTag: '',
  tagError: '',
  imageFile: null,
  imagePreview: '',
  validationId: null,
  titleEthicalReviewFailed: false,
  showEthicalFailPopup: false,
  showExamplePopup: false,
  duplicateOutcomes: [],
  isTermsAccepted: false,
};

export function CreatePredictionProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<CreatePredictionState>(initialState);

  // Tag actions
  const setTags = (tags: string[]) => setState(prev => ({ ...prev, tags }));
  const setCurrentTag = (currentTag: string) => setState(prev => ({ ...prev, currentTag }));
  const setTagError = (tagError: string) => setState(prev => ({ ...prev, tagError }));

  const addTag = () => {
    if (state.currentTag.trim() && state.tags.length < 3 && !state.tags.includes(state.currentTag.trim())) {
      if (isForbiddenTag(state.currentTag.trim())) {
        setTagError(`'${state.currentTag.trim()}' is a forbidden tag. Please use a different tag.`);
        return;
      }

      const newTags = [...state.tags, state.currentTag.trim()];
      setState(prev => ({
        ...prev,
        tags: newTags,
        currentTag: '',
        tagError: '',
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = state.tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
  };

  const handleTagInputChange = (value: string) => {
    setCurrentTag(value);

    if (value.trim() && isForbiddenTag(value.trim())) {
      setTagError(`'${value.trim()}' is a forbidden tag. Please use a different tag.`);
    } else {
      setTagError('');
    }
  };

  // Image actions
  const setImageFile = (imageFile: File | null) => setState(prev => ({ ...prev, imageFile }));
  const setImagePreview = (imagePreview: string) => setState(prev => ({ ...prev, imagePreview }));

  const handleImageFileSelect = (file: File | null) => {
    setImageFile(file);

    if (file) {
      const reader = new FileReader();
      const controller = new AbortController();

      reader.onload = e => {
        if (!controller.signal.aborted && e.target?.result) {
          const result = e.target.result;
          if (typeof result === 'string') {
            setImagePreview(result);
          }
        }
      };

      reader.onerror = () => {
        if (!controller.signal.aborted) {
          console.error('Failed to read file');
          setImagePreview('');
        }
      };

      reader.onabort = () => {
        console.log('File reading aborted');
      };

      try {
        reader.readAsDataURL(file);
      } catch (error) {
        console.error('Error reading file:', error);
        setImagePreview('');
      }

      return () => {
        controller.abort();
        if (reader.readyState === FileReader.LOADING) {
          reader.abort();
        }
      };
    } else {
      setImagePreview('');
    }
  };

  // Validation actions
  const setValidationId = (validationId: string | null) => setState(prev => ({ ...prev, validationId }));
  const setTitleEthicalReviewFailed = (titleEthicalReviewFailed: boolean) => 
    setState(prev => ({ ...prev, titleEthicalReviewFailed }));

  // Popup actions
  const setShowEthicalFailPopup = (showEthicalFailPopup: boolean) => 
    setState(prev => ({ ...prev, showEthicalFailPopup }));
  const setShowExamplePopup = (showExamplePopup: boolean) => 
    setState(prev => ({ ...prev, showExamplePopup }));

  // Outcomes actions
  const setDuplicateOutcomes = (duplicateOutcomes: number[]) => 
    setState(prev => ({ ...prev, duplicateOutcomes }));

  const validateOutcomes = (outcomes: string[]) => {
    const trimmedOutcomes = outcomes
      .map((outcome, idx) => ({ value: outcome.trim().toLowerCase(), index: idx }))
      .filter(item => item.value !== '');

    const duplicateIndexes: number[] = [];
    const seen = new Map<string, number[]>();

    trimmedOutcomes.forEach(item => {
      if (seen.has(item.value)) {
        seen.get(item.value)!.push(item.index);
      } else {
        seen.set(item.value, [item.index]);
      }
    });

    seen.forEach(indexes => {
      if (indexes.length > 1) {
        duplicateIndexes.push(...indexes);
      }
    });

    setDuplicateOutcomes(duplicateIndexes);
  };

  // Terms actions
  const setIsTermsAccepted = (isTermsAccepted: boolean) => 
    setState(prev => ({ ...prev, isTermsAccepted }));

  // Reset
  const resetAll = () => setState(initialState);

  const contextValue: CreatePredictionContextType = {
    ...state,
    setTags,
    setCurrentTag,
    setTagError,
    addTag,
    removeTag,
    handleTagInputChange,
    setImageFile,
    setImagePreview,
    handleImageFileSelect,
    setValidationId,
    setTitleEthicalReviewFailed,
    setShowEthicalFailPopup,
    setShowExamplePopup,
    setDuplicateOutcomes,
    validateOutcomes,
    setIsTermsAccepted,
    resetAll,
  };

  return (
    <CreatePredictionContext.Provider value={contextValue}>
      {children}
    </CreatePredictionContext.Provider>
  );
}

export function useCreatePrediction() {
  const context = useContext(CreatePredictionContext);
  if (!context) {
    throw new Error('useCreatePrediction must be used within CreatePredictionProvider');
  }
  return context;
}
