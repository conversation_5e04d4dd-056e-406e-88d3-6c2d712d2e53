import { CreatePredictionState, CreatePredictionAction, initialState } from './types';

export const createPredictionReducer = (
  state: CreatePredictionState,
  action: CreatePredictionAction
): CreatePredictionState => {
  switch (action.type) {
    case 'SET_TAGS':
      return { ...state, tags: action.payload };

    case 'SET_CURRENT_TAG':
      return { ...state, currentTag: action.payload };

    case 'SET_TAG_ERROR':
      return { ...state, tagError: action.payload };

    case 'ADD_TAG':
      if (state.tags.includes(action.payload)) {
        return { ...state, tagError: 'Tag already exists' };
      }
      return {
        ...state,
        tags: [...state.tags, action.payload],
        currentTag: '',
        tagError: '',
      };

    case 'REMOVE_TAG':
      return {
        ...state,
        tags: state.tags.filter(tag => tag !== action.payload),
      };

    case 'SET_IMAGE_FILE':
      return { ...state, imageFile: action.payload };

    case 'SET_IMAGE_PREVIEW':
      return { ...state, imagePreview: action.payload };

    case 'SET_VALIDATION_ID':
      return { ...state, validationId: action.payload };

    case 'SET_TITLE_ETHICAL_REVIEW_FAILED':
      return { ...state, titleEthicalReviewFailed: action.payload };

    case 'SET_SHOW_ETHICAL_FAIL_POPUP':
      return { ...state, showEthicalFailPopup: action.payload };

    case 'SET_SHOW_EXAMPLE_POPUP':
      return { ...state, showExamplePopup: action.payload };

    case 'SET_DUPLICATE_OUTCOMES':
      return { ...state, duplicateOutcomes: action.payload };

    case 'SET_IS_TERMS_ACCEPTED':
      return { ...state, isTermsAccepted: action.payload };

    case 'RESET_ALL':
      return initialState;

    default:
      return state;
  }
};
