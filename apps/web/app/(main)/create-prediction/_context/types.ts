export interface CreatePredictionState {
  // Tags state
  tags: string[];
  currentTag: string;
  tagError: string;

  // Image state
  imageFile: File | null;
  imagePreview: string;

  // Validation state
  validationId: string | null;
  titleEthicalReviewFailed: boolean;

  // Popup states
  showEthicalFailPopup: boolean;
  showExamplePopup: boolean;

  // Outcomes state
  duplicateOutcomes: number[];

  // Terms state
  isTermsAccepted: boolean;
}

export type CreatePredictionAction =
  | { type: 'SET_TAGS'; payload: string[] }
  | { type: 'SET_CURRENT_TAG'; payload: string }
  | { type: 'SET_TAG_ERROR'; payload: string }
  | { type: 'ADD_TAG'; payload: string }
  | { type: 'REMOVE_TAG'; payload: string }
  | { type: 'SET_IMAGE_FILE'; payload: File | null }
  | { type: 'SET_IMAGE_PREVIEW'; payload: string }
  | { type: 'SET_VALIDATION_ID'; payload: string | null }
  | { type: 'SET_TITLE_ETHICAL_REVIEW_FAILED'; payload: boolean }
  | { type: 'SET_SHOW_ETHICAL_FAIL_POPUP'; payload: boolean }
  | { type: 'SET_SHOW_EXAMPLE_POPUP'; payload: boolean }
  | { type: 'SET_DUPLICATE_OUTCOMES'; payload: number[] }
  | { type: 'SET_IS_TERMS_ACCEPTED'; payload: boolean }
  | { type: 'RESET_ALL' };

export const initialState: CreatePredictionState = {
  tags: [],
  currentTag: '',
  tagError: '',
  imageFile: null,
  imagePreview: '',
  validationId: null,
  titleEthicalReviewFailed: false,
  showEthicalFailPopup: false,
  showExamplePopup: false,
  duplicateOutcomes: [],
  isTermsAccepted: false,
};
