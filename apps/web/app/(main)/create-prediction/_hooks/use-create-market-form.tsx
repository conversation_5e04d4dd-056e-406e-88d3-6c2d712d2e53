import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useRef } from 'react';
import { toast } from '@repo/ui/components/sonner';
import { useChannelCollateral } from '@/hooks/query/channel';
import { useCreateMarketFlow, useMarketValidate } from '@/hooks/query/market';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import { useGlobalStore } from '@/store/global.store';
import { normalizeTextForFormData, toAmount } from '@/lib/format';
import InsufficientDepositBalance from '@/components/ui/popup/insufficient-deposit-balance';
import {
  CreateMarketFormSchema,
  CreateMarketFormValues,
  isForbiddenTag,
  isForbiddenOutcome,
  FORBIDDEN_TAGS,
} from '../types';
import { TOAST_MESSAGE } from '@/lib/constants';
import { useCreatePrediction } from '../_context/create-prediction-context';

export function useCreateMarketForm() {
  const { data: currentCollateral } = useChannelCollateral();
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  const marketValidate = useMarketValidate();
  const createMarketTransaction = useCreateMarketFlow();
  const { openPopup, closePopup } = usePopupStore();

  // Context에서 UI 상태 가져오기
  const {
    tags,
    imageFile,
    validationId,
    duplicateOutcomes,
    isTermsAccepted,
    titleEthicalReviewFailed,
    setTags,
    setValidationId,
    setTitleEthicalReviewFailed,
    setShowEthicalFailPopup,
    validateOutcomes,
  } = useCreatePrediction();

  const form = useForm<CreateMarketFormValues>({
    resolver: zodResolver(CreateMarketFormSchema),

    defaultValues: {
      title: '',
      description: '',
      imageUrl: '',
      predictionDeadline: 0,
      resultConfirmDeadline: 0,
      category: '',
      collateralAmount: undefined,
      outcomes: ['', ''],
      tags: [],
      broadcastURL: '',
      referenceURL: '',
    },
  });
  const { watch, setValue } = form;
  const outcomes = watch('outcomes');

  const addDepositButtonRef = useRef<HTMLButtonElement>(null);

  // Sync form tags with context tags
  useEffect(() => {
    setValue('tags', tags);
  }, [tags, setValue]);

  // Outcomes management (form 관련 로직만)
  const addOutcome = () => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length < 10) {
      setValue('outcomes', [...currentOutcomes, '']);
    }
  };

  const removeOutcome = (index: number) => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length > 2) {
      const newOutcomes = currentOutcomes.filter((_, i) => i !== index);
      setValue('outcomes', newOutcomes);
      validateOutcomes(newOutcomes);
    }
  };

  const updateOutcome = (index: number, value: string) => {
    const currentOutcomes = watch('outcomes');
    const newOutcomes = [...currentOutcomes];
    newOutcomes[index] = value;
    setValue('outcomes', newOutcomes);
    validateOutcomes(newOutcomes);
  };

  const handleEthicalReview = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const title = form.getValues('title');
    if (!title.trim()) {
      toast.error('Please enter a title.');
      return;
    }

    try {
      setTitleEthicalReviewFailed(false);
      setShowEthicalFailPopup(false);

      const validationResult = await marketValidate.mutateAsync({
        title: title,
      });

      if (!validationResult.isEthical) {
        setTitleEthicalReviewFailed(true);
        setShowEthicalFailPopup(true);
        return;
      }

      if (!validationResult.validationId) {
        toast.error('Failed to get validation ID.');
        setTitleEthicalReviewFailed(true);
        return;
      }

      setValidationId(validationResult.validationId);
      toast.success(TOAST_MESSAGE.ETHICAL_REVIEW_SUCCESS);
    } catch (error) {
      console.error('Ethical review failed:', error);
      setTitleEthicalReviewFailed(true);
    }
  };

  const handleEthicalFailPopupClose = () => {
    setShowEthicalFailPopup(false);
    setTitleEthicalReviewFailed(true);
    setValidationId(null);
  };

  const handleTitleChange = (value: string) => {
    if (titleEthicalReviewFailed) {
      setTitleEthicalReviewFailed(false);
    }
    if (validationId) {
      setValidationId(null);
    }
  };

  const onSubmit = async (values: CreateMarketFormValues) => {
    if (!safeSmartAccountAddress) {
      return;
    }

    validateOutcomes(values.outcomes);

    if (duplicateOutcomes.length > 0) {
      toast.error(TOAST_MESSAGE.DUPLICATE_OUTCOME);
      return;
    }

    // Check for forbidden outcomes
    const forbiddenOutcomes = values.outcomes.filter(
      outcome => outcome.trim() !== '' && isForbiddenOutcome(outcome.trim())
    );

    if (forbiddenOutcomes.length > 0) {
      toast.error(TOAST_MESSAGE.FORBIDDEN_OUTCOME);
      return;
    }

    if (values.tags?.some(tag => isForbiddenTag(tag))) {
      toast.error(
        'Forbidden tags detected. Please remove forbidden tags: ' + FORBIDDEN_TAGS.join(', ')
      );
      return;
    }

    if (values.collateralAmount < 50) {
      openPopup(
        <InsufficientDepositBalance
          requiredAmount="$50"
          onAddDeposit={() => {
            closePopup();

            if (addDepositButtonRef.current) {
              addDepositButtonRef.current.click();
            }
          }}
          onCancel={() => {
            closePopup();
          }}
        />
      );
      return;
    }

    // Safe number parsing with validation
    const maxAllowedAmount = currentCollateral?.available.formatted
      ? (() => {
          const parsed = parseFloat(currentCollateral.available.formatted);
          return isNaN(parsed) ? 0 : parsed;
        })()
      : 0;

    if (values.collateralAmount > maxAllowedAmount) {
      toast.error(
        `The collateral amount cannot exceed available collateral (${maxAllowedAmount.toLocaleString()}).`
      );
      return;
    }

    if (!validationId) {
      toast.error('Get the ethical review first.');
      return;
    }

    const filteredData = {
      ...values,
      outcomes: values.outcomes.filter(outcome => outcome.trim() !== ''),
      tags: values.tags?.filter(tag => tag.trim() !== '') || [],
    };

    const collateralAmountBigInt = BigInt(toAmount(filteredData.collateralAmount));
    const reqData = {
      maker: safeSmartAccountAddress,
      channelId: safeSmartAccountAddress,
      title: filteredData.title,
      description: normalizeTextForFormData(filteredData.description),
      predictionDeadline: filteredData.predictionDeadline * 1000,
      resultConfirmDeadline: filteredData.resultConfirmDeadline * 1000,
      broadcastURL: filteredData.broadcastURL,
      disputedPeriod: '30m',
      category: filteredData.category,
      collateralAmount: collateralAmountBigInt,
      outcomes: filteredData.outcomes,
      tags: filteredData.tags,
      referenceURL: filteredData.referenceURL,
      validationId: validationId,
    };

    await createMarketTransaction.mutateAsync({
      ...reqData,
      image: imageFile || undefined,
    });
  };

  return {
    // Form 관련만
    form,
    outcomes,
    onSubmit,
    addOutcome,
    removeOutcome,
    updateOutcome,
    handleEthicalReview,
    handleEthicalFailPopupClose,
    handleTitleChange,

    // External data
    collateral: currentCollateral,
    marketValidate,
    createMarketTransaction,

    // Refs
    addDepositButtonRef,
  };
}
