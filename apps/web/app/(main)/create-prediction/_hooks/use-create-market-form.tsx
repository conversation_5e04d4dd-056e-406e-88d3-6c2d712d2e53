import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useRef } from 'react';
import { toast } from '@repo/ui/components/sonner';
import { useChannelCollateral } from '@/hooks/query/channel';
import { useCreateMarketFlow, useMarketValidate } from '@/hooks/query/market';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import { useGlobalStore } from '@/store/global.store';
import { normalizeTextForFormData, toAmount } from '@/lib/format';
import InsufficientDepositBalance from '@/components/ui/popup/insufficient-deposit-balance';
import {
  CreateMarketFormSchema,
  CreateMarketFormValues,
  isForbiddenTag,
  isForbiddenOutcome,
  FORBIDDEN_TAGS,
} from '../types';
import { useCreatePredictionStore } from '../_store/create-prediction.store';
import { TOAST_MESSAGE } from '@/lib/constants';

export function useCreateMarketForm() {
  const { data: currentCollateral } = useChannelCollateral();
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  const marketValidate = useMarketValidate();
  const createMarketTransaction = useCreateMarketFlow();
  const { openPopup, closePopup } = usePopupStore();

  const store = useCreatePredictionStore();

  const form = useForm<CreateMarketFormValues>({
    resolver: zodResolver(CreateMarketFormSchema),

    defaultValues: {
      title: '',
      description: '',
      imageUrl: '',
      predictionDeadline: 0,
      resultConfirmDeadline: 0,
      category: '',
      collateralAmount: undefined,
      outcomes: ['', ''],
      tags: [],
      broadcastURL: '',
      referenceURL: '',
    },
  });
  const { watch, setValue } = form;
  const outcomes = watch('outcomes');

  const addDepositButtonRef = useRef<HTMLButtonElement>(null);

  // Sync form tags with store tags automaticall
  useEffect(() => {
    setValue('tags', store.tags);
  }, [store.tags, setValue]);

  const addTag = () => {
    store.addTag();
  };

  const removeTag = (tagToRemove: string) => {
    store.removeTag(tagToRemove);
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    store.handleTagInputChange(e.target.value);
  };

  // Outcomes management with store integration
  const addOutcome = () => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length < 10) {
      setValue('outcomes', [...currentOutcomes, '']);
      store.setDuplicateOutcomes([]);
    }
  };

  const removeOutcome = (index: number) => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length > 2) {
      const newOutcomes = currentOutcomes.filter((_, i) => i !== index);
      setValue('outcomes', newOutcomes);
      store.validateOutcomes(newOutcomes);
    }
  };

  const updateOutcome = (index: number, value: string) => {
    const currentOutcomes = watch('outcomes');
    const newOutcomes = [...currentOutcomes];
    newOutcomes[index] = value;
    setValue('outcomes', newOutcomes);
    store.validateOutcomes(newOutcomes);
  };

  const handleEthicalReview = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const title = form.getValues('title');
    if (!title.trim()) {
      toast.error('Please enter a title.');
      return;
    }

    try {
      store.setTitleEthicalReviewFailed(false);
      store.setShowEthicalFailPopup(false);

      const validationResult = await marketValidate.mutateAsync({
        title: title,
      });

      if (!validationResult.isEthical) {
        store.setTitleEthicalReviewFailed(true);
        store.setShowEthicalFailPopup(true);
        // store.setReason(validationResult.reason);
        return;
      }

      if (!validationResult.validationId) {
        toast.error('Failed to get validation ID.');
        store.setTitleEthicalReviewFailed(true);
        return;
      }

      store.setValidationId(validationResult.validationId);
      toast.success(TOAST_MESSAGE.ETHICAL_REVIEW_SUCCESS);
    } catch (error) {
      console.error('Ethical review failed:', error);
      store.setTitleEthicalReviewFailed(true);
    }
  };

  const handleEthicalFailPopupClose = () => {
    store.setShowEthicalFailPopup(false);
    store.setTitleEthicalReviewFailed(true);
    store.setValidationId(null);
  };

  const handleTitleChange = (value: string) => {
    if (store.titleEthicalReviewFailed) {
      store.setTitleEthicalReviewFailed(false);
    }
    if (store.validationId) {
      store.setValidationId(null);
    }
  };

  const onSubmit = async (values: CreateMarketFormValues) => {
    if (!safeSmartAccountAddress) {
      return;
    }

    store.validateOutcomes(values.outcomes);

    if (store.duplicateOutcomes.length > 0) {
      toast.error(TOAST_MESSAGE.DUPLICATE_OUTCOME);
      return;
    }

    // Check for forbidden outcomes
    const forbiddenOutcomes = values.outcomes.filter(
      outcome => outcome.trim() !== '' && isForbiddenOutcome(outcome.trim())
    );

    if (forbiddenOutcomes.length > 0) {
      toast.error(TOAST_MESSAGE.FORBIDDEN_OUTCOME);
      return;
    }

    if (values.tags?.some(tag => isForbiddenTag(tag))) {
      toast.error(
        'Forbidden tags detected. Please remove forbidden tags: ' + FORBIDDEN_TAGS.join(', ')
      );
      return;
    }

    if (values.collateralAmount < 50) {
      openPopup(
        <InsufficientDepositBalance
          requiredAmount="$50"
          onAddDeposit={() => {
            closePopup();

            if (addDepositButtonRef.current) {
              addDepositButtonRef.current.click();
            }
          }}
          onCancel={() => {
            closePopup();
          }}
        />
      );
      return;
    }

    // Safe number parsing with validation
    const maxAllowedAmount = currentCollateral?.available.formatted
      ? (() => {
          const parsed = parseFloat(currentCollateral.available.formatted);
          return isNaN(parsed) ? 0 : parsed;
        })()
      : 0;

    if (values.collateralAmount > maxAllowedAmount) {
      toast.error(
        `The collateral amount cannot exceed available collateral (${maxAllowedAmount.toLocaleString()}).`
      );
      return;
    }

    if (!store.validationId) {
      toast.error('Get the ethical review first.');
      return;
    }

    const filteredData = {
      ...values,
      outcomes: values.outcomes.filter(outcome => outcome.trim() !== ''),
      tags: values.tags?.filter(tag => tag.trim() !== '') || [],
    };

    const collateralAmountBigInt = BigInt(toAmount(filteredData.collateralAmount));
    const reqData = {
      maker: safeSmartAccountAddress,
      channelId: safeSmartAccountAddress,
      title: filteredData.title,
      description: normalizeTextForFormData(filteredData.description),
      predictionDeadline: filteredData.predictionDeadline * 1000,
      resultConfirmDeadline: filteredData.resultConfirmDeadline * 1000,
      broadcastURL: filteredData.broadcastURL,
      disputedPeriod: '30m',
      category: filteredData.category,
      collateralAmount: collateralAmountBigInt,
      outcomes: filteredData.outcomes,
      tags: filteredData.tags,
      referenceURL: filteredData.referenceURL,
      validationId: store.validationId,
    };

    await createMarketTransaction.mutateAsync({
      ...reqData,
      image: store.imageFile || undefined,
    });
  };

  return {
    // Form
    form,
    outcomes,
    onSubmit,

    ...store,

    addTag,
    removeTag,
    handleTagKeyPress,
    handleTagInputChange,
    addOutcome,
    removeOutcome,
    updateOutcome,
    handleEthicalReview,
    handleEthicalFailPopupClose,
    handleTitleChange,

    // External data
    collateral: currentCollateral,
    marketValidate,
    createMarketTransaction,

    // Refs for DOM interaction
    addDepositButtonRef,
  };
}
