import { useCallback } from 'react';
import { CreatePredictionAction } from '../_context/types';

export const useImageUpload = (
  dispatch: React.Dispatch<CreatePredictionAction>
) => {
  const handleImageFileSelect = useCallback((file: File | null) => {
    dispatch({ type: 'SET_IMAGE_FILE', payload: file });

    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        dispatch({ type: 'SET_IMAGE_PREVIEW', payload: result });
      };
      reader.readAsDataURL(file);
    } else {
      dispatch({ type: 'SET_IMAGE_PREVIEW', payload: '' });
    }
  }, [dispatch]);

  return {
    handleImageFileSelect,
  };
};
