import { useCallback } from 'react';
import { CreatePredictionAction } from '../_context/types';

export const usePopupState = (
  dispatch: React.Dispatch<CreatePredictionAction>
) => {
  const setShowEthicalFailPopup = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SHOW_ETHICAL_FAIL_POPUP', payload: show });
  }, [dispatch]);

  const setShowExamplePopup = useCallback((show: boolean) => {
    dispatch({ type: 'SET_SHOW_EXAMPLE_POPUP', payload: show });
  }, [dispatch]);

  return {
    setShowEthicalFailPopup,
    setShowExamplePopup,
  };
};
