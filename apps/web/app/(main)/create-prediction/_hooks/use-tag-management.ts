import { useCallback } from 'react';
import { CreatePredictionAction } from '../_context/types';
import { isForbiddenTag } from '../types';

export const useTagManagement = (
  tags: string[],
  currentTag: string,
  tagError: string,
  dispatch: React.Dispatch<CreatePredictionAction>
) => {
  const addTag = useCallback(() => {
    if (!currentTag.trim()) return;

    if (tags.length >= 3) {
      dispatch({ type: 'SET_TAG_ERROR', payload: 'Maximum 3 tags allowed' });
      return;
    }

    if (tags.includes(currentTag.trim())) {
      dispatch({ type: 'SET_TAG_ERROR', payload: 'Tag already exists' });
      return;
    }

    if (isForbiddenTag(currentTag.trim())) {
      dispatch({
        type: 'SET_TAG_ERROR',
        payload: `'${currentTag.trim()}' is a forbidden tag. Please use a different tag.`,
      });
      return;
    }

    dispatch({ type: 'ADD_TAG', payload: currentTag.trim() });
  }, [currentTag, tags, dispatch]);

  const removeTag = useCallback(
    (tag: string) => {
      dispatch({ type: 'REMOVE_TAG', payload: tag });
    },
    [dispatch]
  );

  const handleTagInputChange = useCallback(
    (value: string) => {
      dispatch({ type: 'SET_CURRENT_TAG', payload: value });

      if (value.trim() && isForbiddenTag(value.trim())) {
        dispatch({
          type: 'SET_TAG_ERROR',
          payload: `'${value.trim()}' is a forbidden tag. Please use a different tag.`,
        });
      } else {
        dispatch({ type: 'SET_TAG_ERROR', payload: '' });
      }
    },
    [dispatch]
  );

  const handleTagKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        addTag();
      }
    },
    [addTag]
  );

  return {
    addTag,
    removeTag,
    handleTagInputChange,
    handleTagKeyPress,
  };
};
