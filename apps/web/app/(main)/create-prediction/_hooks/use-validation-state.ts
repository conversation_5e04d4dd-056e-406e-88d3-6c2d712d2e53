import { useCallback } from 'react';
import { CreatePredictionAction } from '../_context/types';

export const useValidationState = (
  dispatch: React.Dispatch<CreatePredictionAction>
) => {
  const setValidationId = useCallback((id: string | null) => {
    dispatch({ type: 'SET_VALIDATION_ID', payload: id });
  }, [dispatch]);

  const setTitleEthicalReviewFailed = useCallback((failed: boolean) => {
    dispatch({ type: 'SET_TITLE_ETHICAL_REVIEW_FAILED', payload: failed });
  }, [dispatch]);

  const setDuplicateOutcomes = useCallback((outcomes: number[]) => {
    dispatch({ type: 'SET_DUPLICATE_OUTCOMES', payload: outcomes });
  }, [dispatch]);

  return {
    setValidationId,
    setTitleEthicalReviewFailed,
    setDuplicateOutcomes,
  };
};
