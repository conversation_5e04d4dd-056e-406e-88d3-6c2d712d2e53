import { create } from 'zustand';
import { combine } from 'zustand/middleware';
import { isForbiddenTag } from '../types';

const initialState = {
  // Tags state
  tags: [] as string[],
  currentTag: '',
  tagError: '',

  // Image state
  imageFile: null as File | null,
  imagePreview: '',

  // Validation state
  validationId: null as string | null,
  titleEthicalReviewFailed: false,

  // Popup states
  showEthicalFailPopup: false,
  showExamplePopup: false,

  // Outcomes state
  duplicateOutcomes: [] as number[],

  // Terms state
  isTermsAccepted: false,
};

export const useCreatePredictionStore = create(
  combine(initialState, (set, get) => ({
    // Tag actions
    setTags: (tags: string[]) => set({ tags }),
    setCurrentTag: (currentTag: string) => set({ currentTag }),
    setTagError: (tagError: string) => set({ tagError }),

    addTag: () => {
      const { currentTag, tags } = get();

      if (currentTag.trim() && tags.length < 3 && !tags.includes(currentTag.trim())) {
        if (isForbiddenTag(currentTag.trim())) {
          set({
            tagError: `'${currentTag.trim()}' is a forbidden tag. Please use a different tag.`,
          });
          return;
        }

        const newTags = [...tags, currentTag.trim()];
        set({
          tags: newTags,
          currentTag: '',
          tagError: '',
        });
      }
    },

    removeTag: (tagToRemove: string) => {
      const { tags } = get();
      const newTags = tags.filter(tag => tag !== tagToRemove);
      set({ tags: newTags });
    },

    handleTagInputChange: (value: string) => {
      set({ currentTag: value });

      if (value.trim() && isForbiddenTag(value.trim())) {
        set({
          tagError: `'${value.trim()}' is a forbidden tag. Please use a different tag.`,
        });
      } else {
        set({ tagError: '' });
      }
    },

    // Image actions
    setImageFile: (imageFile: File | null) => set({ imageFile }),
    setImagePreview: (imagePreview: string) => set({ imagePreview }),

    handleImageFileSelect: (file: File | null) => {
      set({ imageFile: file });

      if (file) {
        const reader = new FileReader();
        const controller = new AbortController();

        reader.onload = e => {
          if (!controller.signal.aborted && e.target?.result) {
            const result = e.target.result;
            if (typeof result === 'string') {
              set({ imagePreview: result });
            }
          }
        };

        reader.onerror = () => {
          if (!controller.signal.aborted) {
            console.error('Failed to read file');
            set({ imagePreview: '' });
          }
        };

        reader.onabort = () => {
          console.log('File reading aborted');
        };

        try {
          reader.readAsDataURL(file);
        } catch (error) {
          console.error('Error reading file:', error);
          set({ imagePreview: '' });
        }

        return () => {
          controller.abort();
          if (reader.readyState === FileReader.LOADING) {
            reader.abort();
          }
        };
      } else {
        set({ imagePreview: '' });
      }
    },

    // Validation actions
    setValidationId: (validationId: string | null) => set({ validationId }),
    setTitleEthicalReviewFailed: (titleEthicalReviewFailed: boolean) =>
      set({ titleEthicalReviewFailed: titleEthicalReviewFailed }),

    resetValidation: () =>
      set({
        validationId: null,
        titleEthicalReviewFailed: false,
      }),

    // Popup actions
    setShowEthicalFailPopup: (showEthicalFailPopup: boolean) => set({ showEthicalFailPopup }),
    setShowExamplePopup: (showExamplePopup: boolean) => set({ showExamplePopup }),

    // Outcomes actions
    setDuplicateOutcomes: (duplicateOutcomes: number[]) => set({ duplicateOutcomes }),
    validateOutcomes: (outcomes: string[]) => {
      const trimmedOutcomes = outcomes
        .map((outcome, idx) => ({ value: outcome.trim().toLowerCase(), index: idx }))
        .filter(item => item.value !== '');

      const duplicateIndexes: number[] = [];
      const seen = new Map<string, number[]>();

      trimmedOutcomes.forEach(item => {
        if (seen.has(item.value)) {
          seen.get(item.value)!.push(item.index);
        } else {
          seen.set(item.value, [item.index]);
        }
      });

      seen.forEach(indexes => {
        if (indexes.length > 1) {
          duplicateIndexes.push(...indexes);
        }
      });

      set({ duplicateOutcomes: duplicateIndexes });
    },

    // Terms actions
    setIsTermsAccepted: (isTermsAccepted: boolean) => set({ isTermsAccepted }),
    // Reset store
    resetStore: () => set(initialState),
  }))
);
