'use client';

import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { Popup } from '@/components/ui/popup';
import EthicalValidationFailPopup from '@/components/ui/popup/ethical-validation-fail-popup';
import ExamplePopup from '@/components/ui/popup/example-popup';
import { fillDevData } from '@/lib/utils/dev-data';
import { Form } from '@repo/ui/components/form';
import { pxToRem } from '@repo/ui/lib/utils';
import { useRouter } from 'next/navigation';
import { CategoryTagsSection } from './_components/category-tags-section';
import { DatetimeSection } from './_components/datetime-section';
import { DepositSection } from './_components/deposit-section';
import { MarketImageSection } from './_components/market-image-section';
import { QuestionSection } from './_components/quest-section';
import { TermsSubmitSection } from './_components/terms-submit-section';
import { UrlsDescriptionSection } from './_components/urls-description-section';
import { useCreateMarketForm } from './_hooks/use-create-market-form';
import { useCreatePredictionStore } from './_store/create-prediction.store';

export default function CreatePredictionRefactoredPage() {
  const router = useRouter();

  const {
    form,
    outcomes,
    onSubmit,
    collateral,
    marketValidate,
    createMarketTransaction,
    addTag,
    handleTagKeyPress,
    addOutcome,
    removeOutcome,
    updateOutcome,
    handleEthicalReview,
    handleEthicalFailPopupClose,
    handleTitleChange,
    addDepositButtonRef,
  } = useCreateMarketForm();

  const {
    showEthicalFailPopup,
    showExamplePopup,
    setShowExamplePopup,
    resetStore,
    isTermsAccepted,
  } = useCreatePredictionStore();

  const handleFillDevData = () => {
    fillDevData(form, resetStore);
  };

  return (
    <div className="page">
      <div className="mt-space-10">
        <button
          type="button"
          onClick={() => router.back()}
          className="flex items-center gap-2 text-gray-700 transition-colors hover:text-gray-900"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.8611 15L6 10M6 10L10.8611 5M6 10H18M2 2V18"
              stroke="black"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="font-size-lg text-mid-dark font-bold">Create Prediction</span>
        </button>
      </div>

      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 flex justify-end">
          <BaseButton
            type="button"
            variant="outline"
            size="sm"
            onClick={handleFillDevData}
            className="border-yellow-300 bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
          >
            🎲 Fill Random Data
          </BaseButton>
        </div>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex w-full flex-col">
          <MarketImageSection />

          <section data-role="section" className="gap-space-30 flex items-start">
            <DepositSection
              control={form.control}
              collateral={collateral}
              addDepositButtonRef={addDepositButtonRef}
            />

            <CategoryTagsSection
              control={form.control}
              onTagKeyPress={handleTagKeyPress}
              onAddTag={addTag}
            />
          </section>

          <QuestionSection
            control={form.control}
            outcomes={outcomes}
            marketValidateIsPending={marketValidate.isPending}
            onTitleChange={handleTitleChange}
            onEthicalReview={handleEthicalReview}
            onUpdateOutcome={updateOutcome}
            onAddOutcome={addOutcome}
            onRemoveOutcome={removeOutcome}
          />

          <UrlsDescriptionSection control={form.control} />
          <DatetimeSection control={form.control} getValues={form.getValues} />
          <TermsSubmitSection />

          <div className="mt-8 flex space-x-4">
            <InfoButton
              type="submit"
              size="lg"
              fontSize="base"
              width={pxToRem(256)}
              disabled={createMarketTransaction.isPending || !isTermsAccepted}
            >
              {createMarketTransaction.isPending ? 'Creating...' : 'Create Prediction'}
            </InfoButton>
          </div>
        </form>
      </Form>

      {showEthicalFailPopup && (
        <Popup isOpen={showEthicalFailPopup} onClose={handleEthicalFailPopupClose}>
          <EthicalValidationFailPopup onEditQuestion={handleEthicalFailPopupClose} />
        </Popup>
      )}

      {showExamplePopup && (
        <ExamplePopup isOpen={showExamplePopup} onClose={() => setShowExamplePopup(false)} />
      )}
    </div>
  );
}
