import { z } from 'zod';

export const CreateMarketFormSchema = z
  .object({
    collateralAmount: z.number().min(0.01, 'Please enter a deposit amount'),
    title: z
      .string()
      .min(2, 'Title must be at least 2 characters long')
      .max(100, 'Title cannot exceed 100 characters'),
    description: z
      .string()
      .min(2, 'Description must be at least 2 characters long')
      .max(2000, 'Description cannot exceed 2000 characters'),
    imageUrl: z.string().optional(),
    predictionDeadline: z.number().min(1, 'Please set a prediction end time'),
    resultConfirmDeadline: z.number().min(1, 'Please set a result confirmation end time'),
    category: z.string().min(2, 'Please select a category').max(50, 'Category name is too long'),
    outcomes: z
      .array(
        z.string().min(1, 'Outcome cannot be empty').max(50, 'Outcome cannot exceed 50 characters')
      )
      .min(2, 'At least 2 outcomes are required')
      .max(10, 'Maximum 10 outcomes allowed'),
    tags: z
      .array(z.string().min(1, 'Tag cannot be empty').max(20, 'Tag cannot exceed 20 characters'))
      .max(3, 'Maximum 3 tags allowed')
      .optional(),
    broadcastURL: z.string().url('Please enter a valid URL format').optional().or(z.literal('')),
    referenceURL: z
      .string()
      .min(1, 'Reference URL is required')
      .url('Please enter a valid URL format'),
    image: z.instanceof(File).optional(),
  })
  .refine(
    data => {
      return data.resultConfirmDeadline > data.predictionDeadline;
    },
    {
      message: 'Result confirmation end time must be later than prediction end time',
      path: ['resultConfirmDeadline'],
    }
  );

export type CreateMarketFormValues = z.infer<typeof CreateMarketFormSchema>;

export const PLACEHOLDER_TEXT = {
  deposit: 'Minimum Amount $50',
  category: 'Select Category',
  tag: 'Please create tag(max. 20 characters)',
  'market-title': 'Write a prediction title.(max. 100 characters)',
  'market-outcomes': 'Write an outcome.(max. 50 characters)',
  'broadcast-url':
    'Please enter the URL of a live video related to the prediction.(Youtube, Twitch)',
  'reference-url': 'Please enter a URL related to the prediction.',
  description:
    'Please write in detail about the explanation of predictions and the rules by which the prediction is resolved.',
  'prediction-end-time': 'YYYY-MM-DD  hh:mm',
  'confirmation-end-time': 'YYYY-MM-DD  hh:mm',
  'dispute-period': '',
} as const;

export const FORBIDDEN_TAGS = ['all', 'new'];

export const isForbiddenTag = (tag: string): boolean => {
  return FORBIDDEN_TAGS.includes(tag.toLowerCase().trim());
};

export const FORBIDDEN_OUTCOMES = ['void'];

export const isForbiddenOutcome = (outcome: string): boolean => {
  return FORBIDDEN_OUTCOMES.includes(outcome.toLowerCase().trim());
};

export const formatDateTimeLocal = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const year = String(date.getFullYear());
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};
