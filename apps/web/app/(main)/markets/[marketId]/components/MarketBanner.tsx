import { cn } from '@repo/ui/lib/utils';

interface MarketBannerProps {
  imageUrl?: string;
  className?: string;
}

export default function MarketBanner({ imageUrl, className }: MarketBannerProps) {
  return (
    <div
      className={cn(
        'bg-gray-2 relative flex h-[450px] w-full items-center justify-center overflow-hidden',
        className
      )}
    >
      {imageUrl ? (
        <img src={imageUrl} alt="Market banner" className="w-full object-cover object-center" />
      ) : (
        <div className="text-gray-3 flex h-full w-full items-center justify-center">
          No banner image
        </div>
      )}
    </div>
  );
}
