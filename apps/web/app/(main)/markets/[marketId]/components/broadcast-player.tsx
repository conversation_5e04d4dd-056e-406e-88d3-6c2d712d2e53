import { env } from '@/lib/env';

// Constants
const IFRAME_HEIGHT = 480;
const YOUTUBE_EMBED_BASE_URL = 'https://www.youtube.com/embed';
const TWITCH_EMBED_BASE_URL = 'https://player.twitch.tv';

// Types
interface BroadcastPlayerProps {
  url: string;
}

type BroadcastPlatform = 'twitch' | 'youtube';

interface BroadcastConfig {
  type: BroadcastPlatform;
  embedUrl: string;
}

// Environment utilities
const getParentDomain = (): string => {
  const envMap: Record<string, string> = {
    development: 'localhost',
    dq: 'dq.predictgo.io',
    live: 'predictgo.io',
  };

  const currentEnv = process.env.NODE_ENV === 'development' ? 'development' : env.NEXT_PUBLIC_ENV;

  const domain = envMap[currentEnv];
  if (!domain) {
    throw new Error(`Invalid environment: ${currentEnv}`);
  }

  return domain;
};

// URL parsing utilities
const parseYouTubeVideoId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    return urlObj.searchParams.get('v');
  } catch {
    return null;
  }
};

const parseTwitchChannelName = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    const pathSegments = urlObj.pathname.split('/').filter(Boolean);
    return pathSegments[0] || null;
  } catch {
    return null;
  }
};

// URL builders
const buildYouTubeEmbedUrl = (videoId: string): string => {
  return `${YOUTUBE_EMBED_BASE_URL}/${videoId}?autoplay=1`;
};

const buildTwitchEmbedUrl = (channelName: string): string => {
  const parentDomain = getParentDomain();
  return `${TWITCH_EMBED_BASE_URL}?channel=${channelName}&parent=${parentDomain}`;
};

// Platform detection
const detectBroadcastPlatform = (url: string): BroadcastPlatform | null => {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube';
  }
  if (url.includes('twitch.tv')) {
    return 'twitch';
  }
  return null;
};

// Broadcast configuration factory
const createBroadcastConfig = (url: string): BroadcastConfig | null => {
  const platform = detectBroadcastPlatform(url);

  if (!platform) {
    return null;
  }

  switch (platform) {
    case 'youtube': {
      const videoId = parseYouTubeVideoId(url);
      if (!videoId) return null;

      return {
        type: 'youtube',
        embedUrl: buildYouTubeEmbedUrl(videoId),
      };
    }

    case 'twitch': {
      const channelName = parseTwitchChannelName(url);
      if (!channelName) return null;

      return {
        type: 'twitch',
        embedUrl: buildTwitchEmbedUrl(channelName),
      };
    }

    default:
      return null;
  }
};

// Iframe component factories
const createTwitchIframe = (src: string) => (
  <iframe
    src={src}
    frameBorder="0"
    allowFullScreen
    scrolling="no"
    height={IFRAME_HEIGHT}
    width="100%"
    title="Twitch stream player"
  />
);

const createYouTubeIframe = (src: string) => (
  <iframe
    src={src}
    height={IFRAME_HEIGHT}
    width="100%"
    title="YouTube video player"
    frameBorder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    referrerPolicy="strict-origin-when-cross-origin"
    allowFullScreen
  />
);

// Main component
export default function BroadcastPlayer({ url }: BroadcastPlayerProps) {
  const broadcastConfig = createBroadcastConfig(url);

  if (!broadcastConfig) {
    return null;
  }

  const renderPlayer = () => {
    switch (broadcastConfig.type) {
      case 'twitch':
        return createTwitchIframe(broadcastConfig.embedUrl);
      case 'youtube':
        return createYouTubeIframe(broadcastConfig.embedUrl);
      default:
        throw new Error(`Unsupported broadcast type: ${broadcastConfig.type}`);
    }
  };

  return <div className="pb-space-20">{renderPlayer()}</div>;
}
