import DisputeButton from '@/components/actions/dispute-button';
import MarketCountdown from '@/components/common/market-countdown';
import { XlIcon } from '@/components/icons/xl-icon';
import { MarketProps } from '@/hooks/query/market/market.mapper';
import { Skeleton } from '@repo/ui/components/skeleton';

interface DisputeControlPanelProps {
  marketData: MarketProps;
}

export default function DisputeControlPanel({ marketData }: DisputeControlPanelProps) {
  const proposedOutcome = marketData.marketProposedOutcome;
  const disputeAmountConfirmedFormatted = marketData.marketDisputeAmountConfirmed.formatted;
  const disputedAmountFormatted = `$${marketData.marketDisputedAmount.formatted}`;

  const ratio = marketData.marketDisputedAmount.raw.dividedBy(
    marketData.marketDisputeAmountConfirmed.raw
  );
  const ratioFormatted = ratio.multipliedBy(100).toFixed(2);

  return (
    <div className="flex flex-col">
      <h1 className="pb-space-30 dashboard-h2">Prediction Closed</h1>

      {/* Outcome proposed section */}
      <div className="mb-space-20 flex flex-col items-center">
        <div className="mb-space-15 gap-space-30 flex flex-col items-center">
          <XlIcon name="check" size={50} className="mr-space-10" />
          <div className="text-size-sm font-semibold">Outcome proposed</div>
        </div>
        <div className="flex items-center justify-between">
          <div className="text-size-sm font-semibold">{proposedOutcome}</div>
        </div>
      </div>

      {/* Dispute section */}
      <div className="flex flex-col">
        <p className="mb-space-15 text-size-sm text-gray-3">
          Is it different from the proposed outcome?
        </p>

        {/* Progress bar */}
        <div className="my-space-15 bg-gray-1 relative h-2 rounded-full">
          <div
            className="bg-sky absolute top-0 left-0 h-full rounded-full"
            style={{ width: `${ratioFormatted}%` }}
          ></div>
          {/* Disputed amount positioned above the progress bar width */}
          <div
            className="text-size-xs absolute"
            style={{ left: `${ratioFormatted}%`, transform: 'translateX(-50%)', bottom: '10px' }}
          >
            <span className="text-sky font-semibold">{disputedAmountFormatted}</span>
          </div>
        </div>

        <div className="mb-space-15 flex justify-end">
          <span className="text-gray-3 text-[10px]">
            Dispute Confirmed
            <span className="text-mid-dark text-size-xs font-semibold">
              {' '}
              ${disputeAmountConfirmedFormatted}
            </span>
          </span>
        </div>
      </div>

      {/* Open Dispute Button */}
      <div className="mb-space-15">
        <DisputeButton
          disputeAmountBase={marketData.marketDisputeAmountBaseFormatted}
          maxAmount={marketData.marketDisputeAmount}
          marketId={marketData.marketId}
          size="xl2"
          textSize="base"
        />
      </div>

      {/* Time Remaining */}
      <div className="mb-space-15 gap-space-8 flex items-center justify-end">
        {marketData.marketNextDeadline ? (
          <MarketCountdown endTime={marketData.marketNextDeadline} />
        ) : (
          <Skeleton className="h-5 w-24" />
        )}
      </div>
    </div>
  );
}
