import { BaseButton } from '@/components/ui/base.button';
import type { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import type { MarketOutcome } from '@/lib/api/market/market.transform';
import { calculatePercentage, getPercentage } from '@/lib/utils';
import { pxToRem } from '@repo/ui/lib/utils';

interface MarketOutcomesProps {
  totalVolume: string;
  outcomes: MarketOutcome[];
  selectedOutcome?: MarketOutcome | null;
  handleClickOutcome: (outcome: MarketOutcome) => void;
  marketStatus: MarketStatusEnum;
}

const COLORS = [
  '--color-graph-1',
  '--color-graph-2',
  '--color-graph-3',
  '--color-graph-4',
  '--color-graph-5',
  '--color-graph-6',
  '--color-graph-7',
  '--color-graph-8',
  '--color-graph-9',
  '--color-graph-10',
  '--color-graph-11',
];

export default function MarketOutcomes({
  totalVolume,
  outcomes,
  selectedOutcome,
  handleClickOutcome: handleClick,
  marketStatus,
}: MarketOutcomesProps) {
  const isLive = marketStatus === 'OPEN';
  return (
    <div className="gap-space-20 flex flex-col">
      {outcomes.map(outcome => {
        const percentageDisplay = getPercentage(outcome.volume.formatted, totalVolume);
        const progressBarWidth =
          calculatePercentage(outcome.volume.formatted, totalVolume).toFixed(2) + '%';
        return (
          <div key={outcome.outcome} className="gap-space-30 flex items-center">
            <div className="gap-space-10 flex flex-col">
              <div className="text-mid-dark text-size-sm max-w-[300px] truncate font-semibold">
                {outcome.outcome}
              </div>
              <div className="text-size-xs flex gap-1">
                <span className="max-w-[300px] truncate text-black">
                  ${outcome.volume.formatted}{' '}
                </span>
                <span className="text-gray-3">Vol.</span>
              </div>
            </div>

            <div className="relative h-2 w-full overflow-hidden rounded-sm bg-gray-100">
              <div
                className="absolute top-0 left-0 h-full rounded-sm"
                style={{
                  width: progressBarWidth,
                  backgroundColor: `var(${COLORS[outcome.order]})`,
                }}
              />
            </div>

            <div>
              <span className="text-size-sm text-icon-dark font-semibold">
                {percentageDisplay ? `${percentageDisplay}` : '0.00%'}
              </span>
            </div>
            {isLive && (
              <div>
                <BaseButton
                  style={{
                    fontSize: 'var(--text-size-sm)',
                    width: pxToRem(160),
                  }}
                  onClick={() => handleClick(outcome)}
                  variant="yes"
                  className="px-space-6 rounded-round-sm font-semibold"
                  size="lg"
                  active={selectedOutcome?.outcome === outcome.outcome}
                >
                  Predict
                </BaseButton>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
