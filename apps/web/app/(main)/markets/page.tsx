'use client';

import EmptyMarketList from '@/components/common/empty-list';
import { ErrorBoundary } from '@/components/common/error-boundary';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import { BaseButton } from '@/components/ui/base.button';
import { useCategoryStats } from '@/hooks/query/category/use-category-stats';
import { useMarketsByCategory } from '@/hooks/query/market/use-markets-by-category';
import { GetMarketsOrderEnum } from '@/lib/api/market/market.schema.server';
import { useGlobalStore } from '@/store/global.store';
import { pxToRem } from '@repo/ui/lib/utils';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import MarketViewControl from './market-view-control';
import { updateSearchParams } from '@/lib/utils';

const limit = 50;

function MarketsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const isAsideOpen = useGlobalStore(state => state.isAsideOpen);
  const category = searchParams.get('category');
  const { data: categoryStats } = useCategoryStats(category || '');
  const [currentPage, setCurrentPage] = useState(0);
  const order = (searchParams.get('order') as GetMarketsOrderEnum) || 'VOLUME';

  const {
    data,
    isLoading: categoryLoading,
    isFetching,
    error,
    isError,
  } = useMarketsByCategory(category || '', {
    page: currentPage,
    limit,
    order,
    filter: 'ALL',
  });

  // 에러 발생 시 Error Boundary가 catch할 수 있도록 throw
  useEffect(() => {
    if (isError && error) {
      throw error;
    }
  }, [isError, error]);

  useEffect(() => {
    setCurrentPage(0);
  }, [category]);

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
  };

  const hasMoreData = data && data.markets && data.markets.length === limit;

  useEffect(() => {
    if (!category) {
      router.replace('/');
    }
  }, [category, router]);

  return (
    <div
      data-page="category-markets"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className={`relative flex ${isAsideOpen ? 'pr-(--aside-width)' : 'pr-0'}`}
    >
      <section className="min-w-0 flex-1">
        <header className="p-space-30 pb-space-30">
          <h1 className="text-size-lg text-mid-dark font-bold">{category} Markets</h1>
          {categoryStats && (
            <div className="text-mid-dark mt-3 flex gap-6 text-sm">
              <div className="flex items-center gap-1">
                <span className="text-gray-3">Volume</span>
                <span className="text-primary font-medium">
                  ${categoryStats.totalVolumeFormatted}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-gray-3">Live Markets</span>
                <span className="text-primary font-medium">{categoryStats.liveMarketCount}</span>
              </div>
            </div>
          )}
        </header>

        <div className="flex-1">
          <div className="px-[calc(30px-var(--market-card-border-width))] py-10">
            {categoryLoading || !data ? (
              <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="relative w-full">
                    <PredictionMarketCardSkeleton />
                    <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                  </div>
                ))}
              </div>
            ) : data.markets.length > 0 ? (
              <>
                <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                  {data.markets.map((item, index) => (
                    <div key={item.marketId || index} className="relative w-full">
                      <PredictionMarketCard item={item} />
                      <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                    </div>
                  ))}
                </div>

                {/* Load More 버튼 */}
                {hasMoreData && (
                  <div className="flex justify-center pt-10">
                    <BaseButton
                      variant="default"
                      onClick={handleLoadMore}
                      disabled={isFetching}
                      className="px-12 py-3"
                    >
                      {isFetching ? 'Loading...' : 'Load More'}
                    </BaseButton>
                  </div>
                )}
              </>
            ) : (
              <div className="mt-space-60">
                <EmptyMarketList />
              </div>
            )}
          </div>
        </div>
      </section>
      <aside
        className={`border-l-line fixed top-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white pt-[100px] ${
          isAsideOpen ? 'right-0' : 'hidden'
        }`}
      >
        <MarketViewControl
          category={category}
          selectedStatus="ALL"
          sortBy={order}
          onSortChange={order => {
            updateSearchParams({ order }, router, searchParams, pathname);
          }}
          onStatusChange={() => {}}
        />
      </aside>
    </div>
  );
}

export default function MarketsPage() {
  return (
    <ErrorBoundary className="h-full">
      <MarketsContent />
    </ErrorBoundary>
  );
}
