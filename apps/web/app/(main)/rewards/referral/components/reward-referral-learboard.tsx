import SvgIcon from '@/components/icons/svg-icon';
import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { useReferralLeaderboard } from '@/hooks/query/referral/use-referral-leaderboard';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { ICON_PATH, INNER_LINKS } from '@/lib/constants';
import { Skeleton } from '@repo/ui/components/skeleton';

interface RewardReferralLeaderboardProps {
  className?: string;
}

export default function RewardReferralLeaderboard({ className }: RewardReferralLeaderboardProps) {
  const { data: leaderboardData, isLoading } = useReferralLeaderboard();

  if (isLoading) {
    return (
      <section className="py-space-30">
        <h2 className="dashboard-h2 mb-space-20">Leaderboard</h2>
        <div className={cn('w-full', className)}>
          {/* 테이블 헤더 */}
          <div className="text-size-xs text-gray-3 border-b-line py-space-20 grid grid-cols-4 border-b">
            <div>Rank</div>
            <div>Predictor</div>
            <div className="text-center">Invitees</div>
            <div className="text-right">Referral Income</div>
          </div>

          <div className="divide-line divide-y">
            {/* 스켈레톤 로더 */}
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="py-space-15 grid grid-cols-4 items-center">
                <div className="w-8 text-center">
                  <Skeleton className="h-6 w-6 rounded-full" />
                </div>
                <div>
                  <Skeleton className="h-4 w-24" />
                </div>
                <div className="text-center">
                  <Skeleton className="mx-auto h-4 w-8" />
                </div>
                <div className="text-right">
                  <Skeleton className="ml-auto h-4 w-16" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  const showLeaderboard = leaderboardData && leaderboardData.length > 0;
  return (
    <section className="py-space-50">
      <h2 className="dashboard-h2 mb-space-20">Leaderboard</h2>
      <div className={cn('w-full', className)}>
        {/* 테이블 헤더 */}
        <div className="text-size-xs text-gray-3 border-b-line py-space-20 grid grid-cols-4 border-b">
          <div>Rank</div>
          <div>Predictor</div>
          <div className="text-center">Invitees</div>
          <div className="text-right">Referral Income</div>
        </div>

        <div className="divide-line divide-y">
          {showLeaderboard ? (
            leaderboardData.map((item, index) => {
              const rank = index + 1;

              const rankDisplay =
                rank === 1 ? (
                  <SvgIcon name="GoldTrophyIcon" />
                ) : rank === 2 ? (
                  <SvgIcon name="SilverTrophyIcon" />
                ) : rank === 3 ? (
                  <SvgIcon name="BronzeTrophyIcon" />
                ) : (
                  <span className="text-dark-deep text-size-xxs">{rank}</span>
                );

              return (
                <div
                  key={item.inviter}
                  className="hover:bg-gray-1 py-space-15 grid grid-cols-4 items-center transition-colors"
                >
                  <div className="w-8 text-center">{rankDisplay}</div>
                  <div className="text-size-sm text-mid-dark font-semibold">
                    <Link href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(item.address)}>
                      {item.inviter}
                    </Link>
                  </div>
                  <div className="text-size-sm text-mid-dark text-center font-semibold">
                    {item.invitees}
                  </div>
                  <div className="text-size-sm text-icon-dark text-right font-semibold">
                    ${item.referralIncome}
                  </div>
                </div>
              );
            })
          ) : (
            <section className="py-space-30 gap-space-30 flex h-[800px] flex-col items-center justify-center">
              <img src={ICON_PATH.ADD_FRIENDS} alt="Add Friends" />
              <p className="text-size-sm text-icon-dark">Invite your friends and earn rewards!</p>
              <Link href={INNER_LINKS.MAIN.REWARDS.REFERRAL}>
                <InfoButton className="px-space-30 py-space-15">Invite Friends</InfoButton>
              </Link>
            </section>
          )}
        </div>
      </div>
    </section>
  );
}
