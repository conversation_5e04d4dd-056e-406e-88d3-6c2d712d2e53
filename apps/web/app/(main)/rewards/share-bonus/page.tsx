'use client';

import DashboardBox from '@/components/common/dashboard-box';
import { BaseButton } from '@/components/ui/base.button';
import { EXTERNAL_LINKS } from '@/lib/constants';
import { pxToRem } from '@repo/ui/lib/utils';
import { useShareDashboardMain } from '@/hooks/query/share/use-share-dashboard';
import { Skeleton } from '@repo/ui/components/skeleton';
import { formatNumberAbbr } from '@repo/shared/utils/number-format';
import { useRouter } from 'next/navigation';
import { useAuthGuard } from '@/hooks/use-auth-guard';

export default function ShareBonusPage() {
  const router = useRouter();
  const { data: shareDashboardMain } = useShareDashboardMain();
  const { withAuthCheck } = useAuthGuard();

  const totalShareReward = shareDashboardMain?.totalShareReward.formatted;
  const totalShared = shareDashboardMain?.totalShared;

  const handleClickEarnExtraRewards = withAuthCheck(() => {
    router.push('/profile/share-bonus');
  });

  return (
    <div className="page">
      <section className="gap-space-60 border-b-line flex border-b pb-[70px]">
        <div className="flex w-[500px] flex-col items-center justify-center">
          <div className="flex flex-col items-center">
            <h1 className="text-sky mb-[20px] text-[40px] font-bold">Share Bonus Reward</h1>
            <p className="text-size-sm mb-[40px] text-center">
              Contribute to the platform&apos;s unlimited growth and earn <br /> additional rewards!{' '}
              <a
                rel="noreferrer"
                href={EXTERNAL_LINKS.SERVICE_DOCS_SHARE_BONUS}
                target="_blank"
                className="font-bold underline"
              >
                View Rules
              </a>
            </p>
            <img className="w-[300px]" src="/images/share-bonus-object.svg" />
          </div>
        </div>
        <div className="gap-space-30 flex flex-1 flex-col">
          <h2 className="text-size-xl text-dark font-bold">
            Share Bonus RewShare System : Automated Expansion Protocolard
          </h2>
          <div className="text-size-sm text-gray-3">
            <p>
              Our "Share" feature is not merely a marketing tool. It serves as an automated user
              acquisition (UA) marketing tool and an Expansion Protocol that will propel PredictGo
              along an exponential growth trajectory. As more users engage with the platform, the
              pace of this growth will accelerate increasingly.{' '}
            </p>
            <p>
              Building upon this foundation, we aim to develop a dynamic platform capable of
              limitless expansion.
            </p>
          </div>
          <div>
            <p>User : Share</p>
            <img src="/assets/images/share_bonus_object2.svg" />
          </div>
        </div>
      </section>
      <section className="gap-space-30 pt-space-50 flex flex-col">
        <h2 className="dashboard-h2">Dashboard</h2>
        <div className="gap-space-60 flex">
          <DashboardBox className="bg-gray-2 flex-1">
            <h3 className="text-size-sm text-mid-dark font-semibold">
              Total Distributed Share Reward
            </h3>
            {totalShareReward ? (
              <strong className="text-size-xl text-mid-dark font-bold">
                {formatNumberAbbr(totalShareReward)}
              </strong>
            ) : (
              <Skeleton className="h-[40px] w-full" />
            )}
          </DashboardBox>
          <DashboardBox className="flex-1" text-size-sm text-mid-dark font-semibold>
            <h3 className="text-size-sm text-mid-dark font-semibold">Total Shared</h3>
            {totalShared !== undefined ? (
              <strong className="text-size-xl text-mid-dark font-bold">{totalShared}</strong>
            ) : (
              <Skeleton className="h-[40px] w-full" />
            )}
          </DashboardBox>
        </div>
        <div className="gap-space-30 flex items-center justify-end">
          <p className="text-size-sm text-gray-3">
            Go to the Sharing page in the <span className="text-mid-dark">User Dashboard</span>{' '}
          </p>
          <BaseButton
            style={{
              width: pxToRem(180),
            }}
            size="sm2"
            variant="info"
            onClick={handleClickEarnExtraRewards}
          >
            Earn Extra Rewards
          </BaseButton>
        </div>
      </section>
    </div>
  );
}
