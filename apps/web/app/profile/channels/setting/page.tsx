'use client';

import SvgIcon from '@/components/icons/svg-icon';
import PageContainer from '@/components/layouts/page-container';
import { BaseButton, DarkButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { TextareaWithCharacterCount } from '@/components/ui/textarea-with-character-count';
import { toast } from '@/components/ui/base.toast';
import { useMyChannel, useUpdateChannel } from '@/hooks/query/channel';
import { ChannelSnsType, UpdateChannelRequestBody } from '@/lib/api/channel/channel.schema.server';
import { DEFAULT_MARKET_AVATAR_URL, ICON_PATH, TOAST_MESSAGE } from '@/lib/constants';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { pxToRem } from '@repo/ui/lib/utils';
import { useCallback, useEffect, useState } from 'react';
import { FieldErrors, useForm } from 'react-hook-form';
import { z } from 'zod';
import { AvatarImageUploader } from '@/components/ui/avatar-image-uploader';
import { useFileDragAndDrop } from '@/hooks/use-file-drag-and-drop';
import { cn } from '@repo/ui/lib/utils';

const SNS_TYPES = [
  'youtube',
  'twitter',
  'telegram',
  'facebook',
  'discord',
  'tiktok',
  'instagram',
  'abstract',
] as const;

// Form schema based on UpdateChannelReqDto
const channelFormSchema = z.object({
  name: z
    .string()
    .max(20, 'Channel name must be at most 20 characters.')
    .regex(/^[a-zA-Z0-9\s]+$/, 'Channel name must contain only letters, numbers, and spaces.')
    .optional(),
  description: z.string().max(255, 'Description must be at most 255 characters.').optional(),
  channelSns: z
    .array(
      z.object({
        snsType: z.enum(SNS_TYPES),
        snsUrl: z.string().url().or(z.literal('')),
      })
    )
    .optional(),
});

type ChannelFormValues = z.infer<typeof channelFormSchema>;

const validateImageFile = (file: File, maxSizeMB: number): string | null => {
  if (!file.type.startsWith('image/')) {
    return 'Please select an image file';
  }
  if (file.size > maxSizeMB * 1024 * 1024) {
    return `File size must be less than ${maxSizeMB}MB`;
  }
  return null;
};

const readFileAsDataURL = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => resolve(e.target?.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

const createInitialChannelSns = (channelInfo?: any) =>
  SNS_TYPES.map(snsType => {
    const existingSns = channelInfo?.channelSns?.find((sns: any) => sns.snsType === snsType);
    return {
      snsType,
      snsUrl: existingSns?.snsUrl || '',
    };
  });

const filterAndSortChannelSns = (channelSns: Array<{ snsType: string; snsUrl: string }>) =>
  channelSns
    .filter(sns => sns.snsUrl.trim() !== '')
    .map(sns => ({ snsType: sns.snsType, snsUrl: sns.snsUrl }))
    .sort((a, b) => a.snsType.localeCompare(b.snsType));

const hasFormChanges = (
  watchedValues: ChannelFormValues,
  bannerFile: File | null,
  avatarFile: File | null,
  channelInfo?: any
): boolean => {
  // 이름 변경 확인
  if (watchedValues.name && watchedValues.name !== channelInfo?.name) {
    return true;
  }

  // 설명 변경 확인
  if (
    watchedValues.description !== undefined &&
    watchedValues.description !== channelInfo?.description
  ) {
    return true;
  }

  // 파일 업로드 확인
  if (avatarFile || bannerFile) {
    return true;
  }

  // SNS URL 변경 확인
  if (watchedValues.channelSns) {
    const currentChannelSns = filterAndSortChannelSns(watchedValues.channelSns);

    const initialChannelSns = filterAndSortChannelSns(channelInfo?.channelSns || []);

    return JSON.stringify(initialChannelSns) !== JSON.stringify(currentChannelSns);
  }

  return false;
};

const createUpdateData = (
  values: ChannelFormValues,
  bannerFile: File | null,
  avatarFile: File | null,
  channelInfo?: any
): UpdateChannelRequestBody => {
  const updateData: UpdateChannelRequestBody = {};

  // 변경된 필드만 포함
  if (values.name && values.name !== channelInfo?.name) {
    updateData.name = values.name;
  }

  if (values.description !== undefined && values.description !== channelInfo?.description) {
    updateData.description = values.description;
  }

  if (avatarFile) {
    updateData.image = avatarFile;
  }

  if (bannerFile) {
    updateData.banner = bannerFile;
  }

  if (values.channelSns) {
    const currentChannelSns = filterAndSortChannelSns(values.channelSns);
    const initialChannelSns = filterAndSortChannelSns(channelInfo?.channelSns || []);

    if (JSON.stringify(initialChannelSns) !== JSON.stringify(currentChannelSns)) {
      updateData.channelSns = currentChannelSns.map(sns => ({
        snsType: sns.snsType as any,
        snsUrl: sns.snsUrl,
      }));
    }
  }

  return updateData;
};

const PLACEHOLDER_TEXT = {
  channelName: 'Enter channel name (a-z, A-Z, 0-9, max 20 chars)',
  description:
    'Please write about your channel. Describe what your channel will focus on, your content, and what users can expect.',
};

const SNS_DISPLAY_OBJECT: Record<
  ChannelSnsType,
  {
    key: ChannelSnsType;
    iconPath: string;
    placeholder: string;
  }
> = {
  youtube: {
    key: 'youtube',
    iconPath: ICON_PATH.YOUTUBE,
    placeholder: 'Enter your YouTube channel URL',
  },
  twitter: {
    key: 'twitter',
    iconPath: ICON_PATH.X,
    placeholder: 'Enter your X (Twitter) URL',
  },
  facebook: {
    key: 'facebook',
    iconPath: ICON_PATH.FACEBOOK,
    placeholder: 'Enter your Facebook page URL',
  },
  telegram: {
    key: 'telegram',
    iconPath: ICON_PATH.TELEGRAM,
    placeholder: 'Enter your Telegram URL',
  },
  discord: {
    key: 'discord',
    iconPath: ICON_PATH.DISCORD,
    placeholder: 'Enter your Discord URL',
  },
  tiktok: {
    key: 'tiktok',
    iconPath: ICON_PATH.TIKTOK,
    placeholder: 'Enter your TikTok URL',
  },
  instagram: {
    key: 'instagram',
    iconPath: ICON_PATH.INSTAGRAM,
    placeholder: 'Enter your Instagram URL',
  },
  abstract: {
    key: 'abstract',
    iconPath: ICON_PATH.ABSTRACT,
    placeholder: 'Enter your Abstract URL',
  },
};

export default function ChannelsSettingPage() {
  const { data: channelInfo, isLoading, error } = useMyChannel();
  const updateChannelMutation = useUpdateChannel();

  // 파일 상태 관리
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [bannerPreview, setBannerPreview] = useState('');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState(DEFAULT_MARKET_AVATAR_URL);

  const form = useForm<ChannelFormValues>({
    resolver: zodResolver(channelFormSchema),
    defaultValues: {
      name: channelInfo?.name || '',
      description: channelInfo?.description || '',
      channelSns: createInitialChannelSns(channelInfo),
    },
  });
  const handleBannerFileSelect = useCallback(async (file: File) => {
    const error = validateImageFile(file, 5);
    if (error) {
      toast.error(error);
      return;
    }

    try {
      const preview = await readFileAsDataURL(file);
      setBannerFile(file);
      setBannerPreview(preview);
    } catch {
      toast.error('Failed to read file');
    }
  }, []);

  const {
    isDragging: isBannerDragging,
    handleDragOver: handleBannerDragOver,
    handleDragLeave: handleBannerDragLeave,
    handleDrop: handleBannerDrop,
  } = useFileDragAndDrop({
    onFileDrop: handleBannerFileSelect,
  });

  const watchedValues = form.watch();

  // channelInfo가 로딩된 후 form 기본값 동기화
  useEffect(() => {
    if (channelInfo) {
      form.reset({
        name: channelInfo.name || '',
        description: channelInfo.description || '',
        channelSns: createInitialChannelSns(channelInfo),
      });

      // 파일 미리보기 동기화
      setBannerPreview(channelInfo.bannerUrl || '');
      setAvatarPreview(channelInfo.imageUrl || DEFAULT_MARKET_AVATAR_URL);
    }
  }, [channelInfo, form]);

  const handleFileUpload = useCallback((accept: string, onFileSelect: (file: File) => void) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        onFileSelect(file);
      }
    };
    input.click();
  }, []);

  const handleAvatarFileSelect = useCallback(
    async (file: File | null) => {
      setAvatarFile(file);
      if (file) {
        try {
          const preview = await readFileAsDataURL(file);
          setAvatarPreview(preview);
        } catch {
          toast.error('Failed to read file');
        }
      } else {
        setAvatarPreview(channelInfo?.imageUrl || DEFAULT_MARKET_AVATAR_URL);
      }
    },
    [channelInfo]
  );

  const handleBannerUpload = useCallback(() => {
    handleFileUpload('image/jpeg,image/png', handleBannerFileSelect);
  }, [handleFileUpload, handleBannerFileSelect]);

  const onInvalid = useCallback((errors: FieldErrors<ChannelFormValues>) => {
    if (errors.channelSns && Array.isArray(errors.channelSns)) {
      const isSnsError = errors.channelSns.some(field => field?.snsUrl?.message);

      if (isSnsError) {
        toast.error(TOAST_MESSAGE.INVALID_URL);
      }
    }
  }, []);

  const onSubmit = useCallback(
    async (values: ChannelFormValues) => {
      try {
        const updateData = createUpdateData(values, bannerFile, avatarFile, channelInfo);

        if (Object.keys(updateData).length > 0) {
          await updateChannelMutation.mutateAsync(updateData);
          toast.success(TOAST_MESSAGE.CHANNEL_SETTING_UPDATE_SUCCESS);
        } else {
          toast.error(TOAST_MESSAGE.CHANNEL_SETTING_UPDATE_NO_CHANGES);
        }
      } catch (error) {
        console.error('Failed to update channel:', error);
        toast.error(TOAST_MESSAGE.CHANNEL_SETTING_UPDATE_ERROR);
      }
    },
    [bannerFile, avatarFile, channelInfo, updateChannelMutation]
  );

  if (isLoading) {
    return (
      <PageContainer title="Edit Channel">
        <div className="flex items-center justify-center p-8">
          <div className="text-gray-500">Loading channel information...</div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer title="Edit Channel">
        <div className="flex items-center justify-center p-8">
          <div className="text-red-500">Failed to load channel information</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Edit Channel">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit, onInvalid)}
          className="gap-space-30 flex flex-col"
        >
          {/* Error Display */}
          {updateChannelMutation.error && (
            <div className="rounded-md border border-red-200 bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Channel update failed</h3>
                  <div className="mt-2 text-sm text-red-700">
                    {updateChannelMutation.error.message}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Banner Upload Section */}
          <section className="gap-space-20 flex flex-col">
            <p className="text-size-sm text-gray-3">
              Set a representative image for your channel. Applying it service-wide may take up to
              five minutes.
            </p>
            <div
              data-role="upload-banner-image"
              onDragOver={handleBannerDragOver}
              onDragLeave={handleBannerDragLeave}
              onDrop={handleBannerDrop}
              className={cn(
                'bg-gray-2 border-line relative h-(--banner-image-height) w-full overflow-hidden rounded-lg border transition-all duration-200',
                isBannerDragging && 'scale-105'
              )}
            >
              <div
                className="h-full w-full"
                style={{
                  // backgroundSize: 'cover',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center',
                  backgroundImage: `url('${bannerPreview}')`,
                }}
              ></div>
            </div>
            <div className="flex justify-end">
              <div className="gap-space-20 flex items-center">
                <p className="text-size-xs text-gray-3 text-center">
                  At least <b className="text-mid-dark">1258px X 237px</b> recommended.
                  <b className="text-mid-dark">JPG or PNG</b> is allowed. File size up to{' '}
                  <b className="text-mid-dark">5MB</b>.
                </p>
                <BaseButton
                  type="button"
                  variant="info"
                  aria-label="Upload banner image"
                  onClick={handleBannerUpload}
                >
                  <SvgIcon name="ImageFillIcon" />
                  Upload new photo
                </BaseButton>
              </div>
            </div>
          </section>

          {/* Avatar Upload Section */}
          <section>
            <div data-role="upload-icon-image" className="gap-space-30 flex items-center">
              <AvatarImageUploader
                imageUrl={avatarPreview}
                onFileSelect={handleAvatarFileSelect}
                size={80}
                maxSize={1 * 1024 * 1024} // 1MB
              />
              <div className="text-sm text-gray-500">
                <div>
                  At least <b>80px X 80px</b> recommended.
                  <br />
                  <b>JPG or PNG</b> is allowed. File size up to <b>1MB</b>.
                </div>
              </div>
            </div>
          </section>

          {/* SNS Links Section */}
          <section>
            <div className="gap-space-10 flex flex-col">
              {Object.entries(SNS_DISPLAY_OBJECT).map(
                ([snsType, { iconPath, key, placeholder }], index) => {
                  return (
                    <FormField
                      key={snsType}
                      control={form.control}
                      name={`channelSns.${index}.snsUrl`}
                      render={({ field, fieldState: { error } }) => (
                        <FormItem>
                          <div className="gap-space-15 flex items-center">
                            <img src={iconPath} alt={key} width={20} height={20} />
                            <FormControl>
                              <BaseInput
                                placeholder={placeholder}
                                {...field}
                                className={error ? 'border-red-500' : ''}
                              />
                            </FormControl>
                          </div>
                        </FormItem>
                      )}
                    />
                  );
                }
              )}
            </div>
          </section>

          {/* Channel Info Section */}
          <section className="gap-space-30 flex flex-col">
            <header className="text-size-base text-mid-dark font-bold">Channel Info</header>
            <div className="gap-space-30 flex flex-col md:flex-row">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem data-role="write-channel-name" className="space-y-space-15 flex-1">
                    <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                      Channel Name
                    </FormLabel>
                    <FormControl>
                      <BaseInput placeholder={PLACEHOLDER_TEXT.channelName} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem data-role="write-channel-description" className="space-y-space-15">
                  <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                    About Channel
                  </FormLabel>
                  <FormControl>
                    <TextareaWithCharacterCount
                      style={{ height: pxToRem(224) }}
                      placeholder={PLACEHOLDER_TEXT.description}
                      maxLength={255}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <DarkButton
            type="submit"
            size="lg"
            fontSize="base"
            disabled={
              updateChannelMutation.isPending ||
              !hasFormChanges(watchedValues, bannerFile, avatarFile, channelInfo)
            }
            style={{
              width: pxToRem(148),
            }}
          >
            {updateChannelMutation.isPending ? 'Saving...' : 'Save changes'}
          </DarkButton>
        </form>
      </Form>
    </PageContainer>
  );
}
