import { MainLayout } from '@/components/layouts/main-layout';
import { AsideNavigation } from '@/components/layouts/aside-navigation';
import { AuthGuard } from '@/components/common/auth-guard';

export default function WithAsideLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard>
      <MainLayout>
        <div className="min-h-[calc(100vh-109px - 160px)] relative flex">
          {/* Fixed Aside */}
          <aside className="border-r-line fixed top-[109px] bottom-0 left-0 z-10 w-[256px] border-r bg-white">
            <AsideNavigation />
          </aside>
          {/* Main Content */}
          <div className="min-w-0 flex-1 pl-[256px]">
            <div className="mx-auto h-full w-[1300px] overflow-x-auto px-4 py-6">{children}</div>
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
