'use client';

import { InfoButton } from '@/components/ui/base.button';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { Skeleton } from '@/components/ui/skeleton';
import { useReferralBenefit, useReferralClaimable } from '@/hooks/query/referral';
import { useReferralTotal } from '@/hooks/query/referral/use-referral-total';
import { toPercentage } from '@/lib/utils';

export default function MyReferralDashboard() {
  const { data: referralTotal } = useReferralTotal();
  const { data: referralClaimable } = useReferralClaimable();
  const { data: referralBenefit } = useReferralBenefit();

  const totalRewards = referralTotal?.totalReward.formatted;
  const totalClaimable = referralClaimable?.totalReward.formatted;

  const yourLevel = referralBenefit?.level;
  const commissionRewardRatio = referralBenefit?.commissionRewardRatio;
  const feeRebateRatio = referralBenefit?.feeRebateRatio;

  const isZeroClaimable = referralClaimable?.totalReward.raw.isZero();
  return (
    <section>
      <h2 className="dashboard-h2 pb-space-30">Dashboard</h2>
      <div className="gap-space-60 pb-space-50 border-b-line flex border-b">
        <div className="flex h-[362px] flex-1 flex-col justify-between">
          <DashboardCard variant="info">
            <div className="text-size-sm font-semibold">Total Rewards</div>
            {totalRewards ? (
              <div className="text-size-2xl font-bold">${totalRewards}</div>
            ) : (
              <Skeleton className="h-10 w-full" />
            )}
          </DashboardCard>
          <DashboardCard className="border-sky border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claim Rewards</div>
            </div>
            <div className="pl-space-10 flex justify-between">
              <span className="text-size-xs text-mid-dark font-semibold">Claimable</span>
              <div className="gap-space-30 flex items-center">
                {totalClaimable ? (
                  <div className="text-size-2xl font-bold">${totalClaimable}</div>
                ) : (
                  <Skeleton className="h-10 w-full" />
                )}
                <InfoButton className="w-[160px]" disabled={!!isZeroClaimable}>
                  Claim
                </InfoButton>
              </div>
            </div>
          </DashboardCard>
        </div>
        <div className="h-[362px] flex-1">
          <DashboardCard className="border-sky h-full border" variant="neutral">
            <div className="text-size-sm flex justify-between font-semibold">
              <span>
                Your Level :{' '}
                {yourLevel ? (
                  <span className="text-no-red">Lv {yourLevel}</span>
                ) : (
                  <Skeleton className="h-4 w-16" />
                )}
              </span>
              <span>
                Total Rebate :{' '}
                {feeRebateRatio ? (
                  <span className="text-no-red">{toPercentage(feeRebateRatio)}</span>
                ) : (
                  <Skeleton className="h-4 w-16" />
                )}
              </span>
            </div>
            <div className="text-size-xs text-mid-dark pb-space-30">
              Next referral level update date Apr 01 2025, 00:00 (UTC)
            </div>
            {/* Image and Commission/Rebate Details */}
            <div className="p-space-30 mb-space-30 flex items-center justify-between rounded-md bg-gray-800">
              {/* Placeholder for image */}
              <div className="text-size-xs flex h-[70px] w-[100px] items-center justify-center bg-gray-700 text-white">
                Image Placeholder
              </div>
              <div>
                <div className="text-size-sm text-sky">You Receive</div>
                <div className="text-size-xl font-bold text-white">
                  <span className="text-sky font-bold">
                    {commissionRewardRatio ? toPercentage(commissionRewardRatio) : '-'}
                  </span>
                  <span className="text-white">Commission Reward</span>
                </div>
                <div className="text-size-sm pt-space-10 text-no-red">Your Friends Receive</div>
                <div className="text-size-xl font-bold text-white">
                  <span className="text-no-red font-bold">
                    {feeRebateRatio ? toPercentage(feeRebateRatio) : '-'}
                  </span>
                  <span className="text-white">Fee Rebate</span>
                </div>
              </div>
            </div>
            {/* Social Sharing Buttons */}
            <div className="gap-space-10 flex">
              <InfoButton className="flex-1">Copy Link</InfoButton>
              <InfoButton className="flex-1">Twitter</InfoButton>
              <InfoButton className="flex-1">Telegram</InfoButton>
            </div>
          </DashboardCard>
        </div>
      </div>
    </section>
  );
}
