'use client';

import { useReferralBenefit } from '@/hooks/query/referral/use-referral-benefit';
import { useReferralTotal } from '@/hooks/query/referral/use-referral-total';
import { toPercentage } from '@/lib/utils';
import { Skeleton } from '@repo/ui/components/skeleton';

export default function MyReferralFeeRebate() {
  const { data: benefit, isLoading, error } = useReferralBenefit();
  const { data: referralTotal } = useReferralTotal();

  const totalRebate = referralTotal?.totalFeeRebate.formatted;
  const feeRebateRatio = benefit?.feeRebateRatio;
  const bindReferralCode = benefit?.bindReferralCode;

  if (error) {
    return (
      <section className="pt-space-50 gap-space-30 flex flex-col">
        <header>
          <h2 className="dashboard-h2">Fee Rebate</h2>
        </header>
        <div className="text-center text-red-500">
          Failed to load referral benefit data. Please try again.
        </div>
      </section>
    );
  }

  return (
    <section className="pt-space-50 gap-space-30 flex flex-col">
      <header>
        <h2 className="dashboard-h2">Fee Rebate</h2>
      </header>
      <div className="gap-space-60 flex">
        <div className="gap-space-30 flex flex-1">
          <div className="gap-space-15 flex flex-1 flex-col">
            <div className="dashboard-h3">Bind Referral Code</div>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {isLoading ? (
                <div className="h-4 w-8 animate-pulse rounded bg-gray-300" />
              ) : (
                (bindReferralCode ?? '-')
              )}
            </div>
          </div>
          <div className="gap-space-15 flex flex-1 flex-col">
            <div className="dashboard-h3">Fee Rebate</div>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {isLoading ? (
                <div className="h-4 w-12 animate-pulse rounded bg-gray-300" />
              ) : feeRebateRatio ? (
                toPercentage(feeRebateRatio)
              ) : (
                '-'
              )}
            </div>
          </div>
        </div>
        <div className="gap-space-15 flex flex-1 flex-col">
          <div className="dashboard-h3">Total Rebate</div>
          <div className="text-size-lg border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-bold">
            {totalRebate ? (
              <div className="text-size-lg font-bold">${totalRebate}</div>
            ) : (
              <Skeleton className="h-10 w-full" />
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
