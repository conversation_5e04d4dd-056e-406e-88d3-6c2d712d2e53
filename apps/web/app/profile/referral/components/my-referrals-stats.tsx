'use client';
import { useReferralBenefit } from '@/hooks/query/referral';
import { useReferralTotal } from '@/hooks/query/referral/use-referral-total';
import { toPercentage } from '@/lib/utils';
import { Skeleton } from '@repo/ui/components/skeleton';
import { pxToRem } from '@repo/ui/lib/utils';

export default function MyReferralsStats() {
  const { data: referralTotal } = useReferralTotal();
  const { data: benefitData } = useReferralBenefit();

  const yourCode = benefitData?.referralCode;
  const totalInvitees = benefitData?.totalInvitees;
  const totalCommissions = referralTotal?.totalCommissionReward;
  const yourLevel = benefitData?.level;

  const commissionRewardRatio = benefitData?.commissionRewardRatio;
  const feeRebateRatio = benefitData?.feeRebateRatio;
  const totalProfit = benefitData?.profit;

  return (
    <section
      className="py-space-50 gap-space-30 border-b-line flex flex-col border-b"
      style={
        {
          '--value-box-height': pxToRem(50),
        } as React.CSSProperties
      }
    >
      <header>
        <h2 className="dashboard-h2">Referrals</h2>
      </header>

      <div className="gap-space-60 flex w-full">
        <div className="gap-space-30 flex flex-1">
          <div className="gap-space-10 flex flex-1 flex-col">
            <h3 className="dashboard-h3">Your code</h3>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {yourCode || '-'}
            </div>
          </div>
          <div className="gap-space-10 flex flex-1 flex-col">
            <h3 className="dashboard-h3">Total Invitees</h3>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {totalInvitees || 0}
            </div>
          </div>
        </div>
        <div className="gap-space-10 flex flex-1 flex-col">
          <h3 className="dashboard-h3">Total Commissions</h3>
          <div className="text-size-lg border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-bold">
            {totalCommissions ? (
              <div className="text-icon-dark text-size-base flex-1 font-bold">
                ${totalCommissions.formatted}
              </div>
            ) : (
              <Skeleton className="h-4 flex-1" />
            )}
          </div>
        </div>
      </div>

      <div className="p-space-20 bg-gray-2 flex h-[200px] flex-col gap-[35px]">
        <div className="text-size-sm gap-space-30 flex font-semibold">
          <span className="text-sky">
            {new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </span>
          <div>
            Your Level&nbsp;:&nbsp;
            <span className="text-no-red">{yourLevel || 1}</span>
          </div>
        </div>

        <div className="flex flex-1 flex-col justify-between">
          <div className="flex items-center justify-between">
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Commission Reward
            </div>
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Fee Rebate
            </div>
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Total Profit
            </div>
          </div>
          <div className="bg-line h-[1px]" />
          <div className="flex items-center justify-between">
            {commissionRewardRatio ? (
              <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
                {toPercentage(commissionRewardRatio)}
              </div>
            ) : (
              <Skeleton className="h-4 flex-1" />
            )}
            {feeRebateRatio ? (
              <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
                {toPercentage(feeRebateRatio)}
              </div>
            ) : (
              <Skeleton className="h-4 flex-1" />
            )}
            {totalProfit ? (
              <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
                ${totalProfit.formatted}
              </div>
            ) : (
              <Skeleton className="h-4 flex-1" />
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
