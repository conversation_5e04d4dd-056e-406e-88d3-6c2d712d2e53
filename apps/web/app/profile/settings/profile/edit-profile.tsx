'use client';

import { AvatarImageUploader } from '@/components/ui/avatar-image-uploader';
import { BaseButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { InputWithCharacterCount } from '@/components/ui/input-with-character-count';
import { TextareaWithCharacterCount } from '@/components/ui/textarea-with-character-count';
import { useCurrentUser, useUpdateUserProfile } from '@/hooks/query/user';
import { UpdateUserProfileRequestBodySchema } from '@/lib/api/user/user.schema.server';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { toast } from '@repo/ui/components/sonner';
import { pxToRem } from '@repo/ui/lib/utils';
import { useEffect, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { z } from 'zod';

type ProfileFormValues = z.infer<typeof UpdateUserProfileRequestBodySchema>;

export default function EditProfile() {
  const { data: userInfo, refetch } = useCurrentUser();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  useEffect(() => {
    if (userInfo?.imageUrl) {
      setImagePreview(userInfo.imageUrl);
    }
  }, [userInfo?.imageUrl]);

  const updateUserProfileMutation = useUpdateUserProfile({
    onSuccess: () => {
      toast.success('Profile updated successfully!');
      refetch();
    },
    onError: () => {
      toast.error('Failed to update profile');
    },
  });

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(UpdateUserProfileRequestBodySchema),
    defaultValues: {
      nickname: userInfo?.nickname || '',
      bio: userInfo?.bio || '',
      image: undefined as unknown as File | undefined,
      email: userInfo?.email || '',
    },
  });

  // Watch form values for changes
  const watchedValues = useWatch({
    control: form.control,
  });

  useEffect(() => {
    if (userInfo) {
      form.reset({
        nickname: userInfo.nickname || '',
        bio: userInfo.bio || '',
        image: undefined as unknown as File | undefined,
        email: userInfo.email || '',
      });
    }
  }, [userInfo, form]);

  const handleFileSelect = (file: File | null) => {
    if (!file) {
      setImageFile(null);
      setImagePreview(userInfo?.imageUrl || '');
      return;
    }

    setImageFile(file);

    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  // Check if form has changes
  const hasChanges = () => {
    if (!userInfo) return false;

    const isNicknameChanged = (watchedValues.nickname || '') !== (userInfo.nickname || '');
    const isEmailChanged = (watchedValues.email || '') !== (userInfo.email || '');
    const isBioChanged = (watchedValues.bio || '') !== (userInfo.bio || '');
    const hasImageChange = imageFile !== null;

    return isNicknameChanged || isEmailChanged || isBioChanged || hasImageChange;
  };

  const onSubmit = async (values: ProfileFormValues) => {
    if (!userInfo) return;

    // This should not happen since button is disabled when no changes
    if (!hasChanges()) {
      toast.info('No changes to save');
      return;
    }

    try {
      const formData = new FormData();

      // Add only changed fields
      if ((values.nickname || '') !== (userInfo.nickname || '')) {
        formData.append('nickname', values.nickname || '');
      }

      if ((values.email || '') !== (userInfo.email || '')) {
        formData.append('email', values.email || '');
      }

      if ((values.bio || '') !== (userInfo.bio || '')) {
        formData.append('bio', values.bio || '');
      }

      if (imageFile) {
        formData.append('image', imageFile);
      }

      // if (values.email && values.email !== userInfo.email) {
      //   await requestEmailVerification(values.email);
      // }

      await updateUserProfileMutation.mutateAsync(formData);
    } catch (error) {
      console.error('Profile update error:', error);
    }
  };
  return (
    <>
      {/* Avatar Section */}
      <div className="mb-space-30">
        <div className="flex items-center gap-6">
          <AvatarImageUploader
            imageUrl={imagePreview}
            onFileSelect={handleFileSelect}
            size={80}
            disabled={updateUserProfileMutation.isPending}
          />
          <div className="text-size-xs text-gray-3">
            <div>
              At least <strong>80px X 80px</strong> recommended. <br /> <strong>JPG or PNG</strong>{' '}
              is allowed.
            </div>
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="gap-space-30 pt-space-30 flex flex-col">
            <h2 className="text-mid-dark text-size-base font-bold">Personal Info</h2>
            <FormField
              control={form.control}
              name="nickname"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="nickname"
                    className="text-size-sm text-mid-dark block font-medium"
                  >
                    Username
                  </FormLabel>
                  <FormControl>
                    <InputWithCharacterCount
                      id="nickname"
                      placeholder="Enter your username"
                      maxLength={42}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="email"
                    className="text-size-sm text-mid-dark block font-medium"
                  >
                    Email
                  </FormLabel>
                  <FormControl>
                    <BaseInput
                      id="email"
                      placeholder="Enter your email"
                      className={
                        form.formState.errors.email ? 'border-red-500 focus:border-red-500' : ''
                      }
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel htmlFor="bio" className="text-size-sm text-mid-dark block font-medium">
                    Bio
                  </FormLabel>
                  <FormControl>
                    <TextareaWithCharacterCount
                      id="bio"
                      placeholder="Tell us about yourself"
                      className="resize-none"
                      maxLength={256}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-8 flex space-x-4">
            <BaseButton
              type="submit"
              variant="dark"
              style={{
                width: pxToRem(148),
                fontSize: 'var(--text-size-sm13)',
              }}
              disabled={updateUserProfileMutation.isPending || !hasChanges()}
            >
              {updateUserProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
            </BaseButton>
          </div>
        </form>
      </Form>
    </>
  );
}
