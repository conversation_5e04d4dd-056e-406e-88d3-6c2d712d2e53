'use client';

import ShareClaimButton from '@/components/actions/share-claim-button';
import SvgIcon from '@/components/icons/svg-icon';
import PageContainer from '@/components/layouts/page-container';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { pxToRem } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { ShareRewardsTable } from './share-rewards-table';
import { Skeleton } from '@repo/ui/components/skeleton';
import { useMyShareDashboard } from '@/hooks/query/share/use-my-share-dashboard';

export default function ShareBonusPage() {
  const { data: dashboardData } = useMyShareDashboard();

  const totalClaimedReward = dashboardData?.totalClaimedFormatted;
  const claimableReward = dashboardData?.claimableAmountFormatted;
  const unLockableReward = dashboardData?.unLockableShareRewardsFormatted;

  const isNotClaimable = !claimableReward || claimableReward === '0';
  return (
    <PageContainer title="Share Bonus">
      <div className="flex flex-col">
        <h2 className="dashboard-h2 pb-space-30">Dashboard</h2>
        <section className="gap-space-60 pb-space-50 border-b-line flex border-b">
          <DashboardCard className="flex-1" variant="info">
            <div className="text-size-sm font-semibold">Total Claimed Rewards</div>
            <div className="flex justify-between">
              {totalClaimedReward ? (
                <div className="text-size-2xl font-bold">${totalClaimedReward}</div>
              ) : (
                <Skeleton className="h-4 w-16" />
              )}
              <Link className="text-size-xs text-gray-2 gap-space-6 flex items-center" href={'/'}>
                Details{' '}
                <SvgIcon
                  data-label="icon"
                  name="ChevronNextIcon"
                  className="size-[8px] text-white"
                />
              </Link>
            </div>
          </DashboardCard>
          <DashboardCard className="border-sky flex-1 border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claimable Rewards</div>
            </div>
            <div className="pl-space-10 flex justify-between">
              {claimableReward ? (
                <span className="text-size-2xl text-mid-dark font-semibold">
                  ${claimableReward}
                </span>
              ) : (
                <Skeleton className="h-4 w-16" />
              )}
              <ShareClaimButton disabled={isNotClaimable} className="w-[160px]" />
            </div>
          </DashboardCard>
        </section>
        <section className="py-space-50 gap-space-20 flex flex-col">
          <header>
            <h2 className="dashboard-h2 pb-space-30">Share and Earn Extra Rewards!</h2>
          </header>
          {/* Unlockable rewards */}
          <div
            style={{
              height: pxToRem(100),
            }}
            className="p-space-20 border-sky ml-auto flex w-[50%] flex-col justify-between border"
          >
            <h3 className="dashboard-h3">Unlockable Rewards</h3>
            <div className="text-size-xl flex justify-end text-right font-bold">
              {unLockableReward ? (
                <span className="text-size-2xl text-mid-dark font-semibold">
                  ${unLockableReward}
                </span>
              ) : (
                <Skeleton className="h-4 w-16" />
              )}
            </div>
          </div>
          <div className="pt-space-20">
            <ShareRewardsTable />
          </div>
        </section>
      </div>
    </PageContainer>
  );
}
