'use client';

import * as React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { BaseButton } from '@/components/ui/base.button';
import CommonAvatar from '@/components/ui/avatar-image';
import { Skeleton } from '@repo/ui/components/skeleton';
import { ShareRewardItem } from '@/hooks/query/share/share.mapper';
import { useMyShares } from '@/hooks/query/share/use-my-shares';
import { useShareMarket } from '@/hooks/query/share/use-share-market';

type SortOption = 'NEWEST' | 'VALUE';

const sortOptions = [
  { value: 'NEWEST', label: 'Newest' },
  { value: 'VALUE', label: 'Value' },
] as const;

export function ShareRewardsTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [sortBy, setSortBy] = React.useState<SortOption>('NEWEST');

  const { data: sharesData, isLoading } = useMyShares({
    page: 0,
    limit: 50,
    order: sortBy,
  });

  const shareMarketMutation = useShareMarket();
  const columns = React.useMemo<ColumnDef<ShareRewardItem>[]>(
    () => [
      {
        accessorKey: 'prediction',
        header: 'Predictions',
        size: 50, // 40% 차지
        cell: ({ row }) => (
          <div className="gap-space-8 flex items-center">
            <CommonAvatar imageUrl={row.original.prediction.avatar} size="md" alt="Prediction" />
            <span className="text-mid-dark text-size-sm font-semibold">
              {row.original.prediction.title}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'winnings',
        header: () => <div className="text-center">Winnings</div>,
        size: 20, // 20% 차지
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm text-center font-semibold">
            ${row.original.winningsFormatted}
          </div>
        ),
      },
      {
        accessorKey: 'shareBonus',
        header: () => <div className="text-center">Share Bonus</div>,
        size: 20, // 20% 차지
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm text-center font-semibold">
            ${row.original.shareBonusFormatted}
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: () => <div className="text-center">Share</div>,
        size: 10, // 20% 차지
        cell: ({ row }) => (
          <div className="flex justify-center">
            {row.original.status === 'Share' ? (
              <BaseButton
                variant="info"
                size="sm2"
                className="text-size-sm w-[140px] text-white"
                onClick={() => handleShare(row.original.prediction.id)}
                disabled={shareMarketMutation.isPending}
              >
                {shareMarketMutation.isPending ? 'Sharing...' : 'Share'}
              </BaseButton>
            ) : (
              <span className="text-size-sm text-icon-dark font-semibold">Completed</span>
            )}
          </div>
        ),
      },
    ],
    []
  );

  const sortedData = React.useMemo(() => {
    return sharesData?.shares || [];
  }, [sharesData]);

  const table = useReactTable({
    data: sortedData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  const handleShare = (marketId: string) => {
    // TODO: 실제 서명 로직 구현 필요
    const mockSignature = 'mock-signature-' + Date.now();
    shareMarketMutation.mutate({
      marketId,
      signature: mockSignature,
    });
  };

  const renderSkeletonRows = () => {
    return Array.from({ length: 5 }).map((_, index) => (
      <TableRow key={`skeleton-${index}`} className="border-0">
        <TableCell className="py-space-15" style={{ width: '50%' }}>
          <div className="gap-space-8 flex items-center">
            <Skeleton className="h-10 w-10 rounded-full" />
            <Skeleton className="h-4 w-32" />
          </div>
        </TableCell>
        <TableCell className="py-space-15" style={{ width: '20%' }}>
          <div className="flex justify-center">
            <Skeleton className="h-4 w-16" />
          </div>
        </TableCell>
        <TableCell className="py-space-15" style={{ width: '20%' }}>
          <div className="flex justify-center">
            <Skeleton className="h-4 w-16" />
          </div>
        </TableCell>
        <TableCell className="py-space-15" style={{ width: '10%' }}>
          <div className="flex justify-center">
            <Skeleton className="h-8 w-[140px] rounded-md" />
          </div>
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <div className="w-full">
      <div>
        <div className="mb-4 flex justify-start">
          <div className="flex items-center gap-2">
            <BaseSelect value={sortBy} onValueChange={v => setSortBy(v as SortOption)}>
              <BaseSelectTrigger className="h-8 w-[150px]" size="sm">
                <BaseSelectValue />
              </BaseSelectTrigger>
              <BaseSelectContent>
                {sortOptions.map(option => (
                  <BaseSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </BaseSelectItem>
                ))}
              </BaseSelectContent>
            </BaseSelect>
          </div>
        </div>
        <Table className="w-full">
          <TableHeader className="h-[60px]">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead
                    className="text-size-sm text-gray-3 font-semibold"
                    key={header.id}
                    style={{ width: header.column.getSize() + '%' }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              renderSkeletonRows()
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      className="py-space-15"
                      key={cell.id}
                      style={{ width: cell.column.getSize() + '%' }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  <div className="text-size-sm text-mid-dark font-semibold">No Market found</div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
