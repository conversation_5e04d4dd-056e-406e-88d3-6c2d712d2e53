'use client';

import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { UserSubscriptionsChannelProps } from '@/hooks/query/user/use-user-subscriptions';
import { Switch } from '@repo/ui/components/switch';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';

interface SubscribedChannelProps {
  channel: UserSubscriptionsChannelProps;
  optimisticSubscribed: boolean;
  onSubscriptionChange?: (channelId: string, isSubscribed: boolean, channelName: string) => void;
  isLoading?: boolean;
}

export default function SubscribedChannel({
  channel,
  optimisticSubscribed,
  onSubscriptionChange,
  isLoading = false,
}: SubscribedChannelProps) {
  const { channelId, channelName, channelImageUrl, channelLeaderNickname, channelSubscribers } =
    channel;

  const handleSubscriptionChange = async (checked: boolean) => {
    if (onSubscriptionChange) {
      await onSubscriptionChange(channelId, checked, channelName);
    }
  };

  return (
    <div
      className={`border-line py-space-20 gap-space-60 flex items-center justify-between border-b transition-opacity duration-200 ${
        !optimisticSubscribed ? 'opacity-50' : 'opacity-100'
      }`}
    >
      <div className="gap-space-30 flex w-full items-center">
        <CommonAvatar
          imageUrl={channelImageUrl}
          size="md3"
          alt={channelName}
          href={INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}
        />
        <div className="flex-1">
          {/* Channel Title */}
          <Link
            href={INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}
            className="text-dark-deep mb-space-5 block text-lg font-bold hover:underline"
          >
            {channelName}
          </Link>
          {/* Channel Info */}
          <div className="gap-space-20 text-size-xs flex">
            {/* Username */}
            <div className="gap-space-5 flex items-center">
              <SvgIcon name="CrownIcon" />
              <span>{channelLeaderNickname}</span>
            </div>
            {/* Subscribers */}
            <div className="gap-space-5 flex items-center">
              <SvgIcon name="SubscriptionInversedIcon" />
              <span className="text-gray-3">Subscribers</span>
              <span className="text-icon-dark">{channelSubscribers}</span>
            </div>
          </div>
        </div>
        {/* Switch */}
        <div className="gap-space-5 flex items-center">
          <Switch
            id={`subscribe-${channelId}`}
            checked={optimisticSubscribed}
            onCheckedChange={handleSubscriptionChange}
            disabled={isLoading}
            className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
          />

          <label
            htmlFor={`subscribe-${channelId}`}
            className={`text-size-xs transition-opacity ${isLoading ? 'pointer-events-none' : ''}`}
          >
            {optimisticSubscribed ? 'Subscribed' : 'Subscribe'}
          </label>
        </div>
      </div>
    </div>
  );
}
