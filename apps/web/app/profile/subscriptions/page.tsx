'use client';

import PageContainer from '@/components/layouts/page-container';
import {
  UserSubscriptionsProps,
  useUserSubscriptions,
} from '@/hooks/query/user/use-user-subscriptions';
import { useToggleChannelSubscription } from '@/hooks/query/channel/use-toggle-channel-subscription';
import { Popup } from '@/components/ui/popup';
import ChannelUnsubscribePopup from '@/components/ui/popup/channel-unsubscribe-popup';
import { useState, useEffect } from 'react';
import SubscribedChannel from './components/subscribed-channel';

// 채널 상태를 관리하기 위한 타입
type ChannelWithOptimisticState = UserSubscriptionsProps['channels'][number] & {
  optimisticSubscribed: boolean;
};

export default function SubscriptionsPage() {
  const { data, refetch } = useUserSubscriptions();
  const toggleSubscription = useToggleChannelSubscription();

  // 별도 상태로 채널 리스트 관리
  const [channels, setChannels] = useState<ChannelWithOptimisticState[]>([]);

  const [unsubscribePopup, setUnsubscribePopup] = useState<{
    isOpen: boolean;
    channelId: string;
    channelName: string;
  }>({
    isOpen: false,
    channelId: '',
    channelName: '',
  });

  // 서버 데이터가 변경될 때 로컬 상태 업데이트
  useEffect(() => {
    if (data?.channels) {
      setChannels(prevChannels => {
        // 기존 optimistic 상태를 유지하면서 서버 데이터와 병합
        const newChannels = data.channels.map(serverChannel => {
          const existingChannel = prevChannels.find(c => c.channelId === serverChannel.channelId);
          return {
            ...serverChannel,
            optimisticSubscribed:
              existingChannel?.optimisticSubscribed ?? serverChannel.isSubscribed,
          };
        });
        return newChannels;
      });
    }
  }, [data?.channels]);

  const handleSubscriptionChange = async (
    channelId: string,
    isSubscribed: boolean,
    channelName: string
  ) => {
    // unsubscribe 시도 시 팝업 노출
    if (!isSubscribed) {
      setUnsubscribePopup({
        isOpen: true,
        channelId,
        channelName,
      });
      return;
    }

    // subscribe 시도 시 optimistic update 적용
    setChannels(prevChannels =>
      prevChannels.map(channel =>
        channel.channelId === channelId ? { ...channel, optimisticSubscribed: true } : channel
      )
    );

    try {
      await toggleSubscription.mutateAsync({
        channelId,
        isCurrentlySubscribed: false, // 현재 unsubscribed 상태에서 subscribe하는 것
      });
      await refetch();
    } catch (error) {
      console.error('Subscription toggle failed:', error);
      // 에러 시 optimistic update 롤백
      setChannels(prevChannels =>
        prevChannels.map(channel =>
          channel.channelId === channelId ? { ...channel, optimisticSubscribed: false } : channel
        )
      );
    }
  };

  const handleConfirmUnsubscribe = async () => {
    // unsubscribe 시 optimistic update 적용 (리스트에서 삭제하지 않고 투명도만 적용)
    setChannels(prevChannels =>
      prevChannels.map(channel =>
        channel.channelId === unsubscribePopup.channelId
          ? { ...channel, optimisticSubscribed: false }
          : channel
      )
    );

    try {
      await toggleSubscription.mutateAsync({
        channelId: unsubscribePopup.channelId,
        isCurrentlySubscribed: true,
      });
      await refetch();
    } catch (error) {
      console.error('Unsubscribe failed:', error);
      // 에러 시 optimistic update 롤백
      setChannels(prevChannels =>
        prevChannels.map(channel =>
          channel.channelId === unsubscribePopup.channelId
            ? { ...channel, optimisticSubscribed: true }
            : channel
        )
      );
    } finally {
      setUnsubscribePopup({ isOpen: false, channelId: '', channelName: '' });
    }
  };

  const handleCancelUnsubscribe = () => {
    setUnsubscribePopup({ isOpen: false, channelId: '', channelName: '' });
  };

  const totalSubscribedChannels = channels.filter(channel => channel.optimisticSubscribed).length;

  return (
    <PageContainer title="Subscribed Channels">
      <div className="flex flex-col">
        <div className="mb-space-30 text-size-sm text-gray-3">
          Channels: <span className="text-mid-dark">{totalSubscribedChannels}</span>
        </div>
        <div className="gap-space-5 flex flex-col">
          {channels.map(channel => (
            <SubscribedChannel
              key={channel.channelId}
              channel={channel}
              optimisticSubscribed={channel.optimisticSubscribed}
              onSubscriptionChange={handleSubscriptionChange}
              isLoading={toggleSubscription.isPending}
            />
          ))}
        </div>
      </div>

      <Popup
        isOpen={unsubscribePopup.isOpen}
        onClose={handleCancelUnsubscribe}
        showCloseButton={false}
      >
        <ChannelUnsubscribePopup
          channelName={unsubscribePopup.channelName}
          onConfirm={handleConfirmUnsubscribe}
          onCancel={handleCancelUnsubscribe}
        />
      </Popup>
    </PageContainer>
  );
}
