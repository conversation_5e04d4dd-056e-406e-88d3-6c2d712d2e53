import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { DollarInput } from '@/components/ui/dollar-input';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';
import { toast } from '../ui/base.toast';

interface AddDepositPopupProps {
  onSubmit: (amount: number) => Promise<void>;
  isPending: boolean;
  isDisabled: boolean;
  balance: {
    value: string;
    type: 'collateral' | 'usdc';
  };
}

export default function AddDepositPopup({
  onSubmit,
  isPending,
  isDisabled,
  balance,
}: AddDepositPopupProps) {
  const availableBalance = balance.value;
  const [amount, setAmount] = useState<number>(0);

  const formatBalance = (balance: number): string => {
    return balance.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const isValidAmount = amount >= 1; // Minimum $1
  const isAboveMinimumValidation = amount >= 1; // Different validation for button enable
  const canAddDeposit = availableBalance ? amount <= Number(availableBalance) : false;

  const handleAmountChange = (value: number) => {
    setAmount(value);
  };

  const handleMaxClick = () => {
    setAmount(Number(availableBalance) || 0);
  };

  const handleConfirm = async () => {
    if (!isValidAmount) return;
    if (!canAddDeposit) {
      toast.error('Channel deposit is not enough.');
      return;
    }
    await onSubmit(amount);
  };

  const balanceText = balance.type === 'collateral' ? 'Deposit Balance' : 'My Balance';
  return (
    <div className={cn('bg-gray-2 pt-space-40 flex flex-col')}>
      {/* Header */}
      <div className="mb-space-50 text-center">
        <h1 className="text-size-base font-bold">Add Deposit</h1>
      </div>

      <div className="gap-space-30 pb-space-40 px-space-25 flex flex-col">
        {/* Amount Input */}
        <div className="gap-space-10 flex flex-col">
          <div className="flex items-center justify-between">
            <label className="text-size-sm text-mid-dark font-semibold">Amount</label>
          </div>

          <div className="relative">
            <DollarInput
              placeholder="Minimum Amount $50"
              value={amount}
              onChange={handleAmountChange}
              maxValue={parseFloat(availableBalance)}
              minValue={50}
              className={cn(
                'w-full pr-16',
                !isValidAmount && amount && 'border-no-red focus:border-no-red',
                !canAddDeposit && amount && 'border-no-red focus:border-no-red'
              )}
            />
            <BaseButton
              variant="neutral"
              size="sm"
              onClick={handleMaxClick}
              className="absolute top-1/2 right-2 h-6 -translate-y-1/2 px-2 text-xs"
            >
              MAX
            </BaseButton>
          </div>
          <div className="text-size-xs text-gray-3">
            {balanceText} ${formatBalance(Number(availableBalance))}
          </div>
        </div>
      </div>
      <InfoButton
        size="md"
        rounded="none"
        onClick={handleConfirm}
        disabled={isDisabled || !isAboveMinimumValidation || !canAddDeposit || !amount}
        loading={isPending}
      >
        {isPending ? 'Processing...' : 'Confirm'}
      </InfoButton>
    </div>
  );
}
