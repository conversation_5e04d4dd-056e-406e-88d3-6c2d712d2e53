import AddDepositPopup from '@/components/actions/add-deposit-popup';
import { InfoButton } from '@/components/ui/base.button';
import { toast } from '@/components/ui/base.toast';
import { useAddDepositFlow } from '@/hooks/query/channel/use-add-deposit-flow';
import { ApiError } from '@/lib/api/base-api.error';
import { formatCurrency } from '@/lib/format';
import { useCallback, useState } from 'react';
import { Popup } from '../ui/popup';
import { useChannelCollateral } from '@/hooks/query/channel';

interface AddDepositButtonProps {
  marketId: string;
}

export default function AddMarketDepositButton({ marketId }: AddDepositButtonProps) {
  const addDepositFlowMutation = useAddDepositFlow(marketId);
  const [isOpen, setIsOpen] = useState(false);
  const { data: collateral } = useChannelCollateral();

  const handleSubmit = useCallback(
    async (amount: number) => {
      try {
        await addDepositFlowMutation.mutateAsync(amount);
        toast.success(`Successfully deposited ${formatCurrency(amount)}!`);
        setIsOpen(false);
      } catch (error) {
        console.error('Failed to deposit:', error);
        if (ApiError.isApiError(error)) {
          toast.error(error.getDisplayMessage());
        } else {
          toast.error('Failed to deposit. Please try again.');
        }
      }
    },
    [addDepositFlowMutation]
  );

  return (
    <>
      <InfoButton
        size="sm"
        className="w-[140px]"
        onClick={() => setIsOpen(true)}
        disabled={addDepositFlowMutation.isPending}
      >
        Add Deposit
      </InfoButton>
      <Popup isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <AddDepositPopup
          isPending={addDepositFlowMutation.isPending}
          isDisabled={addDepositFlowMutation.isPending}
          balance={{
            value: collateral?.available.formatted || '0',
            type: 'collateral',
          }}
          onSubmit={handleSubmit}
        />
      </Popup>
    </>
  );
}
