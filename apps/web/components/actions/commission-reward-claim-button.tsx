import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useCommissionRewardClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface CommissionRewardClaimButtonProps {
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
}

export default function CommissionRewardClaimButton({
  className,
  onSuccess,
  onFailure,
}: CommissionRewardClaimButtonProps) {
  const commissionRewardClaimMutation = useCommissionRewardClaimAndGo();

  const handleClaim = async () => {
    try {
      await commissionRewardClaimMutation.mutateAsync();
    } catch {
      toast.error('Commission reward claim failed');
    }
  };

  useEffect(() => {
    if (commissionRewardClaimMutation.isSuccess) {
      toast.success('Commission reward claim successful');
      onSuccess?.();
    }
    if (commissionRewardClaimMutation.isError) {
      toast.error('Commission reward claim failed');
      onFailure?.();
    }
  }, [
    commissionRewardClaimMutation.isSuccess,
    commissionRewardClaimMutation.isError,
    onSuccess,
    onFailure,
  ]);

  const isDisabled = commissionRewardClaimMutation.isPending;

  return (
    <InfoButton onClick={handleClaim} disabled={isDisabled} size="sm" className={cn(className)}>
      {commissionRewardClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
