import { useDepositChannelCollateralAndGo } from '@/hooks/query/predict';
import { ApiError } from '@/lib/api/base-api.error';
import { useState } from 'react';
import { BaseButton, ButtonCustomVariant } from '../ui/base.button';
import { toast } from '../ui/base.toast';
import { Popup } from '../ui/popup';
import AddDepositPopup from './add-deposit-popup';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';

interface DepositChannelCollateralButtonProps {
  children: React.ReactNode;
  isDisabled?: boolean;
  variant?: ButtonCustomVariant;
  style?: React.CSSProperties;
  ref?: React.RefObject<HTMLButtonElement | null>;
}

export default function DepositChannelCollateralButton({
  children,
  variant = 'info',
  isDisabled = false,
  style,
  ref,
}: DepositChannelCollateralButtonProps) {
  const [isAddDepositPopupOpen, setIsAddDepositPopupOpen] = useState(false);
  const depositMutation = useDepositChannelCollateralAndGo();
  const { balance } = useMyUSDCBalance();

  const onSubmit = async (amount: number) => {
    try {
      await depositMutation.mutateAsync({ amount: amount.toString() });
      setIsAddDepositPopupOpen(false);
      toast.success('Successfully deposited!');
    } catch (error) {
      console.error('Failed to deposit:', error);
      if (ApiError.isApiError(error)) {
        toast.error(error.getDisplayMessage());
      } else {
        toast.error('Failed to deposit. Please try again.');
      }
    }
  };

  return (
    <>
      <BaseButton
        variant={variant}
        disabled={depositMutation.isPending || isDisabled}
        onClick={e => {
          e.preventDefault();
          setIsAddDepositPopupOpen(true);
        }}
        style={style}
        ref={ref}
      >
        {children}
      </BaseButton>
      <Popup isOpen={isAddDepositPopupOpen} onClose={() => setIsAddDepositPopupOpen(false)}>
        <AddDepositPopup
          balance={{
            value: balance,
            type: 'usdc',
          }}
          onSubmit={onSubmit}
          isPending={depositMutation.isPending}
          isDisabled={depositMutation.isPending}
        />
      </Popup>
    </>
  );
}
