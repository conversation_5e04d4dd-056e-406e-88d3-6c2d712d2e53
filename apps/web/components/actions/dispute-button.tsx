import { Popup } from '@/components/ui/popup';
import OpenDisputePopupBody from '@/components/ui/popup/open-dispute-popup';
import { useDepositDisputeCollateralAndGo } from '@/hooks/query/predict';
import { ApiError } from '@/lib/api/base-api.error';
import { DepositDisputeCollateralRequestBody } from '@/lib/api/predict/predict.schema';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { useState } from 'react';
import { ButtonCustomSize, InfoButton } from '../ui/base.button';
import { toast } from '../ui/base.toast';

interface DisputeButtonProps {
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
  disputeAmountBase: string;
  maxAmount: number;
  marketId: string;
}

export default function DisputeButton({
  size,
  textSize,
  disputeAmountBase,
  maxAmount,
  marketId,
}: DisputeButtonProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const depositDisputeCollateralMutation = useDepositDisputeCollateralAndGo(marketId);

  const handleClick = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const handleSubmit = async (data: {
    amount: number;
    description?: string;
    referenceUrl?: string;
    files?: File[];
  }) => {
    try {
      const requestData: DepositDisputeCollateralRequestBody = {
        marketId,
        amount: data.amount.toString(),
        description: data.description,
        referenceURL: data.referenceUrl,
        files: data.files,
      };
      await depositDisputeCollateralMutation.mutateAsync(requestData);
      toast.success('Dispute opened successfully');
      closePopup();
    } catch (error) {
      console.error('Failed to open dispute:', error);
      if (ApiError.isApiError(error)) {
        toast.error(error.getDisplayMessage());
      } else {
        toast.error('Failed to open dispute');
      }
    }
  };

  return (
    <>
      <InfoButton
        fontSize={textSize}
        className="w-full"
        onClick={handleClick}
        disabled={depositDisputeCollateralMutation.isPending}
        size={size}
      >
        Open Dispute
      </InfoButton>

      <Popup isOpen={isPopupOpen} onClose={closePopup}>
        <OpenDisputePopupBody
          currentDisputeAmount={maxAmount}
          disputeAmountBase={disputeAmountBase}
          isPending={depositDisputeCollateralMutation.isPending}
          onClose={closePopup}
          onSubmit={handleSubmit}
        />
      </Popup>
    </>
  );
}
