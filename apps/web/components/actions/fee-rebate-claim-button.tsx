import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useFeeRebateClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface FeeRebateClaimButtonProps {
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
}

export default function FeeRebateClaimButton({
  className,
  onSuccess,
  onFailure,
}: FeeRebateClaimButtonProps) {
  const feeRebateClaimMutation = useFeeRebateClaimAndGo();

  const handleClaim = async () => {
    try {
      await feeRebateClaimMutation.mutateAsync();
    } catch {
      toast.error('Fee rebate claim failed');
    }
  };

  useEffect(() => {
    if (feeRebateClaimMutation.isSuccess) {
      toast.success('Fee rebate claim successful');
      onSuccess?.();
    }
    if (feeRebateClaimMutation.isError) {
      toast.error('Fee rebate claim failed');
      onFailure?.();
    }
  }, [feeRebateClaimMutation.isSuccess, feeRebateClaimMutation.isError, onSuccess, onFailure]);

  const isDisabled = feeRebateClaimMutation.isPending;

  return (
    <InfoButton onClick={handleClaim} disabled={isDisabled} size="sm" className={cn(className)}>
      {feeRebateClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
