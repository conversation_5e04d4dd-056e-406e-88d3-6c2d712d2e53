import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useMarketClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface MarketClaimButtonProps {
  className?: string;
  disabled?: boolean;
  onSuccess?: () => void;
  onFailure?: () => void;
}

export default function MarketClaimButton({
  className,
  disabled,
  onSuccess,
  onFailure,
}: MarketClaimButtonProps) {
  const marketClaimMutation = useMarketClaimAndGo();

  const handleClaim = async () => {
    try {
      await marketClaimMutation.mutateAsync();
    } catch {
      toast.error('Market claim failed');
    }
  };

  useEffect(() => {
    if (marketClaimMutation.isSuccess) {
      toast.success('Market claim successful');
      onSuccess?.();
    }
    if (marketClaimMutation.isError) {
      toast.error('Market claim failed');
      onFailure?.();
    }
  }, [marketClaimMutation.isSuccess, marketClaimMutation.isError, onSuccess, onFailure]);

  const isDisabled = marketClaimMutation.isPending;

  return (
    <InfoButton
      onClick={handleClaim}
      disabled={isDisabled || disabled}
      size="sm"
      className={cn(className)}
    >
      {marketClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
