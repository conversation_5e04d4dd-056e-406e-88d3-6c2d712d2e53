import { usePredictAndGo } from '@/hooks/query/predict';
import { ButtonCustomSize, GreenButton } from '../ui/base.button';
import { cn } from '@repo/ui/lib/utils';
import { useEffect, useCallback } from 'react';
import { toast } from '../ui/base.toast';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { ApiError } from '@/lib/api/base-api.error';
import { useAuthGuard } from '@/hooks/use-auth-guard';

interface PredictButtonProps {
  marketId: string;
  outcome: string | undefined;
  amount: number;
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
  estimatedOdds?: string;
}

export default function PredictButton({
  marketId,
  outcome,
  amount,
  className,
  onSuccess,
  onFailure,
  size,
  textSize,
  estimatedOdds,
}: PredictButtonProps) {
  const text = `Predict ${outcome}`;
  const predictAndGoMutation = usePredictAndGo();
  const { withAuthCheck } = useAuthGuard();

  const handleSuccess = useCallback(() => {
    toast.success('Predict success');
    onSuccess?.();
  }, [onSuccess]);

  const handleFailure = useCallback(() => {
    const error = predictAndGoMutation.error;
    let errorMessage = 'Predict failed';

    if (ApiError.isApiError(error)) {
      errorMessage = error.getDisplayMessage();
    } else if (error?.message) {
      errorMessage = error.message;
    }

    toast.error(errorMessage);
    onFailure?.();
  }, [predictAndGoMutation.error, onFailure]);

  const handlePredict = withAuthCheck(async () => {
    if (!outcome || !amount || predictAndGoMutation.isPending) {
      return;
    }

    await predictAndGoMutation.mutateAsync({ marketId, outcome, amount: amount.toString() });
  });

  useEffect(() => {
    if (predictAndGoMutation.isSuccess) {
      handleSuccess();
    }
    if (predictAndGoMutation.isError) {
      handleFailure();
    }
  }, [predictAndGoMutation.isSuccess, predictAndGoMutation.isError, handleSuccess, handleFailure]);

  const isDisabled = predictAndGoMutation.isPending || !amount || !outcome;
  // const estimatedOdss =
  return (
    <GreenButton
      fontSize={textSize}
      className={cn('h-full w-full', className)}
      onClick={handlePredict}
      disabled={isDisabled}
      size={size}
    >
      <div className="truncate">
        {text} <br />
        {`(x ${estimatedOdds})`}
      </div>
    </GreenButton>
  );
}
