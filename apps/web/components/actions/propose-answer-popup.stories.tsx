import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import * as React from 'react';
import ProposeAnswerPopup from './propose-answer-popup';
import { DarkButton } from '../ui/base.button';
import { Popup } from '../ui/popup';
import { WagmiContextProvider } from '../providers/wagmi-provider';
import { TanstackQueryClientProvider } from '../providers/tanstack-query-client-provider';

const meta: Meta<typeof ProposeAnswerPopup> = {
  title: 'Components/actions/ProposeAnswerPopup',
  component: ProposeAnswerPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    Story => (
      <WagmiContextProvider cookies={null}>
        <TanstackQueryClientProvider>
          <Story />
        </TanstackQueryClientProvider>
      </WagmiContextProvider>
    ),
  ],
  argTypes: {
    marketId: {
      control: 'text',
      description: 'The ID of the market',
    },
    isPending: {
      control: 'boolean',
      description: '제출 진행 상태',
    },
    onConfirm: {
      action: 'confirmed',
      description: '확인 액션 핸들러',
    },
    onCancel: {
      action: 'canceled',
      description: '취소 액션 핸들러',
    },
  },
};

export default meta;

type Story = StoryObj<typeof ProposeAnswerPopup>;

const mockOutcomes = [{ outcome: 'Yes' }, { outcome: 'No' }];

const renderStory = (args: any, mockValue: any) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <>
      <DarkButton onClick={() => setIsOpen(true)}>Open Propose Answer Popup</DarkButton>
      <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton>
        <ProposeAnswerPopup
          {...args}
          onConfirm={outcome => {
            console.log('Confirmed:', outcome);
            args.onConfirm(outcome);
            setIsOpen(false);
          }}
          onCancel={() => {
            args.onCancel();
            setIsOpen(false);
          }}
        />
      </Popup>
    </>
  );
};

export const Default: Story = {
  render: args =>
    renderStory(args, {
      outcomes: mockOutcomes,
      isLoading: false,
    }),
  args: {
    marketId: '1',
    isPending: false,
  },
};

export const Loading: Story = {
  render: args =>
    renderStory(args, {
      outcomes: [],
      isLoading: true,
    }),
  args: {
    marketId: '1',
    isPending: false,
  },
};

export const Proposing: Story = {
  render: args =>
    renderStory(args, {
      outcomes: mockOutcomes,
      isLoading: false,
    }),
  args: {
    marketId: '1',
    isPending: true,
  },
};

export const EmptyOutcomes: Story = {
  render: args =>
    renderStory(args, {
      outcomes: [],
      isLoading: false,
    }),
  args: {
    marketId: '1',
    isPending: false,
  },
};
