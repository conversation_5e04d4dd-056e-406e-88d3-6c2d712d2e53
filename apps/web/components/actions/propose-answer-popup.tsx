import { DarkButton, InfoButton } from '@/components/ui/base.button';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useMarketOutcomes } from '@/hooks/query/market';
import { Skeleton } from '@repo/ui/components/skeleton';
import { useState } from 'react';

interface ProposeAnswerPopupProps {
  marketId: string;
  isPending?: boolean;
  onConfirm: (outcome: string) => void;
  onCancel: () => void;
}

export default function ProposeAnswerPopup({
  marketId,
  isPending = false,
  onConfirm,
  onCancel,
}: ProposeAnswerPopupProps) {
  const [selectedOutcome, setSelectedOutcome] = useState<string>('');
  const { outcomes, isLoading } = useMarketOutcomes(marketId);

  const handleConfirm = () => {
    if (selectedOutcome) {
      onConfirm(selectedOutcome);
    }
  };

  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        <svg
          width="50"
          height="50"
          viewBox="0 0 50 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="50" height="50" rx="25" fill="#22C55E" />
          <path
            d="M37 18L22 33L13 24"
            stroke="white"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm font-semibold">Propose Answer</h1>
          <p className="text-size-xs text-gray-3">Select the correct answer for this prediction</p>
        </div>
      </div>

      {/* Select Outcome */}
      <div className="px-space-30 pb-space-30">
        <div className="space-y-space-15">
          <label className="text-size-sm font-semibold">Selected Predictions</label>
          <BaseSelect
            value={selectedOutcome}
            onValueChange={setSelectedOutcome}
            disabled={isLoading || isPending}
          >
            <BaseSelectTrigger className="w-full">
              <BaseSelectValue placeholder="Select an outcome" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {isLoading ? (
                <BaseSelectItem value="loading" disabled>
                  Loading outcomes...
                </BaseSelectItem>
              ) : (
                <>
                  {outcomes?.map(outcome => (
                    <BaseSelectItem key={outcome.outcome} value={outcome.outcome}>
                      {outcome.outcome}
                    </BaseSelectItem>
                  ))}
                  <BaseSelectItem value="Void">Void</BaseSelectItem>
                </>
              )}
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton
          onClick={onCancel}
          size="lg"
          fontSize="xs"
          rounded="none"
          className="flex-1"
          disabled={isPending}
        >
          Cancel
        </DarkButton>
        <InfoButton
          onClick={handleConfirm}
          size="lg"
          fontSize="xs"
          rounded="none"
          className="flex-1"
          disabled={isPending || !selectedOutcome}
        >
          {isPending ? 'Proposing...' : 'Confirm'}
        </InfoButton>
      </div>
    </div>
  );
}
