import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useShareClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface ShareClaimButtonProps {
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
  disabled?: boolean;
}

export default function ShareClaimButton({
  className,
  onSuccess,
  onFailure,
  disabled,
}: ShareClaimButtonProps) {
  const shareClaimMutation = useShareClaimAndGo();

  const handleClaim = async () => {
    await shareClaimMutation.mutateAsync();
  };

  useEffect(() => {
    if (shareClaimMutation.isSuccess) {
      toast.success('Share claim successful');
      onSuccess?.();
    }
    if (shareClaimMutation.isError) {
      toast.error('Share claim failed');
      onFailure?.();
    }
  }, [shareClaimMutation.isSuccess, shareClaimMutation.isError, onSuccess, onFailure]);

  const isDisabled = shareClaimMutation.isPending;

  return (
    <InfoButton
      onClick={handleClaim}
      disabled={isDisabled || disabled}
      size="sm"
      className={cn(className)}
    >
      {shareClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
