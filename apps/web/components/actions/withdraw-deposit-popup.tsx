import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { DollarInput } from '@/components/ui/dollar-input';
import { useChannelCollateral } from '@/hooks/query/channel';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';

interface WithdrawDepositPopupProps {
  onSubmit: (amount: number) => void;
  isPending: boolean;
  isDisabled: boolean;
}

export default function WithdrawDepositPopup({
  onSubmit,
  isPending,
  isDisabled,
}: WithdrawDepositPopupProps) {
  const { data: collateralData } = useChannelCollateral();
  const available = collateralData?.available.formatted;
  const [amount, setAmount] = useState<number>(0);
  const isValidAmount = amount >= 1;
  const isAboveMinimumValidation = amount >= 1;
  const canAfford = amount <= Number(available);

  const handleAmountChange = (value: number) => {
    setAmount(value);
  };

  const handleMaxClick = () => {
    setAmount(Number(available) || 0);
  };

  const handleConfirm = async () => {
    if (!isValidAmount || !canAfford) return;
    onSubmit(amount);
  };

  return (
    <div className={cn('bg-gray-2 pt-space-40 flex flex-col')}>
      {/* Header */}
      <div className="mb-space-50 text-center">
        <h1 className="text-size-base font-bold">Withdraw Deposit</h1>
      </div>

      {/* Content */}
      <div className="gap-space-30 pb-space-40 px-space-25 flex flex-col">
        {/* Amount Input */}
        <div className="gap-space-10 flex flex-col">
          <div className="flex items-center justify-between">
            <label className="text-size-sm text-mid-dark font-semibold">Amount</label>
          </div>

          <div className="relative">
            <DollarInput
              placeholder="$0"
              value={amount}
              onChange={handleAmountChange}
              maxValue={Number(available)}
              minValue={0}
              className={cn(
                'w-full pr-16',
                !isValidAmount && amount && 'border-no-red focus:border-no-red',
                !canAfford && amount && 'border-no-red focus:border-no-red'
              )}
            />
            <BaseButton
              variant="neutral"
              size="sm"
              onClick={handleMaxClick}
              className="absolute top-1/2 right-2 h-6 -translate-y-1/2 px-2 text-xs"
            >
              MAX
            </BaseButton>
          </div>
          <div className="text-size-xs text-gray-3">Available ${available}</div>
        </div>
      </div>
      <InfoButton
        size="md"
        rounded="none"
        onClick={handleConfirm}
        disabled={isDisabled || !isAboveMinimumValidation || !canAfford || !amount}
        loading={isPending}
      >
        {isPending ? 'Processing...' : 'Confirm'}
      </InfoButton>
    </div>
  );
}
