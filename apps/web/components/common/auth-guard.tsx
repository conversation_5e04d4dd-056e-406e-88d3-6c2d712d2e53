'use client';

import { useEffect } from 'react';
import { redirect } from 'next/navigation';
import { useGlobalStore } from '@/store/global.store';
import { useCurrentUser } from '@/hooks/query/user';
import { Skeleton } from '@repo/ui/components/skeleton';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 인증된 사용자만 접근할 수 있도록 보호하는 컴포넌트
 * 미인증 사용자는 홈페이지로 리다이렉트됩니다.
 */
export function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { isSignedIn } = useGlobalStore();
  const { isLoading } = useCurrentUser();

  useEffect(() => {
    // 로딩이 완료되고 로그인되지 않은 상태라면 홈으로 리다이렉트
    if (!isLoading && !isSignedIn) {
      redirect('/');
    }
  }, [isLoading, isSignedIn]);

  // 로딩 중이거나 인증되지 않은 경우 fallback 렌더링
  if (isLoading || !isSignedIn) {
    return (
      fallback || (
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="space-y-4">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}
