'use client';

import { Component, ReactNode } from 'react';
import Image from 'next/image';
import { cn } from '@repo/ui/lib/utils';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  className?: string;
  fallback?: ReactNode;
  title?: string;
  description?: string;
  iconSrc?: string;
  iconSize?: number;
  showRetryButton?: boolean;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onRetry?: () => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo);
    this.setState({ errorInfo });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    this.props.onRetry?.();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const {
        title = 'Failed to load markets',
        description = 'Something went wrong while loading the prediction markets.',
        iconSrc = '/icons/alert.svg',
        iconSize = 48,
        showRetryButton = false,
        className,
      } = this.props;

      return (
        <div className={cn('flex flex-1 items-center justify-center p-6', className)}>
          <div className="flex flex-col items-center text-center">
            <Image
              src={iconSrc}
              alt="Error"
              width={iconSize}
              height={iconSize}
              className="mb-4 opacity-50"
            />
            <div className="mb-2 text-lg font-semibold text-gray-700">{title}</div>
            <div className="mb-4 text-sm text-gray-500">{description}</div>
            {showRetryButton && (
              <button
                onClick={this.handleRetry}
                className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
