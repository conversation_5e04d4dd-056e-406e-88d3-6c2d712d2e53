import { useCountdown } from '@/hooks/useCountdown';
import { padNumber } from '@/lib/utils';
import { cn } from '@repo/ui/lib/utils';

interface MarketCountdownProps {
  endTime: number; // 밀리세컨즈 타임스탬프
  onComplete?: () => void;
  className?: string;
}

interface CountdownItemProps {
  value: number;
  label: string;
  isLast?: boolean;
}

interface CountdownContainerProps {
  children: React.ReactNode;
  className?: string;
}

const CountdownContainer = ({ children, className }: CountdownContainerProps) => (
  <div
    className={cn(
      'bg-gray-2 text-size-xxs border-line rounded-round-sm flex h-[26px] border',
      className
    )}
  >
    {children}
  </div>
);

const CountdownItem = ({ value, label, isLast = false }: CountdownItemProps) => (
  <div
    className={cn(
      'flex w-[35px] flex-col items-center justify-between',
      !isLast && 'border-r-line border-r' // 마지막 항목이 아니면 우측 경계선 표시
    )}
  >
    <span className="leading-[14px] font-medium">{padNumber(value)}</span>
    <span className="text-size-xxs8 text-muted-foreground leading-[10px]">
      {value === 1 ? label.slice(0, -1) : label}
    </span>
  </div>
);

/**
 * 마켓 카운트다운 컴포넌트
 * 일이 0이면 시/분/초, 일이 0보다 크면 일/시/분을 표시합니다.
 */
export const MarketCountdown = ({ endTime, onComplete, className }: MarketCountdownProps) => {
  const { remainingTime } = useCountdown(endTime, { onComplete });

  if (!remainingTime) {
    return (
      <CountdownContainer className={className}>
        <CountdownItem value={0} label="hours" />
        <CountdownItem value={0} label="mins" />
        <CountdownItem value={0} label="secs" isLast />
      </CountdownContainer>
    );
  }

  const { days, hours, minutes, seconds } = remainingTime;
  const showDays = days > 0;

  if (showDays) {
    return (
      <CountdownContainer className={className}>
        <CountdownItem value={days} label="days" />
        <CountdownItem value={hours} label="hours" />
        <CountdownItem value={minutes} label="mins" isLast />
      </CountdownContainer>
    );
  }

  // 일이 0이면 시/분/초 표시
  return (
    <CountdownContainer className={className}>
      <CountdownItem value={hours} label="hours" />
      <CountdownItem value={minutes} label="mins" />
      <CountdownItem value={seconds} label="secs" isLast />
    </CountdownContainer>
  );
};

export default MarketCountdown;
