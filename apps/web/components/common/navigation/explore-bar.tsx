'use client';

import { usePathname } from 'next/navigation';
import SvgIcon from '@/components/icons/svg-icon';
import { useGlobalStore } from '@/store/global.store';
import { TrendingKeywords } from './trending-keywords';
import { CategorySwiper } from './category-swiper';
import { MarketBriefSearch } from './market-brief-search';

export const ExploreBar = () => {
  const pathname = usePathname();
  const isMarketsPage = pathname === '/markets';
  const toggleAside = useGlobalStore(state => state.toggleAside);

  return (
    <div className="bg-gray-2 relative flex h-[var(--sub-nav-height)] w-full flex-1 flex-col">
      <div className="flex h-full w-full">
        {/* Left - Category */}
        <CategorySwiper />

        {/* Center - Search */}
        <MarketBriefSearch />

        {/* Right - Trending & Filter */}
        <div className="bg-gray-2 gap-space-10 px-space-20 flex h-full w-[340px] items-center justify-between">
          <TrendingKeywords />
          {isMarketsPage && (
            <button
              onClick={toggleAside}
              className="border-sky flex size-[30px] items-center justify-center rounded-sm border bg-white"
            >
              <SvgIcon name="AudioSettingsIcon" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
