import Link from 'next/link';
import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@repo/ui/lib/utils';
import { LgIcon, LgIconName } from '../../icons/lg-icon';
import PredictGoLogo from '../../icons/predict-go-logo';
import { INNER_LINKS } from '@/lib/constants';
import { toast } from '@/components/ui/base.toast';

interface NavigationItem {
  href: string;
  label: string;
  icon?: LgIconName;
  subMenuItems: SubMenuItem[];
  badge?: string;
}

interface SubMenuItem {
  label: string;
  href: string;
  icon?: LgIconName;
  isComingSoon?: boolean;
}

const SUB_MENUS = {
  markets: [
    {
      label: 'Category Markets',
      href: INNER_LINKS.MAIN.MARKETS.ROOT,
      icon: 'category' as LgIconName,
    },
    { label: 'All Markets', href: INNER_LINKS.MAIN.MARKETS.ALL, icon: 'market' as LgIconName },
  ],
  arcade: [
    { label: 'Ranks', href: INNER_LINKS.MAIN.ARCADE.RANKS, icon: 'ranks' as LgIconName },
    { label: 'Activity', href: INNER_LINKS.MAIN.ARCADE.ACTIVITY, icon: 'activity' as LgIconName },
    {
      label: 'Expert lounge',
      href: '/arcade/expert-lounge',
      icon: 'expert_lounge' as LgIconName,
      isComingSoon: true,
    },
    {
      label: 'Weekly Quiz',
      href: '/arcade/weekly-quiz',
      icon: 'weekly_quiz' as LgIconName,
      isComingSoon: true,
    },
  ],
  rewards: [
    { label: 'Referral', href: INNER_LINKS.MAIN.REWARDS.REFERRAL, icon: 'referral' as LgIconName },
    {
      label: 'Share Bonus',
      href: INNER_LINKS.MAIN.REWARDS.SHARE_BONUS,
      icon: 'share_bonus' as LgIconName,
    },
  ],
};

export const NAVS = [
  {
    href: INNER_LINKS.MAIN.MARKETS.ALL,
    label: 'Markets',
    icon: 'market' as LgIconName,
    subMenuItems: [],
  },
  {
    href: INNER_LINKS.MAIN.ARCADE.RANKS,
    label: 'Arcade',
    icon: 'ranks' as LgIconName,
    subMenuItems: SUB_MENUS.arcade,
  },
  {
    href: INNER_LINKS.MAIN.REWARDS.REFERRAL,
    label: 'Rewards',
    icon: 'bonus_reward' as LgIconName,
    subMenuItems: SUB_MENUS.rewards,
  },
  {
    href: '/insights',
    label: 'Insights',
    icon: 'insight' as LgIconName,
    subMenuItems: [],
  },
  // {
  //   href: EXTERNAL_LINKS.DOCUMENTATION,
  //   label: 'Docs',
  //   icon: 'documentation' as LgIconName,
  //   subMenuItems: [],
  // },
];

interface NavMenuItemProps {
  href: string;
  label: string;
  icon?: LgIconName;
  badge?: string;
  isActive?: boolean;
  onMouseEnter?: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseLeave?: () => void;
}

function NavMenuItem({
  href,
  label,
  icon,
  badge,
  isActive,
  onMouseEnter,
  onMouseLeave,
}: NavMenuItemProps) {
  return (
    <div className="relative" onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
      <Link
        href={href}
        className={cn(
          'gap-space-8 hover:bg-gray-1 px-space-10 relative flex items-center rounded-sm py-[5px] text-sm font-bold transition-colors',
          isActive && 'bg-gray-1'
        )}
      >
        {icon && <LgIcon className="size-[24px]" name={icon} alt="" />}
        {label}
        {badge && (
          <span className="ml-1 rounded-full bg-red-500 px-1.5 py-0.5 text-xs text-white">
            {badge}
          </span>
        )}
      </Link>
    </div>
  );
}

interface SubNavigationProps {
  isVisible: boolean;
  items: SubMenuItem[];
  offsetLeft: number;
}

function SubNavigation({ isVisible, items, offsetLeft }: SubNavigationProps) {
  const handleComingSoonClick = (e: React.MouseEvent, label: string) => {
    e.preventDefault();
    toast.info('Coming Soon !!');
  };

  return (
    <div
      className={`bg-gray-2 absolute top-[var(--nav-height)] right-0 left-0 z-20 overflow-hidden border-b border-gray-200 transition-all duration-300 ease-in-out ${
        isVisible
          ? 'h-[var(--sub-nav-height)] translate-y-0 transform opacity-100'
          : 'h-0 -translate-y-2 transform opacity-0'
      }`}
    >
      <div
        className="flex h-[var(--sub-nav-height)] items-center"
        style={{ paddingLeft: `${offsetLeft}px` }}
      >
        {isVisible &&
          items.map(subItem =>
            subItem.isComingSoon ? (
              <button
                key={subItem.href}
                onClick={e => handleComingSoonClick(e, subItem.label)}
                className="text-dark text-size-sm py-space-6 px-space-12 hover:bg-gray-1 mr-4 flex cursor-pointer items-center gap-2 rounded-sm font-semibold transition-colors duration-200"
              >
                {subItem.icon && (
                  <span className="border-line rounded-sm border-[1px] bg-white px-[2px] py-[1px]">
                    <LgIcon size={20} name={subItem.icon} alt="" />
                  </span>
                )}
                {subItem.label}
              </button>
            ) : (
              <Link
                key={subItem.href}
                href={subItem.href}
                className="text-dark text-size-sm py-space-6 px-space-12 hover:bg-gray-1 mr-4 flex items-center gap-2 rounded-sm font-semibold transition-colors duration-200"
              >
                {subItem.icon && (
                  <span className="border-line rounded-sm border-[1px] bg-white px-[2px] py-[1px]">
                    <LgIcon size={20} name={subItem.icon} alt="" />
                  </span>
                )}
                {subItem.label}
              </Link>
            )
          )}
      </div>
    </div>
  );
}

interface MainNavigationProps {
  navItems?: NavigationItem[];
  children?: React.ReactNode;
}

export function MainNavigation({ navItems = NAVS, children }: MainNavigationProps) {
  const [activeSubMenu, setActiveSubMenu] = useState<string | null>(null);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [subMenuOffset, setSubMenuOffset] = useState<number>(0);
  const menuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  const handleMenuHover = (menuKey: string, event: React.MouseEvent<HTMLDivElement>) => {
    // 기존 타이머가 있다면 취소
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }

    const menu = navItems.find(nav => nav.href.includes(menuKey));
    if (menu && menu.subMenuItems.length > 0) {
      // 실제 메뉴 요소의 위치 계산
      const menuElement = event.currentTarget;
      if (menuElement) {
        const rect = menuElement.getBoundingClientRect();
        setSubMenuOffset(rect.left);
      }
      setActiveSubMenu(menuKey);
    }
  };

  const handleMenuLeave = () => {
    // 즉시 숨기지 않고 300ms 후에 숨기기
    const timeout = setTimeout(() => {
      setActiveSubMenu(null);
    }, 300);
    setHideTimeout(timeout);
  };

  const handleNavEnter = () => {
    // 네비게이션 영역에 마우스가 들어오면 타이머 취소
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
  };

  // 컴포넌트 언마운트 시 타이머 정리
  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  const currentSubMenuItems = activeSubMenu
    ? navItems.find(nav => nav.href.includes(activeSubMenu))?.subMenuItems || []
    : [];

  return (
    <div className="flex h-full" onMouseLeave={handleMenuLeave} onMouseEnter={handleNavEnter}>
      <div className="flex gap-[50px]">
        {/* Logo */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center gap-1">
            <PredictGoLogo />
          </Link>
        </div>

        {/* Main Nav Items */}
        <nav className="flex items-center">
          <div className="flex gap-[20px]">
            {navItems.map(nav => (
              <NavMenuItem
                key={nav.href}
                href={nav.href}
                label={nav.label}
                icon={nav.icon}
                badge={nav.badge}
                isActive={
                  activeSubMenu === nav.href.replace('/', '') && nav.subMenuItems.length > 0
                }
                onMouseEnter={e => {
                  menuRefs.current[nav.href.replace('/', '')] = e.currentTarget;
                  handleMenuHover(nav.href.replace('/', ''), e);
                }}
              />
            ))}
          </div>
        </nav>

        {children}
      </div>

      {/* Sub Navigation */}
      <SubNavigation
        isVisible={!!activeSubMenu}
        items={currentSubMenuItems}
        offsetLeft={subMenuOffset}
      />
    </div>
  );
}
