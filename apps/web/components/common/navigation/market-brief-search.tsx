'use client';

import { SearchIcon } from 'lucide-react';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useSearchMarkets } from '@/hooks/query/market/use-search-markets';
import { useRouter } from 'next/navigation';
import { INNER_LINKS } from '@/lib/constants';

interface MarketSearchResult {
  id: string;
  title: string;
  imageUrl?: string;
}

export function MarketBriefSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const searchRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const { data: searchResults, isLoading, isFetching } = useSearchMarkets(searchQuery);
  const router = useRouter();

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setSearchQuery(newValue);
    setFocusedIndex(-1);

    if (newValue.length >= 2) {
      setIsTyping(true);
      setIsDropdownOpen(true);
    } else {
      setIsTyping(false);
      setIsDropdownOpen(false);
    }
  }, []);

  const handleMarketSelect = useCallback(
    (market: MarketSearchResult) => {
      setSearchQuery(market.title);
      setIsDropdownOpen(false);
      setIsTyping(false);
      setFocusedIndex(-1);
      router.push(INNER_LINKS.MAIN.MARKETS.DETAIL(market.id));
    },
    [router]
  );

  const handleInputFocus = useCallback(() => {
    if (searchQuery.length >= 2) {
      setIsDropdownOpen(true);
    }
  }, [searchQuery]);

  const handleInputBlur = useCallback((event: React.FocusEvent) => {
    // 드롭다운 내부를 클릭한 경우에는 닫지 않음
    if (searchRef.current?.contains(event.relatedTarget as Node)) {
      return;
    }
    setTimeout(() => {
      setIsDropdownOpen(false);
      setIsTyping(false);
    }, 150);
  }, []);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    const markets = searchResults?.markets;
    if (!isDropdownOpen || !markets || markets.length === 0) {
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setFocusedIndex(prev => (prev + 1) % markets.length);
        break;
      case 'ArrowUp':
        event.preventDefault();
        setFocusedIndex(prev => (prev - 1 + markets.length) % markets.length);
        break;
      case 'Enter':
        event.preventDefault();
        if (focusedIndex >= 0) {
          const market = markets[focusedIndex];
          if (market) {
            handleMarketSelect(market);
          }
        } else {
          // 포커스가 없는 상태에서 엔터: 전체 검색 페이지로 이동
          setIsDropdownOpen(false);
          setIsTyping(false);
          router.push(`/markets/all?keyword=${encodeURIComponent(searchQuery)}`);
        }
        break;
      default:
        break;
    }
  };

  // 검색 상태 관리 - 단일 useEffect로 통합
  useEffect(() => {
    if (searchQuery.length >= 2) {
      if (!isLoading && !isFetching && isTyping) {
        setIsTyping(false);
      }
    } else {
      setIsTyping(false);
      setIsDropdownOpen(false);
    }
  }, [searchQuery, isLoading, isFetching, isTyping]);

  // ESC 키 핸들링
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsDropdownOpen(false);
        setIsTyping(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  useEffect(() => {
    if (searchResults?.markets) {
      itemRefs.current = itemRefs.current.slice(0, searchResults.markets.length);
    }
  }, [searchResults?.markets]);

  useEffect(() => {
    if (focusedIndex >= 0 && itemRefs.current[focusedIndex]) {
      itemRefs.current[focusedIndex]?.scrollIntoView({
        block: 'nearest',
      });
    }
  }, [focusedIndex]);

  // 상태 계산을 useMemo로 최적화
  const { showLoading, showNoResults, showResults } = useMemo(() => {
    const loading = isTyping || isLoading || isFetching;
    const hasResults = (searchResults?.markets?.length ?? 0) > 0;
    const searchCompleted = !loading && searchQuery.length >= 2;

    return {
      showLoading: loading,
      showNoResults: searchCompleted && !hasResults,
      showResults: searchCompleted && hasResults,
    };
  }, [isTyping, isLoading, isFetching, searchResults?.markets?.length, searchQuery.length]);

  return (
    <div className="relative flex-1" ref={searchRef}>
      <div className="bg-gray-1 flex h-full w-full items-center rounded-none border-none px-3 ring-0 outline-0">
        <SearchIcon className="mr-2 text-gray-400" />
        <input
          type="text"
          placeholder="Search by predict"
          className="text-size-xs flex-1 border-none bg-transparent ring-0 outline-none"
          value={searchQuery}
          onChange={handleSearchChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
        />
      </div>

      {/* 검색 결과 드롭다운 */}
      {isDropdownOpen && searchQuery.length >= 2 && (
        <div className="bg-gray-2 absolute top-full right-0 left-0 z-50 max-h-96 overflow-y-auto shadow-lg">
          {showLoading && (
            <div className="flex items-center gap-2 px-4 py-3 text-sm text-gray-500">
              <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-500"></div>
              Searching...
            </div>
          )}

          {showNoResults && <div className="px-4 py-3 text-sm text-gray-500">No results found</div>}

          {showResults &&
            searchResults?.markets?.map((market, index) => (
              <button
                key={market.id}
                ref={el => {
                  itemRefs.current[index] = el;
                }}
                className={`flex w-full items-center gap-3 px-4 py-3 text-left transition-colors ${
                  index === focusedIndex ? 'bg-gray-100' : 'hover:bg-gray-100'
                }`}
                onClick={() => handleMarketSelect(market)}
                onMouseMove={() => setFocusedIndex(index)}
              >
                {/* 마켓 아바타 이미지 */}
                <div className="flex-shrink-0">
                  {market.imageUrl ? (
                    <img
                      src={market.imageUrl}
                      alt={market.title}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                  ) : (
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full transition-colors ${
                        index === focusedIndex ? 'bg-gray-200' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`text-xs font-medium transition-colors ${
                          index === focusedIndex ? 'text-gray-600' : 'text-gray-500'
                        }`}
                      >
                        {market.title.slice(0, 2).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>

                {/* 마켓 정보 */}
                <div className="min-w-0 flex-1">
                  <div
                    className={`truncate text-sm font-medium transition-colors ${
                      index === focusedIndex ? 'text-gray-900' : 'text-gray-900'
                    }`}
                  >
                    {market.title}
                  </div>
                  <div
                    className={`truncate text-xs transition-colors ${
                      index === focusedIndex ? 'text-gray-600' : 'text-gray-500'
                    }`}
                  >
                    @{market.id.slice(0, 8)}...{market.id.slice(-4)}
                  </div>
                </div>

                {/* 검색 아이콘 */}
                <div className="flex-shrink-0">
                  <SearchIcon
                    className={`h-4 w-4 transition-colors ${
                      index === focusedIndex ? 'text-gray-500' : 'text-gray-400'
                    }`}
                  />
                </div>
              </button>
            ))}
        </div>
      )}
    </div>
  );
}
