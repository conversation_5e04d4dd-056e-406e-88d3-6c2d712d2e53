'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import { useTrendingKeywords } from '@/hooks/query/trending/use-trending-keywords';

export const TrendingKeywords = () => {
  const { data: trendingData, isLoading } = useTrendingKeywords();
  const keywords = trendingData?.keywords || [];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (keywords.length > 1 && !isHovered) {
      const interval = setInterval(() => {
        setCurrentIndex(prevIndex => (prevIndex + 1) % keywords.length);
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [keywords, isHovered]);

  const currentKeyword = keywords[currentIndex];

  if (isLoading || keywords.length === 0 || !currentKeyword) {
    return (
      <div className="rounded-round-sm border-line bg-surface-soft py-space-8 flex h-[36px] flex-1 animate-pulse items-center border" />
    );
  }

  return (
    <div
      className="group relative flex-1"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={`/markets/all?keyword=${encodeURIComponent(currentKeyword)}`}>
        <div
          className={cn(
            'rounded-round-sm border-line py-space-8 flex h-[36px] flex-1 cursor-pointer items-center border'
          )}
        >
          <div className="border-r-line border-r px-1">
            <SvgIcon name="KeywordIcon" />
          </div>
          <div className="relative h-full flex-1 overflow-hidden">
            <AnimatePresence>
              <motion.div
                key={currentIndex}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: -20, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="text-dark text-size-sm gap-space-8 px-space-10 absolute inset-0 flex items-center font-semibold"
              >
                <span className="flex size-[14px] shrink-0 items-center justify-center rounded-full bg-black text-[10px] font-semibold text-white">
                  {currentIndex + 1}
                </span>
                <span>{currentKeyword}</span>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </Link>

      <div className="invisible absolute top-0 z-20 w-full rounded-md border border-[#ECECEC] bg-[#F9F9F9] p-[5px] opacity-0 shadow-lg transition-all duration-200 group-hover:visible group-hover:opacity-100">
        <ul className="flex flex-col gap-[2px]">
          {keywords.map((keyword, index) => (
            <li key={keyword}>
              <Link
                href={`/markets/all?keyword=${encodeURIComponent(keyword)}`}
                className="hover:bg-gray-2 gap-space-8 px-space-12 py-space-6 flex items-center rounded-sm"
                onClick={() => setIsHovered(false)}
              >
                <span className="flex size-[14px] shrink-0 items-center justify-center rounded-full bg-black text-[10px] font-semibold text-white">
                  {index + 1}
                </span>

                <span className="text-size-sm text-icon-dark font-semibold">{keyword}</span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
