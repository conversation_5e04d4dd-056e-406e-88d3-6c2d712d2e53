import { useState } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { Popup } from '@/components/ui/popup';
import DepositUsdcPopup from '@/components/ui/popup/deposit-usdc-popup';

export function UsdcDepositButton() {
  const [isDepositPopupOpen, setIsDepositPopupOpen] = useState(false);

  const handleOpenDepositPopup = () => {
    setIsDepositPopupOpen(true);
  };

  const handleCloseDepositPopup = () => {
    setIsDepositPopupOpen(false);
  };

  return (
    <div>
      <InfoButton size="sm" onClick={handleOpenDepositPopup}>
        Deposit
      </InfoButton>

      {/* Deposit USDC Popup */}
      <Popup isOpen={isDepositPopupOpen} onClose={handleCloseDepositPopup} showCloseButton={true}>
        <DepositUsdcPopup onClose={handleCloseDepositPopup} />
      </Popup>
    </div>
  );
}
