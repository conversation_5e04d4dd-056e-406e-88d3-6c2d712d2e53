import { MarketListItem } from '@/hooks/query/market/market.mapper';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { MarketOutcome } from '@/lib/api/market/market.transform';
import { toAmount } from '@/lib/format';
import { getGraphVar } from '@/lib/styles';
import { getEsimatedOdds } from '@/lib/utils';
import { Card, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { Slider } from '@repo/ui/components/slider';
import { cn } from '@repo/ui/lib/utils';
import BigNumber from 'bignumber.js';
import { X } from 'lucide-react';
import { useState } from 'react';
import PredictButton from '../actions/predict-button';
import CommonAvatar from '../ui/avatar-image';
import { BaseButton } from '../ui/base.button';
import { DollarInput } from '../ui/dollar-input';

interface PredictionCardQuickModeProps {
  selectedOutcome: MarketOutcome;
  marketListItem: MarketListItem;
  onBack: () => void;
}

const MIN_BET_AMOUNT = 1;

export default function PredictionCardQuickMode({
  onBack,
  marketListItem,
  selectedOutcome,
}: PredictionCardQuickModeProps) {
  const {
    marketId,
    marketTitle,
    marketAvatarImageUrl,
    marketMaxOutcomeVolume,
    marketTotalVolume,
    marketStatus,
  } = marketListItem;

  const [usdc, setUsdc] = useState<number>(1);
  const maxBetUsdc = Number(marketMaxOutcomeVolume);
  const { balance: myUSDCBalance } = useMyUSDCBalance();

  const isMarketLive = marketStatus === 'OPEN';

  const handleAddAmount = (amount: number) => {
    if (!isMarketLive) return;
    setUsdc(prev => {
      const newAmount = prev + amount;
      return newAmount > maxBetUsdc ? maxBetUsdc : newAmount;
    });
  };

  const handleInputChange = (value: number) => {
    if (!isMarketLive) return;
    setUsdc(Math.min(Math.max(value, MIN_BET_AMOUNT), maxBetUsdc));
  };

  const handleSliderChange = (value: number[]) => {
    if (!isMarketLive) return;
    if (value && value.length > 0) {
      const newValue = value[0];
      if (typeof newValue === 'number') {
        setUsdc(newValue);
      }
    }
  };

  const estimatedOdds = getEsimatedOdds(
    marketTotalVolume.raw,
    selectedOutcome.volume.raw.plus(BigNumber(toAmount(usdc)))
  );

  const outcomePercentage = selectedOutcome.volume.raw
    .dividedBy(marketTotalVolume.raw)
    .multipliedBy(100)
    .toFixed(2);

  return (
    <Card
      style={{
        backgroundColor: getGraphVar(selectedOutcome.order, 20),
        boxShadow: `0px 0px 0px var(--market-card-border-width) ${getGraphVar(selectedOutcome.order, 20)}`,
      }}
      className={cn(
        'gap-space-10 relative h-(--market-card-height) min-w-(--market-card-width) justify-between rounded-none border-0 p-[10px] shadow-none transition-all',
        !isMarketLive && 'opacity-70'
      )}
    >
      <button
        onClick={onBack}
        className="absolute top-0 right-0 rounded-full bg-white p-1 text-gray-400 transition-colors hover:text-gray-600"
        aria-label="Close quick mode"
      >
        <X className="text-mid-dark size-3" />
      </button>
      <CardHeader className="items-cente flex h-(--market-header-height) p-0">
        <div className="gap-space-12 flex items-center">
          <CommonAvatar size="md" imageUrl={marketAvatarImageUrl} />
          <div className="flex flex-col justify-center">
            <h3 className="text-size-sm flex-1 leading-tight font-semibold">{marketTitle}</h3>
            <div className="text-size-xs gap-space-6 flex">
              <span className="text-mid-dark text-size-sm font-semibold">{outcomePercentage}%</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="gap-space-8 relative flex flex-col">
          <div className="flex items-center justify-between">
            <div className="text-size-xs text-mid-dark font-semibold">Amount</div>
            <div className="text-size-xs">
              <span className="text-gray-3">My Balance: </span>
              <span>${myUSDCBalance}</span>
            </div>
          </div>

          <div className="gap-space-10 flex">
            <div className="flex flex-col justify-between">
              <Slider
                defaultValue={[MIN_BET_AMOUNT]}
                value={[usdc]}
                min={MIN_BET_AMOUNT}
                max={maxBetUsdc}
                step={1}
                onValueChange={handleSliderChange}
                disabled={!isMarketLive}
              />
              <div className="gap-space-5 flex">
                <BaseButton
                  className="text-size-xxs10 px-space-5 rounded-none"
                  variant="neutral"
                  size="xxs"
                  onClick={() => handleAddAmount(1)}
                  disabled={!isMarketLive}
                >
                  +$1
                </BaseButton>
                <BaseButton
                  className="text-size-xxs10 px-space-5 rounded-none"
                  variant="neutral"
                  size="xxs"
                  onClick={() => handleAddAmount(10)}
                  disabled={!isMarketLive}
                >
                  +$10
                </BaseButton>
              </div>
            </div>
            <DollarInput
              value={usdc}
              onChange={handleInputChange}
              minValue={MIN_BET_AMOUNT}
              maxValue={maxBetUsdc}
              className="text-size-xl px-space-10 flex-1 rounded border text-right font-bold"
              disabled={!isMarketLive}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-0">
        <PredictButton
          marketId={marketId}
          estimatedOdds={estimatedOdds}
          outcome={isMarketLive ? selectedOutcome.outcome : undefined}
          amount={isMarketLive ? usdc : 0}
          onSuccess={onBack}
          onFailure={onBack}
        />
      </CardFooter>
    </Card>
  );
}
