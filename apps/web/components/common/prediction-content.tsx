import { MarketOutcome } from '@/lib/api/market/market.transform';
import { getGraphVar } from '@/lib/styles';
import { cn } from '@repo/ui/lib/utils';
import { GreenButton } from '../ui/base.button';
import { calculatePercentage, getPercentage } from '@/lib/utils';

type MarketCardDisplayState =
  | { name: 'live' }
  | { name: 'pending' }
  | { name: 'resolved'; winningOutcome: string | null | undefined }
  | { name: 'cancelled' };

export interface PredictionOption {
  order: number;
  marketId: string;
  text: string;
  percentage: string;
  volume?: string;
}

interface PredictionContentProps {
  outcomes: MarketOutcome[];
  totalVolume: string;
  displayState: MarketCardDisplayState;
  handleClick: (selection: MarketOutcome) => void;
}

export default function PredictionCardOutcomes({
  outcomes,
  totalVolume,
  displayState,
  handleClick,
}: PredictionContentProps) {
  const isLive = displayState.name === 'live';
  const isResolved = displayState.name === 'resolved' || displayState.name === 'cancelled';
  const winningOutcome = displayState.name === 'resolved' ? displayState.winningOutcome : undefined;

  return (
    <div className="p-space-10">
      {/* Options list */}
      <div className="gap-space-5 flex flex-col">
        {outcomes.map(outcome => {
          const isWinningOutcome = isResolved && winningOutcome === outcome.outcome;

          const percentageDisplay = getPercentage(outcome.volume.formatted, totalVolume);
          const progressBarWidth =
            calculatePercentage(outcome.volume.formatted, totalVolume).toFixed(2) + '%';

          return (
            <div
              key={outcome.outcome}
              className={cn(
                'gap-space-10 flex items-center rounded p-1',
                isWinningOutcome && 'bg-blue-100',
                isResolved && !isWinningOutcome && 'opacity-50'
              )}
            >
              {/* Option Text */}
              <div className="text-gray-3 text-size-xs w-[90px] truncate font-semibold">
                {outcome.outcome}
              </div>

              {/* Progress */}
              <div className="gap-space-10 flex flex-1 flex-row items-center justify-between">
                <div
                  data-name="prediction-progress"
                  className="relative h-2 w-full overflow-hidden"
                >
                  <div
                    data-name="prediction-progress-bar"
                    className={cn('absolute top-0 left-0 h-full rounded-none')}
                    style={{
                      backgroundColor: getGraphVar(outcome.order),
                      width: progressBarWidth,
                    }}
                  />
                </div>
                <div>
                  <span className="text-size-xs text-mid-dark font-semibold whitespace-nowrap">
                    {percentageDisplay}
                  </span>
                </div>
              </div>
              {/* Button  */}
              <GreenButton
                rounded="sm"
                fontSize="xxs"
                onClick={() => handleClick(outcome)}
                className="px-space-6"
                size="xs"
                disabled={!isLive}
              >
                Predict
              </GreenButton>
            </div>
          );
        })}
      </div>
    </div>
  );
}
