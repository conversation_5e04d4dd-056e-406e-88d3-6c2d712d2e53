'use client';

import SvgIcon from '@/components/icons/svg-icon';
import { cardSwitchVariants } from '@/lib/animationVariants';
import { formatVolume } from '@repo/shared/utils/number-format';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { cn } from '@repo/ui/lib/utils';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useState } from 'react';
import CommonAvatar from '../ui/avatar-image';
import { MarketCountdown } from './market-countdown';
import PredictionCardQuickMode from './prediction-card-quick-mode';
import PredictionCardOutcomes from './prediction-content';
import PredictionMarketCardStatus from './prediction-market-card-status';
import type { MarketListItem } from '@/hooks/query/market/market.mapper';
import type { MarketOutcome } from '@/lib/api/market/market.transform';
import { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { INNER_LINKS } from '@/lib/constants';

type MarketCardDisplayState =
  | { name: 'live' }
  | { name: 'pending' }
  | { name: 'resolved'; winningOutcome: string | null; proposedOutcomeOdds: string | null }
  | { name: 'cancelled' };

function getDisplayState(
  status: MarketStatusEnum,
  proposedOutcome: string | null,
  proposedOutcomeOdds: string | null
): MarketCardDisplayState {
  if (status === 'OPEN') {
    return { name: 'live' };
  } else if (status === 'REVIEWING' || status === 'DISPUTABLE' || status === 'DISPUTED') {
    return { name: 'pending' };
  } else if (
    status === 'CLOSED_WITHOUT_DISPUTE' ||
    status === 'CLOSED_WITH_DISPUTE_ACCEPTED' ||
    status === 'CLOSED_WITH_DISPUTE_REJECTED'
  ) {
    const proposedOutcomeOddsMultiplied = parseFloat(proposedOutcomeOdds ?? '0') / 100;
    return {
      name: 'resolved',
      winningOutcome: proposedOutcome,
      proposedOutcomeOdds: proposedOutcomeOddsMultiplied.toFixed(2),
    };
  } else if (
    status === 'CANCELLED_WITH_UNMET' ||
    status === 'CANCELLED_WITH_INVALID' ||
    status === 'CANCELLED_WITH_OUTCOME_NOT_PROPOSED'
  ) {
    return { name: 'cancelled' };
  }
  return { name: 'cancelled' };
}

export interface PredictionMarketCardProps {
  item: MarketListItem;
}
export function PredictionMarketCard({ item }: PredictionMarketCardProps) {
  const {
    marketId,
    marketTitle,
    marketAvatarImageUrl,
    marketParticipants,
    marketOutcomes,
    channelId,
    channelName,
    channelAvatarImageUrl,
    marketStatus,
    marketNextDeadline,
    marketProposedOutcome,
    marketProposeOutcomeOdds,
    marketTotalVolume,
  } = item;

  const displayState = getDisplayState(
    marketStatus,
    marketProposedOutcome,
    marketProposeOutcomeOdds
  );
  const [isQuickMode, setIsQuickMode] = useState(false);
  const [selectedOutcome, setSelectedOutcome] = useState<MarketOutcome | null>(null);

  const isPredictable = displayState.name === 'live';
  const isResolved = displayState.name === 'resolved' || displayState.name === 'cancelled';

  const handleClickPredict = (outcome: MarketOutcome) => {
    if (!isPredictable) {
      return;
    }

    setSelectedOutcome(outcome);
    setIsQuickMode(true);
  };

  const showQuickMode = isQuickMode && selectedOutcome !== null;
  return (
    <AnimatePresence mode="wait">
      {showQuickMode ? (
        <motion.div
          key="quick-mode"
          variants={cardSwitchVariants}
          initial="quickModeInitial"
          animate="quickModeEnter"
          exit="quickModeExit"
        >
          <PredictionCardQuickMode
            selectedOutcome={selectedOutcome}
            marketListItem={item}
            onBack={() => {
              setIsQuickMode(false);
              setSelectedOutcome(null);
            }}
          />
        </motion.div>
      ) : (
        <motion.div
          key="market-card"
          variants={cardSwitchVariants}
          initial="marketCardInitial"
          animate="marketCardEnter"
          exit="marketCardExit"
        >
          <Card
            data-name="market-card"
            className={cn(
              'h-(--market-card-height) min-w-(--market-card-width) justify-between gap-[10px] rounded-none border-0 p-[10px] shadow-none',
              isResolved && 'opacity-70'
            )}
          >
            <MarketCardHeader
              imageUrl={marketAvatarImageUrl}
              marketId={marketId}
              title={marketTitle}
              volume={marketTotalVolume.formatted}
              participants={marketParticipants}
            />
            <MarketCardContent
              outcomes={marketOutcomes}
              handleClick={handleClickPredict}
              totalVolume={marketTotalVolume.formatted}
              displayState={displayState}
            />
            <MarketCardFooter
              channelId={channelId}
              marketAvatarImageUrl={channelAvatarImageUrl}
              channelName={channelName}
              status={marketStatus}
              endDate={marketNextDeadline}
            />
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function MarketCardHeader({
  title,
  volume,
  participants,
  marketId,
  imageUrl,
}: {
  title: string;
  volume: string;
  participants: number;
  marketId: string;
  imageUrl: string;
}) {
  return (
    <CardHeader className="gap-0 p-0">
      <Link href={INNER_LINKS.MAIN.MARKETS.DETAIL(marketId)}>
        <div className="flex h-(--market-header-height) items-center justify-center">
          <div className="flex w-full items-center justify-between gap-3">
            <CommonAvatar size="md" className="rounded-full" imageUrl={imageUrl} />
            <h3 className="text-size-sm line-clamp-2 min-w-0 flex-1 leading-tight font-semibold">
              {title}
            </h3>
            <div className="flex flex-col items-end">
              <span className="text-size-xxs text-muted-foreground">
                Vol. {formatVolume(volume)}
              </span>
              <div className="flex items-center gap-1">
                <SvgIcon name="MemberIcon" />
                <span className="text-size-xxs text-muted-foreground">{participants}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </CardHeader>
  );
}

function MarketCardContent({
  outcomes,
  handleClick,
  totalVolume,
  displayState,
}: {
  outcomes: MarketOutcome[];
  handleClick: (outcome: MarketOutcome) => void;
  totalVolume: string;
  displayState: MarketCardDisplayState;
}) {
  return (
    <CardContent className="h-[102px] p-0">
      {displayState.name === 'resolved' && displayState.winningOutcome ? (
        <div className="bg-gray-2 gap-space-20 px-space-10 flex h-full flex-col justify-center">
          <h4 className="text-size-xxs text-gray-3">Prediction Result</h4>
          <p className="text-size-lg font-bold">
            {`${displayState.winningOutcome} (x ${displayState.proposedOutcomeOdds})`}
          </p>
        </div>
      ) : (
        <ScrollArea className="bg-gray-2 h-full">
          <PredictionCardOutcomes
            outcomes={outcomes}
            handleClick={handleClick}
            totalVolume={totalVolume}
            displayState={displayState}
          />
        </ScrollArea>
      )}
    </CardContent>
  );
}

function MarketCardFooter({
  marketAvatarImageUrl,
  channelName,
  status,
  channelId,
  endDate,
}: {
  marketAvatarImageUrl: string;
  channelName: string;
  status: MarketStatusEnum;
  endDate: number | null;
  channelId: string;
}) {
  return (
    <CardFooter className="h-(--market-footer-height) justify-between p-0">
      <div className="flex items-center justify-between">
        <Link href={INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}>
          <div className="rounded-round-lg border-line bg-gray-2 gap-space-6 flex items-center justify-center border">
            <CommonAvatar size="sm" imageUrl={marketAvatarImageUrl} className="rounded-full" />
            <div className="text-size-xxs pr-space-12 font-semibold">{channelName}</div>
          </div>
        </Link>
      </div>
      <div className="gap-space-8 flex items-center">
        <PredictionMarketCardStatus status={status} />
        {endDate && <MarketCountdown endTime={endDate} />}
      </div>
    </CardFooter>
  );
}

export function PredictionMarketCardSkeleton() {
  return (
    <Card
      data-name="market-card-skeleton"
      className={cn(
        'h-(--market-card-height) min-w-(--market-card-width) justify-between gap-[10px] rounded-none border-0 p-[10px] shadow-none'
      )}
    >
      {/* Header Skeleton */}
      <CardHeader className="gap-0 p-0">
        <div className="flex h-(--market-header-height) items-center justify-center">
          <div className="flex w-full items-center justify-between gap-3">
            <div className="size-10 animate-pulse rounded-full bg-gray-200" />
            <div className="min-w-0 flex-1 space-y-1">
              <div className="h-4 w-full animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
            </div>
            <div className="flex flex-col items-end gap-1">
              <div className="h-3 w-12 animate-pulse rounded bg-gray-200" />
              <div className="h-3 w-8 animate-pulse rounded bg-gray-200" />
            </div>
          </div>
        </div>
      </CardHeader>

      {/* Content Skeleton */}
      <CardContent className="h-[102px] p-0">
        <div className="bg-gray-2 h-full space-y-2 p-2">
          <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
          <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
          <div className="h-6 w-2/3 animate-pulse rounded bg-gray-200" />
        </div>
      </CardContent>

      {/* Footer Skeleton */}
      <CardFooter className="h-(--market-footer-height) justify-between p-0">
        <div className="rounded-round-lg border-line bg-gray-2 gap-space-6 flex items-center justify-center border px-2 py-1">
          <div className="size-6 animate-pulse rounded-full bg-gray-200" />
          <div className="h-3 w-16 animate-pulse rounded bg-gray-200" />
        </div>
        <div className="gap-space-8 flex items-center">
          <div className="h-5 w-12 animate-pulse rounded bg-gray-200" />
          <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
        </div>
      </CardFooter>
    </Card>
  );
}
