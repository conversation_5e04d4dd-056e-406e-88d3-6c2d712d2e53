import { cn } from '@repo/ui/lib/utils';

export const SuperbridgeIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="203"
      height="144"
      viewBox="0 0 203 144"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('h-full w-full', className)}
    >
      <title>Superbridge Logo</title>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M44.2209 128.177L10.9275 90.4177L10.9223 90.4117C6.63691 85.5348 0.811452 76.6457 0.0766453 65.9947C0.0264394 65.267 0 64.5291 0 63.7824C0 52.0891 6.48189 42.2752 10.8806 37.2008L10.908 37.1692L29.6142 15.9737L29.6238 15.9628C37.8164 6.73751 48.3424 0 60.9548 0C70.4339 0 78.795 3.41557 85.1291 9.19501L85.1493 9.21341L85.1693 9.23193C88.4399 12.2512 91.1229 15.8415 93.1131 19.8058C98.7741 8.40722 110.382 0 125.546 0C125.694 0 125.842 0.000943528 125.99 0.0028279C126.236 0.00596851 126.482 0.0117238 126.728 0.0200802C138.839 0.431991 148.95 7.03609 156.877 15.9628L156.891 15.9786L191.387 55.1779C191.389 55.1795 191.39 55.181 191.392 55.1826C196.129 60.5476 202.998 69.7817 202.998 81.7212C202.998 93.0847 195.968 102.391 192.057 106.815L173.383 127.975L173.374 127.985C165.181 137.211 154.655 143.948 142.043 143.948C131.512 143.948 117.754 138.519 110.577 124.521C110.331 124.417 110.087 124.311 109.844 124.202C104.176 135.747 92.6034 144 77.4516 144C66.2268 144 55.8797 139.544 47.4918 131.57L53.9655 124.76C60.7714 131.23 68.8541 134.604 77.4516 134.604C89.0675 134.604 97.832 128.193 101.861 119.128C102.894 116.805 103.616 114.307 103.995 111.699C104.158 110.574 104.258 109.428 104.291 108.266C104.802 108.863 105.309 109.422 105.815 109.949C108.085 112.31 110.344 113.994 112.901 115.257C114.332 115.964 115.857 116.54 117.53 117.028C117.824 117.822 118.145 118.585 118.488 119.319C123.647 130.315 134.14 134.553 142.043 134.553C151.041 134.553 159.212 129.783 166.349 121.746L185.017 100.593C188.637 96.4974 193.602 89.4463 193.602 81.7212C193.602 73.3739 188.741 66.3747 184.345 61.3974L149.852 22.2017C142.944 14.4231 135.067 9.70477 126.409 9.41029C126.229 9.4042 126.05 9.4 125.87 9.39771C125.762 9.39633 125.654 9.39564 125.546 9.39564C109.67 9.39564 99.1199 21.6314 98.7062 35.2151H87.6911C87.2774 27.8011 84.0194 20.9574 78.7962 16.1357C74.1937 11.9361 68.0914 9.39564 60.9548 9.39564C51.9565 9.39564 43.7856 14.1655 36.6491 22.2017L17.9802 43.3549C14.2051 47.71 9.39564 55.3833 9.39564 63.7824C9.39564 64.3073 9.41423 64.8294 9.45001 65.3481C9.98672 73.1277 14.3925 80.1269 17.9802 84.2098L51.0774 121.746L51.1291 121.798C52.047 122.848 52.9932 123.836 53.9655 124.76L47.4918 131.57C46.3801 130.513 45.3053 129.397 44.2687 128.225L44.2209 128.177Z"
        className="fill-white"
        shapeRendering="geometricPrecision"
      ></path>
      <path
        d="M51.0782 121.695L17.9811 84.158C14.1542 79.8029 9.39648 72.1296 9.39648 63.7305C9.39648 55.3314 14.2059 47.6582 17.9811 43.3031L36.6499 22.1498C43.7865 14.1137 51.9573 9.34381 60.9556 9.34381C68.0922 9.34381 74.1945 11.8843 78.7971 16.0838C84.0202 20.9055 87.2782 27.7492 87.6919 35.1632H98.7071C99.1208 21.5795 109.67 9.34381 125.547 9.34381C134.545 9.34381 142.716 14.1137 149.852 22.1498L184.346 61.3456C188.742 66.3229 193.603 73.3221 193.603 81.6693C193.603 89.3944 188.638 96.4455 185.018 100.541L166.349 121.695C159.213 129.731 151.042 134.501 142.044 134.501C133.614 134.501 122.237 129.679 117.531 116.977C112.204 115.421 108.378 112.984 104.292 108.215C103.879 122.576 93.3288 134.553 77.4525 134.553C67.6268 134.553 58.4733 130.146 51.1299 121.747L51.0782 121.695Z"
        className="fill-white"
        shapeRendering="geometricPrecision"
      ></path>
      <path
        className="fill-black"
        d="M51.0782 121.695L17.9811 84.158C14.1542 79.8029 9.39648 72.1296 9.39648 63.7305C9.39648 55.3314 14.2059 47.6582 17.9811 43.3031L36.6499 22.1498C43.7865 14.1137 51.9573 9.34381 60.9556 9.34381C68.0922 9.34381 74.1945 11.8843 78.7971 16.0838C84.0202 20.9055 87.2782 27.7492 87.6919 35.1633H98.7071C99.1208 21.5795 109.67 9.34381 125.547 9.34381C134.545 9.34381 142.716 14.1137 149.852 22.1498L184.346 61.3456C188.742 66.3229 193.603 73.3221 193.603 81.6694C193.603 89.3944 188.638 96.4455 185.018 100.541L166.349 121.695C159.213 129.731 151.042 134.501 142.044 134.501C133.614 134.501 122.237 129.679 117.531 116.977C112.204 115.421 108.378 112.984 104.292 108.215C103.878 122.576 93.3288 134.553 77.4525 134.553C67.6268 134.553 58.4733 130.146 51.1299 121.747L51.0782 121.695ZM24.1351 78.766L42.8039 99.9192C48.5959 106.452 54.6465 109.926 60.9556 109.926C72.3845 109.926 79.6245 101.215 79.6245 91.7275C79.6245 85.7652 75.9528 82.9655 71.3502 79.751C70.2642 79.0252 69.4885 77.6772 69.4885 76.3292C69.4885 74.0998 71.2985 72.2333 73.4705 72.2333C74.3496 72.2333 75.3322 72.5444 76.5216 73.3221C80.0382 75.4478 84.6408 80.114 86.3991 84.158H100.155C101.965 80.1658 106.516 75.4478 110.032 73.3221C111.17 72.5963 112.153 72.2333 113.084 72.2333C115.307 72.2333 117.066 74.0998 117.066 76.3292C117.066 77.729 116.238 79.0252 115.204 79.751C110.601 82.9655 106.93 85.7134 106.93 91.7275C106.93 101.215 114.118 109.926 125.598 109.926C131.908 109.926 137.958 106.504 143.75 99.9192L162.419 78.766C165.47 75.3441 168.935 69.4336 168.935 63.7824C168.935 58.1311 165.419 52.2207 162.419 48.7988L143.75 27.6455C137.958 21.1129 131.908 17.6392 125.598 17.6392C114.17 17.6392 106.93 26.3494 106.93 35.8373C106.93 41.7996 110.601 44.5993 115.204 47.8137C116.29 48.5396 117.066 49.8876 117.066 51.2356C117.066 53.465 115.256 55.3314 113.084 55.3314C112.204 55.3314 111.222 55.0204 110.032 54.2427C106.516 52.117 101.913 47.4508 100.155 43.4068H86.3991C84.5891 47.399 80.0382 52.117 76.5216 54.2427C75.3839 54.9685 74.4014 55.3314 73.4705 55.3314C71.2468 55.3314 69.4885 53.465 69.4885 51.2356C69.4885 49.8357 70.3159 48.5396 71.3502 47.8137C75.9528 44.5993 79.6245 41.8514 79.6245 35.8373C79.6245 26.3494 72.4362 17.6392 60.9556 17.6392C54.6465 17.6392 48.5959 21.0611 42.8039 27.6455L24.1351 48.7988C21.0839 52.2207 17.6191 58.1311 17.6191 63.7824C17.6191 69.4336 21.1356 75.3441 24.1351 78.766ZM39.7528 40.5034L48.3373 30.9119C50.1473 28.9417 52.8365 28.6306 54.5431 30.2379C56.3531 31.6896 55.9911 34.4374 54.5431 36.1483L45.9585 45.8954C44.2519 47.7619 41.4593 47.9693 39.7528 46.4657C37.9428 44.9104 38.3048 42.1107 39.7528 40.5034ZM121.048 24.7422C121.616 24.4829 122.289 24.4311 122.858 24.3274C125.547 24.1718 127.512 25.8828 127.615 28.4232C127.719 30.2897 126.374 32.0525 124.512 32.4154C123.426 32.5709 123.116 33.1412 122.961 34.1263C122.806 35.9409 121.151 37.3408 119.186 37.3926C116.755 37.4963 114.945 35.6817 114.842 33.1931C114.842 29.2009 117.531 25.8828 121.048 24.6903V24.7422Z"
        shapeRendering="geometricPrecision"
      ></path>
    </svg>
  );
};
