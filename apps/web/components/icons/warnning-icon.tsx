import { cn } from '@repo/ui/lib/utils';

export const WarnningIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="35"
      height="42"
      viewBox="0 0 35 42"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('h-full w-full', className)}
    >
      <title>Warning Icon</title>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9821 40.6197C16.548 41.5483 18.452 41.5483 20.0179 40.6197C24.8611 37.7479 35 30.5906 35 21V10.517C35 7.98684 33.4129 5.72863 31.0323 4.87163L19.5323 0.731633C18.2187 0.258732 16.7813 0.258733 15.4677 0.731634L3.96768 4.87163C1.58713 5.72863 0 7.98685 0 10.517V21C0 30.5906 10.1389 37.7479 14.9821 40.6197ZM7.85657 7.50048C5.47602 8.35748 3.88889 10.6157 3.88889 13.1458V21C3.88889 25.5948 7.14503 29.8826 11.2937 33.3674C13.2827 35.0382 15.2905 36.3653 16.8092 37.2765C17.2346 37.5317 17.7654 37.5317 18.1908 37.2765C19.7095 36.3653 21.7173 35.0382 23.7063 33.3674C27.855 29.8826 31.1111 25.5948 31.1111 21V13.1458C31.1111 10.6157 29.524 8.35748 27.1434 7.50048L19.5323 4.76048C18.2187 4.28758 16.7813 4.28758 15.4677 4.76048L7.85657 7.50048Z"
        fill="#F76566"
      />
      <path
        d="M19.449 12.3666C19.449 11.193 18.4696 10.2416 17.2615 10.2416C16.0533 10.2416 15.074 11.193 15.074 12.3666V22.2333C15.074 23.4069 16.0533 24.3583 17.2615 24.3583C18.4696 24.3583 19.449 23.4069 19.449 22.2333V12.3666Z"
        fill="#F76566"
      />
      <path
        d="M19.3203 28.6668C19.3203 29.7714 18.3986 30.6668 17.2615 30.6668C16.1245 30.6668 15.2027 29.7714 15.2027 28.6668C15.2027 27.5622 16.1245 26.6668 17.2615 26.6668C18.3986 26.6668 19.3203 27.5622 19.3203 28.6668Z"
        fill="#F76566"
      />
    </svg>
  );
};
