import { useRef } from 'react';
import { useFileDragAndDrop } from '@/hooks/use-file-drag-and-drop';
import { Avatar, AvatarImage } from '@repo/ui/components/avatar';
import { toast } from '@repo/ui/components/sonner';
import { cn } from '@repo/ui/lib/utils';
import { DEFAULT_MARKET_AVATAR_URL, TOAST_MESSAGE } from '@/lib/constants';

interface AvatarImageUploaderProps {
  imageUrl?: string;
  onFileSelect: (file: File | null) => void;
  size?: number;
  acceptedTypes?: string[];
  maxSize?: number;
  disabled?: boolean;
  className?: string;
}

export function AvatarImageUploader({
  imageUrl,
  onFileSelect,
  size = 80,
  acceptedTypes = ['image/jpeg', 'image/png'],
  maxSize = 1 * 1024 * 1024, // 1MB
  disabled = false,
  className,
}: AvatarImageUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (validateFile(file)) {
      onFileSelect(file);
    }
  };

  const handleClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const { isDragging, handleDragOver, handleDragLeave, handleDrop } = useFileDragAndDrop({
    disabled,
    onFileDrop: handleFileSelect,
  });

  const validateFile = (file: File): boolean => {
    // 파일 타입 검증
    if (!acceptedTypes.some(type => file.type === type)) {
      toast.error(TOAST_MESSAGE.INVALID_IMAGE_TYPE);
      return false;
    }

    // 파일 크기 검증
    if (file.size > maxSize) {
      toast.error(TOAST_MESSAGE.INVALID_IMAGE_SIZE);
      return false;
    }

    return true;
  };

  const editIconSize = Math.max(10, size * 0.125); // 아바타 크기의 12.5%
  const overlaySize = Math.max(20, size * 0.3); // 아바타 크기의 30%

  return (
    <div className={cn('relative inline-block rounded-full', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      <button
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          'relative transition-all duration-200',
          !disabled && 'cursor-pointer hover:scale-105',
          isDragging && 'scale-105',
          disabled && 'cursor-not-allowed opacity-50'
        )}
        style={{ width: size, height: size }}
        type="button"
        aria-label="Upload Avatar Image"
        disabled={disabled}
      >
        <Avatar
          className={cn(
            'h-full w-full',
            (!imageUrl || imageUrl === DEFAULT_MARKET_AVATAR_URL) && 'border-line border-2'
          )}
        >
          <AvatarImage src={imageUrl} className="object-cover" />
        </Avatar>

        {/* 편집 오버레이 */}
        <div
          style={{
            right: '0px',
            bottom: '0px',
            width: overlaySize,
            height: overlaySize,
          }}
          className={cn(
            'bg-sky absolute flex items-center justify-center rounded-full',
            'transition-all duration-200',
            isDragging && 'bg-blue-600'
          )}
        >
          <svg
            width={editIconSize}
            height={editIconSize}
            viewBox="0 0 10 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
              fill="white"
            />
          </svg>
        </div>
      </button>
    </div>
  );
}
