import { cn } from '@repo/ui/lib/utils';
import type { ComponentPropsWithRef } from 'react';
import { allowedInputRegex } from '@/lib/utils';

export function BaseInput({
  className,
  ref,
  onChange,
  allowAllInput = false,
  pattern,
  ...props
}: ComponentPropsWithRef<'input'> & {
  isError?: boolean;
  allowAllInput?: boolean;
}) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!allowAllInput) {
      const value = e.currentTarget.value;
      if (value !== '' && !allowedInputRegex.test(value)) {
        return;
      }
    }
    onChange?.(e);
  };

  const defaultPattern = allowAllInput ? undefined : allowedInputRegex.source;
  return (
    <input
      ref={ref}
      className={cn(
        'border-line bg-gray-2 placeholder:text-icon-gray text-size-xs px-space-15 flex h-(--input-height-md) w-full items-center rounded-none border py-0 transition-all duration-200',
        className
      )}
      onChange={handleChange}
      pattern={pattern || defaultPattern}
      {...props}
    />
  );
}
