import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';
import { allowedInputRegex } from '@/lib/utils';

interface BaseTextareaProps extends ComponentProps<typeof Textarea> {
  icon?: React.ReactNode;
  allowAllInput?: boolean;
}

export function BaseTextarea({
  className,
  icon,
  onChange,
  allowAllInput = false,
  ...props
}: BaseTextareaProps) {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!allowAllInput) {
      const value = e.currentTarget.value;
      if (value !== '' && !allowedInputRegex.test(value)) {
        return;
      }
    }
    onChange?.(e);
  };

  if (icon) {
    return (
      <div className={cn('pr-space-10 flex items-start gap-2', className)}>
        <Textarea
          className="h-full w-full border-none shadow-none"
          onChange={handleChange}
          {...props}
        />
        {icon}
      </div>
    );
  }
  return (
    <Textarea
      className={cn(
        'border-line bg-gray-2 placeholder:text-icon-gray py-space-12 text-size-xs px-space-12 flex min-h-[120px] items-center rounded-none',
        className
      )}
      onChange={handleChange}
      {...props}
    />
  );
}
