import type { BannerData } from '@repo/shared/schemas/banner.schema';

const getVideoMimeType = (videoUrl: string): string => {
  const url = videoUrl.toLowerCase();

  if (url.includes('.mp4')) return 'video/mp4';
  if (url.includes('.webm')) return 'video/webm';
  if (url.includes('.ogg') || url.includes('.ogv')) return 'video/ogg';

  // Default fallback
  return 'video/mp4';
};

export const BannerBackground = ({ banner }: { banner: BannerData }) => {
  if (banner.videoUrl) {
    const videoType = getVideoMimeType(banner.videoUrl);

    return (
      <video
        className="absolute h-full w-full object-cover"
        poster={banner.imageUrl}
        autoPlay
        muted
        loop
        playsInline
      >
        <source src={banner.videoUrl} type={videoType} />
      </video>
    );
  }

  return (
    <div
      className="absolute h-full w-full bg-cover bg-center bg-no-repeat"
      style={{ backgroundImage: `url(${banner.imageUrl})` }}
    />
  );
};
