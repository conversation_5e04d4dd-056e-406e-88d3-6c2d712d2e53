'use client';

import { useBroadcastNotifications } from '@/hooks/query/broadcast/use-broadcast-notifications';
import { motion } from 'motion/react';
import { useMemo } from 'react';
import { BroadcastNotificationItem } from './item';

export default function BroadcastNotificationBar() {
  const { data: broadcastNotificationData, isLoading, error } = useBroadcastNotifications();

  const broadcasts = useMemo(() => {
    if (!broadcastNotificationData?.broadcasts) return [];
    // To make the infinite scroll smooth, we duplicate the items.
    return [...broadcastNotificationData.broadcasts, ...broadcastNotificationData.broadcasts];
  }, [broadcastNotificationData]);

  if (isLoading) {
    return <div className="h-[62px] w-full" />;
  }

  if (error || !broadcasts || broadcasts.length === 0) {
    return null;
  }

  const animationVariants = {
    animate: {
      x: ['0%', '-100%'],
      transition: {
        x: {
          repeat: Infinity,
          repeatType: 'loop',
          duration: broadcasts.length * 10, // Adjust duration based on number of items
          ease: 'linear',
        },
      },
    },
  };

  return (
    <div
      data-testid="broadcast-notification-bar"
      className="flex w-full items-center overflow-hidden"
    >
      <motion.div
        className="flex shrink-0 items-center gap-5 px-2.5 py-4"
        variants={animationVariants}
        animate="animate"
      >
        {broadcasts.map((broadcast, index) => (
          <BroadcastNotificationItem
            key={`${broadcast.market.id}-${index}`}
            broadcast={broadcast}
          />
        ))}
      </motion.div>
    </div>
  );
}
