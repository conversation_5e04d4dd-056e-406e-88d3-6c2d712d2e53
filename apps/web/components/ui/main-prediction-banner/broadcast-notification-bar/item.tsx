'use client';

import { INNER_LINKS } from '@/lib/constants';
import type { TransformedBroadcast } from '@/lib/api/broadcast/broadcast.transform';
import Link from 'next/link';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';
import { useState, useEffect } from 'react';

export function BroadcastNotificationItem({ broadcast }: { broadcast: TransformedBroadcast }) {
  const [icon, setIcon] = useState(broadcast.market.imageUrl || '/default/default-avatar.svg');

  useEffect(() => {
    setIcon(broadcast.market.imageUrl || '/default/default-avatar.svg');
  }, [broadcast.market.imageUrl]);

  return (
    <Link
      href={INNER_LINKS.MAIN.MARKETS.DETAIL(broadcast.market.id)}
      className="flex cursor-pointer items-center gap-2.5 rounded-full bg-black/20 p-1 pr-6"
      style={{
        backdropFilter: 'blur(30px)',
      }}
    >
      <Image
        src={icon}
        alt="activity"
        width={32}
        height={32}
        className="rounded-full"
        onError={() => setIcon('/icons/empty_logo.svg')}
      />
      <div className="text-sm text-white">
        <ReactMarkdown
          components={{
            strong: ({ node, ...props }) => <span className="font-bold" {...props} />,
          }}
        >
          {broadcast.message}
        </ReactMarkdown>
      </div>
    </Link>
  );
}
