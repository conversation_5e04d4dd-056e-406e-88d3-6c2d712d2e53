'use client';

import { useBanner } from '@/hooks/query/banner/use-banner';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from '@repo/ui/components/carousel';
import Autoplay from 'embla-carousel-autoplay';
import EmblaCarouselFade from 'embla-carousel-fade';
import { useEffect, useState } from 'react';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';
import BroadcastNotificationBar from './broadcast-notification-bar';
import { BannerBackground } from './banner-background';
import { SimpleBanner } from './simple-banner';
import { MarketBanner } from './market-banner';

export default function MainPredictionBanner() {
  const { data: banners, isLoading, error } = useBanner();
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  if (isLoading) {
    return (
      <div className="relative">
        <BroadcastNotificationBar />
        <Skeleton className="h-95 w-full" />
      </div>
    );
  }

  if (error || !banners || banners.length === 0) {
    console.error('Failed to load banner data:', error);
    return <BroadcastNotificationBar />;
  }

  return (
    <div className="relative">
      <div className="absolute top-0 right-0 left-0 z-20">
        <BroadcastNotificationBar />
      </div>
      <Carousel
        setApi={setApi}
        className="w-full"
        plugins={[
          Autoplay({
            delay: 20000,
            stopOnInteraction: true,
          }),
          EmblaCarouselFade(),
        ]}
        opts={{
          loop: banners.length > 1,
        }}
      >
        <CarouselContent>
          {banners.map((banner, index) => (
            <CarouselItem key={index}>
              <Link href={banner.url || '#'}>
                <div className="relative h-95 w-full overflow-hidden">
                  <BannerBackground banner={banner} />
                  <div className="absolute inset-0 bg-black/50" />
                  {banner.type === 'SIMPLE' ? (
                    <SimpleBanner banner={banner} />
                  ) : (
                    <MarketBanner banner={banner} />
                  )}
                </div>
              </Link>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="absolute inset-x-10 bottom-5 flex items-center justify-between">
          <div className="flex items-center gap-2.5">
            {banners.map((_, i) => (
              <div
                key={i}
                className={cn('rounded-full transition-all duration-300', {
                  'bg-point-3 h-1.5 w-6': current === i + 1,
                  'h-1.5 w-1.5 bg-white/50': current !== i + 1,
                })}
              />
            ))}
          </div>
          {/* <div className="flex items-center gap-3">
            <CarouselPrevious className="static -translate-y-0" />
            <CarouselNext className="static -translate-y-0" />
          </div> */}
        </div>
      </Carousel>
    </div>
  );
}
