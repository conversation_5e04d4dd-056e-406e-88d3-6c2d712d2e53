import { useMarketById } from '@/hooks/query/market';
import type { BannerData } from '@repo/shared/schemas/banner.schema';
import CommonAvatar from '@/components/ui/avatar-image';
import { Skeleton } from '@/components/ui/skeleton';
import { Carousel, CarouselContent, CarouselItem } from '@repo/ui/components/carousel';

export const MarketBanner = ({ banner }: { banner: BannerData & { type: 'MARKET' } }) => {
  const { data: market, isLoading } = useMarketById(banner.marketId);

  if (isLoading || !market) {
    return (
      <div
        data-testid="market-banner"
        className="relative z-10 flex h-full flex-col justify-end p-10 text-white"
      >
        <Skeleton className="mb-4 h-9 w-3/4" />
        <Skeleton className="h-4 w-1/4" />
        <Skeleton className="mt-2 h-4 w-1/2" />
      </div>
    );
  }

  return (
    <div
      data-testid="market-banner"
      className="relative z-10 flex h-full flex-col justify-end pb-11.5 pl-10 text-white"
    >
      <div className="mb-2.5 flex h-[60px] items-center gap-5">
        <CommonAvatar imageUrl={market.marketAvatarImageUrl} alt={market.marketTitle} size="md3" />
        <h2 className="text-4xl font-bold">{banner.title}</h2>
      </div>
      <div className="flex h-6 items-center justify-center pl-20">
        <Carousel
          opts={{
            align: 'start',
            dragFree: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-4">
            {market.marketOutcomes.map((outcome, index) => {
              const percentage = market.marketTotalVolume.raw.isGreaterThan(0)
                ? outcome.volume.raw
                    .dividedBy(market.marketTotalVolume.raw)
                    .multipliedBy(100)
                    .toNumber()
                : 0;

              return (
                <CarouselItem key={outcome.outcome} className="basis-auto pl-4">
                  <div className="flex items-center">
                    <div className="flex flex-1 items-center gap-4">
                      <span className="shrink-0 text-lg font-bold whitespace-nowrap">
                        {outcome.outcome}
                      </span>
                      {/* <div className="relative h-2 w-20 flex-1 overflow-hidden rounded-full bg-white/30">
                        <div
                          className="absolute h-full rounded-full"
                          style={{
                            width: `${percentage.toFixed(0)}%`,
                            backgroundColor: getGraphVar(outcome.order),
                          }}
                        />
                      </div> */}
                      <span className="w-16 shrink-0 text-right text-lg font-bold">
                        {percentage.toFixed(0)}%
                      </span>
                    </div>
                    {index < market.marketOutcomes.length - 1 && (
                      <div className="ml-8 h-4 w-px bg-white/20" />
                    )}
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  );
};
