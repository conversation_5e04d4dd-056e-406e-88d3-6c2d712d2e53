import { useState } from 'react';
import { Label } from '@repo/ui/components/label';
import { BaseInput } from '../base.input';
import { InfoButton } from '../base.button';
import { useUpdateUserProfile } from '@/hooks/query/user';
import { toast } from '@repo/ui/components/sonner';
import { z } from 'zod';

const emailSchema = z.string().email('Please enter a valid email address');

interface ChooseEmailPopupProps {
  onContinue: () => void;
  onSkip: () => void;
}

export default function ChooseEmailPopup({ onContinue, onSkip }: ChooseEmailPopupProps) {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const updateProfile = useUpdateUserProfile();

  const hasEmailInput = email.trim().length > 0;
  const canContinue = hasEmailInput && !isSubmitting;

  const handleContinue = async () => {
    if (!hasEmailInput || !canContinue) return;

    const emailValidation = emailSchema.safeParse(email);
    if (!emailValidation.success) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      setIsSubmitting(true);
      const formData = new FormData();
      formData.append('email', email);

      await updateProfile.mutateAsync(formData);
      toast.success('Email saved successfully.');
      onContinue();
    } catch (error) {
      console.error('Failed to save email:', error);
      toast.error('Failed to save email.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-gray-2 w-full">
      <div className="py-space-30 space-y-[40px]">
        <header className="gap-space-30 flex flex-col items-center justify-center text-center">
          <h1 className="text-size-base font-bold">What's your email?</h1>
          <p className="text-size-xs text-gray-3">
            Add your email to receive market and trading notifications
          </p>
        </header>

        <div className="space-y-space-30 px-space-20">
          <div>
            <Label className="text-size-xs mb-space-10 font-semibold">User E-mail</Label>
            <BaseInput
              placeholder="<EMAIL>"
              className="mb-space-15"
              value={email}
              onChange={e => setEmail(e.target.value)}
              type="email"
              disabled={isSubmitting}
            />
            <p className="text-size-xxs text-gray-3 font-medium">
              We'll send important notifications to this email
            </p>
          </div>
          <div className="flex justify-end">
            <button
              onClick={onSkip}
              className="text-size-xs text-no-red font-medium underline"
              disabled={isSubmitting}
            >
              Do this later
            </button>
          </div>
        </div>
      </div>
      <div className="flex">
        <InfoButton
          onClick={handleContinue}
          className="flex-1"
          fontSize="sm"
          height={46}
          disabled={!canContinue}
        >
          {isSubmitting ? 'Saving...' : 'Continue'}
        </InfoButton>
      </div>
    </div>
  );
}
