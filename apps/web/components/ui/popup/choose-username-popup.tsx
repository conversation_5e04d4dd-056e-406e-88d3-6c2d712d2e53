import { Label } from '@repo/ui/components/label';
import { BaseInput } from '../base.input';
import { Checkbox } from '@repo/ui/components/checkbox';
import { useState } from 'react';
import { InfoButton } from '../base.button';
import { useUpdateUserProfile } from '@/hooks/query/user';
import { toast } from '@repo/ui/components/sonner';
import { EXTERNAL_LINKS } from '@/lib/constants';

interface ChooseUsernamePopupProps {
  onClose: () => void;
  onContinue: () => void;
}

export default function ChooseUsernamePopup({ onContinue }: ChooseUsernamePopupProps) {
  const [username, setUsername] = useState('');
  const [isAgree, setIsAgree] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState('');
  const updateProfileMutation = useUpdateUserProfile();

  const getValidationError = (username: string) => {
    if (!username.trim()) return 'Username is required';
    if (username.length < 2) return 'Username must be at least 2 characters';
    if (username.length > 42) return 'Username must be 42 characters or less';
    if (!/^[a-zA-Z0-9]*$/.test(username)) return 'Username can only contain letters and numbers';
    return '';
  };

  const canContinue = username.trim() && isAgree && !isSubmitting;

  const handleContinue = async () => {
    if (!canContinue) return;

    const error = getValidationError(username);
    if (error) {
      setValidationError(error);
      return;
    }

    try {
      setIsSubmitting(true);
      setValidationError(''); // 에러 초기화
      const formData = new FormData();
      formData.append('nickname', username);
      await updateProfileMutation.mutateAsync(formData);
      toast.success('Username saved successfully.');
      onContinue();
    } catch (error) {
      console.error('Failed to save username:', error);
      toast.error('Failed to save username.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-gray-2 w-full">
      <div className="py-space-30 space-y-[40px]">
        <header className="gap-space-30 flex flex-col items-center justify-center text-center">
          <h1 className="text-size-base font-bold">Choose a username</h1>
          <p className="text-size-xs text-gray-3">You can update this later</p>
        </header>
        <div className="space-y-space-30 px-space-20">
          <div>
            <Label className="text-size-xs mb-space-10 font-semibold">Username</Label>
            <BaseInput
              value={username}
              onChange={e => {
                setUsername(e.target.value);
                if (validationError) setValidationError(''); // 입력 시 에러 초기화
              }}
              placeholder="username"
              className={`mb-space-15 ${validationError ? 'border-red-500' : ''}`}
              disabled={isSubmitting}
            />
            <div className="space-y-1">
              <p className="text-size-xxs text-gray-3 font-medium">
                a-z, A-Z, 0-9 allowed, up to 42 chars
              </p>
              {validationError && (
                <p className="text-size-xxs font-medium text-red-500">{validationError}</p>
              )}
            </div>
          </div>
          <div>
            <label className="gap-space-6 flex items-start">
              <Checkbox
                checked={isAgree}
                onCheckedChange={checked => setIsAgree(!!checked)}
                disabled={isSubmitting}
              />
              <div className="text-size-xxs text-gray-3 font-semibold">
                Legal requirements differ by the regions, and certain types of transactions may be
                prohibited in some regions. Thus, users must comply with local laws and regulations.
                <br />
                <br />
                Before using the service, I have read and agree to the PredictGo{' '}
                <a
                  rel="noreferrer"
                  href={EXTERNAL_LINKS.TERMS_OF_USE}
                  target="_blank"
                  className="text-mid-dark font-bold underline"
                >
                  Terms of Use
                </a>
                .
              </div>
            </label>
          </div>
        </div>
      </div>
      <div className="flex">
        <InfoButton
          loading={isSubmitting}
          className="h-[46px] flex-1 rounded-none"
          fontSize="sm"
          onClick={handleContinue}
          disabled={!canContinue}
        >
          Continue
        </InfoButton>
      </div>
    </div>
  );
}
