import { InfoButton } from '@/components/ui/base.button';
import { toast } from '@/components/ui/base.toast';
import { appKitModal } from '@/components/providers/wagmi-provider';
import { useGlobalStore } from '@/store/global.store';
import { XsIcon } from '@/components/icons/xs-icon';
import { BaseInput } from '../base.input';
import Image from 'next/image';
import { SuperbridgeIcon } from '@/components/icons/superbridge-icon';
import QRCode from '@/components/ui/qr-code';

interface DepositUsdcPopupProps {
  onClose: () => void;
}

export default function DepositUsdcPopup({ onClose }: DepositUsdcPopupProps) {
  const { safeSmartAccount } = useGlobalStore();

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(safeSmartAccount?.address ?? '');
    toast.success('Address copied to clipboard!');
  };

  const handleBuyCrypto = () => {
    onClose();
    appKitModal.open({
      view: 'OnRampProviders',
    });
  };

  const handleSuperbridge = () => {
    window.open('https://superbridge.app/base', '_blank');
  };

  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="gap-space-30 flex flex-col px-[25px] py-[30px] text-center">
        <h1 className="py-space-10 text-mid-dark font-semibold">Deposit USDC</h1>

        {/* Address Input */}
        <div className="gap-space-8 flex flex-col text-left">
          <p className="text-size-xs text-mid-dark font-semibold">
            Only deposit <span className="text-sky font-medium">Base USDC</span> to this address.
          </p>
          <div className="gap-space-8 relative flex items-center">
            <BaseInput
              value={safeSmartAccount?.address ?? ''}
              readOnly
              className="bg-white pr-10"
            />
            <div className="absolute top-1/2 right-3 flex -translate-y-1/2 items-center">
              <button
                onClick={handleCopyAddress}
                className="p-space-1 hover:bg-gray-1 rounded-round-sm transition-colors"
              >
                <XsIcon name="copy" />
              </button>
            </div>
          </div>
        </div>

        {/* QR Code Section */}
        {safeSmartAccount?.address && <QRCode address={safeSmartAccount.address} />}

        {/* Options */}
        <div className="gap-space-15 flex flex-col">
          {/* Buy Crypto Option */}
          <a
            onClick={handleBuyCrypto}
            className="gap-space-6 flex cursor-pointer border border-0 text-left underline-offset-2 hover:underline"
          >
            <div>
              <Image src="/assets/icons/card-02.svg" alt="buy-crypto" width={20} height={20} />
            </div>
            <div className="flex-1 gap-1.5">
              <div className="text-size-sm font-semibold">Buy Crypto</div>
              <div className="text-size-xs text-gray-3">Easy with card or bank account</div>
            </div>
          </a>
          <div className="bg-line h-px w-full" />
          {/* Superbridge Option */}
          <a
            onClick={handleSuperbridge}
            className="gap-space-6 flex cursor-pointer border border-0 text-left underline-offset-2 hover:underline"
          >
            <div className="flex w-6 items-start">
              <SuperbridgeIcon className="h-auto w-full pt-0.5" />
            </div>
            <div className="flex-1 gap-1.5">
              <div className="text-size-sm font-semibold">SUPERBRIDGE</div>
              <div className="text-size-xs text-gray-3">
                Easily bridge your Ethereum assets to Base.
              </div>
              <div className="text-size-xs text-sky">https://superbridge.app/base</div>
            </div>
          </a>
        </div>
      </div>

      {/* Continue Button */}
      <div className="p-0">
        <InfoButton onClick={onClose} size="lg" fontSize="sm" rounded="none" className="w-full">
          Continue
        </InfoButton>
      </div>
    </div>
  );
}
