import { Popup } from '@/components/ui/popup';

interface ExamplePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ExamplePopup({ isOpen, onClose }: ExamplePopupProps) {
  return (
    <Popup isOpen={isOpen} onClose={onClose}>
      <div className="w-full max-w-xl rounded-lg bg-gray-100 p-6 shadow-lg">
        <h2 className="text-size-sm mb-4 font-bold">Example</h2>
        <div className="mb-4">
          <h3 className="text-size-xs mb-1 font-semibold">Title</h3>
          <p className="text-size-xs text-gray-700">GTA VI released in 2025?</p>
        </div>
        <div className="mb-4">
          <h3 className="text-size-xs mb-1 font-semibold">Description</h3>
          <p className="text-size-xs mb-2 text-gray-700">
            This prediction will resolve to &quot;Yes&quot; if it Grand Theft Auto VI is officially
            released in the US by December 31, 2025, 11:59 PM ET.
          </p>
          <p className="text-size-xs mb-3 font-medium text-red-500">
            <u>Otherwise, this market will resolve to &quot;No&quot;.</u>
            <div className="mt-1 flex w-full justify-end">
              <img src="/images/example-alert.svg" alt="Example" />
            </div>
          </p>
          <p className="text-size-xs mb-2 text-gray-600">
            For the purposes of this market, &quot;release&quot; refers to the game becoming
            publicly available for purchase or download in the US. Early access, beta versions,
            other forms of pre-release availability, or leaks will not count as an official release.
            If the release is only for certain consoles (e.g. Xbox Series X/S) it will count.
          </p>
          <p className="text-size-xs text-gray-600">
            The resolution source will be official information from Rockstar Games or its parent
            company, Take-Two Interactive.
          </p>
        </div>
      </div>
    </Popup>
  );
}
