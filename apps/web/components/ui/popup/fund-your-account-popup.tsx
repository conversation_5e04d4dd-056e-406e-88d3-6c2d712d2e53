import { DarkButton, InfoButton } from '@/components/ui/base.button';
import { ICON_PATH } from '@/lib/constants';
import Image from 'next/image';
import React from 'react';

interface FundYourAccountPopupProps {
  onDepositFunds: () => void;
  onSkipForNow: () => void;
}

export default function FundYourAccountPopup({
  onDepositFunds,
  onSkipForNow,
}: FundYourAccountPopupProps) {
  return (
    <div className="w-full bg-white">
      {/* Header */}
      <div className="py-space-50 gap-space-30 flex flex-col items-center justify-center text-center">
        {/* Dollar Icon */}
        <div>
          <Image src={ICON_PATH.USDC} alt="dollar" width={50} height={50} />
        </div>

        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-base font-bold">Fund Your Account</h1>
          <p className="text-size-sm text-gray-3">Create deposit address</p>
        </div>
      </div>

      <div className="mb-space-30 px-space-20 flex justify-end">
        <button onClick={onSkipForNow} className="text-size-xs text-no-red underline">
          Skip for now
        </button>
      </div>

      {/* Action Button */}
      <div className="flex">
        <InfoButton onClick={onDepositFunds} fontSize="sm" height={46} className="flex-1">
          Deposit Funds
        </InfoButton>
      </div>
    </div>
  );
}
