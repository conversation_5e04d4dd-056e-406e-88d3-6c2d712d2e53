import { XsIcon } from '@/components/icons/xs-icon';
import { toast } from '@/components/ui/base.toast';
import QRCode from '@/components/ui/qr-code';
import { useGlobalStore } from '@/store/global.store';
import { BaseInput } from '../base.input';

export default function QRCodePopup() {
  const { safeSmartAccount } = useGlobalStore();

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(safeSmartAccount?.address ?? '');
    toast.success('Address copied to clipboard!');
  };

  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="gap-space-30 flex flex-col px-[25px] py-[30px] text-center">
        <h1 className="py-space-10 text-mid-dark font-semibold">QR Code</h1>

        {/* Address Input */}
        <div className="gap-space-8 flex flex-col text-left">
          <p className="text-size-xs text-mid-dark font-semibold">
            Only deposit <span className="text-sky font-medium">Base USDC</span> to this address.
          </p>
          <div className="gap-space-8 relative flex items-center">
            <BaseInput
              value={safeSmartAccount?.address ?? ''}
              readOnly
              className="bg-white pr-10"
            />
            <div className="absolute top-1/2 right-3 flex -translate-y-1/2 items-center">
              <button
                onClick={handleCopyAddress}
                className="p-space-1 hover:bg-gray-1 rounded-round-sm transition-colors"
              >
                <XsIcon name="copy" />
              </button>
            </div>
          </div>
        </div>

        {/* QR Code Section */}
        {safeSmartAccount?.address && <QRCode address={safeSmartAccount.address} />}
      </div>

      {/* Continue Button */}
      {/* <div className="p-0">
        <InfoButton onClick={onClose} size="lg" fontSize="sm" rounded="none" className="w-full">
          Continue
        </InfoButton>
      </div> */}
    </div>
  );
}
