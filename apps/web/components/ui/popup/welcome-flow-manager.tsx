'use client';

import { useEffect, useState, useCallback } from 'react';
import { useCurrentUser } from '@/hooks/query/user';
import { usePopupStore } from './popup.state';
import WelcomeFlowPopup from './welcome-flow-popup';
import { useCreateUser } from '@/hooks/query/user/use-create-user';
import { useGlobalStore } from '@/store/global.store';

const getUrlParams = () => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      forceShowWelcome: urlParams.get('showWelcome') === 'true',
      referralCode: urlParams.get('referralCode') || undefined,
    };
  }
  return { forceShowWelcome: false, referralCode: undefined };
};

export function WelcomeFlowManager() {
  const { isLoading, error, isSignedIn, refetch } = useCurrentUser();
  const { safeSmartAccount } = useGlobalStore();
  const { openPopup, closePopup, isOpen } = usePopupStore();
  const createUser = useCreateUser();

  // 사용자 생성 중 상태 관리 (무한 요청 방지)
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  // 이미 처리된 주소 추적 (중복 처리 방지)
  const [processedAddresses, setProcessedAddresses] = useState<Set<string>>(new Set());

  // 주소가 변경되면 처리된 주소 목록 초기화
  useEffect(() => {
    if (safeSmartAccount?.address) {
      setProcessedAddresses(new Set());
    }
  }, [safeSmartAccount?.address]);

  const handleCreateNewUser = useCallback(
    async (referralCode?: string) => {
      if (!safeSmartAccount?.address || isCreatingUser) return;

      // 이미 처리된 주소인지 확인
      if (processedAddresses.has(safeSmartAccount.address)) return;

      setIsCreatingUser(true);

      try {
        // referralCode가 있으면 포함하여 사용자 생성
        const createUserData = referralCode ? { referralCode } : {};
        await createUser.mutateAsync(createUserData);
        await refetch();
        // 처리된 주소로 기록
        setProcessedAddresses(prev => new Set(prev).add(safeSmartAccount.address));

        // 사용자 생성 후 환영 플로우 표시
        openPopup(
          <WelcomeFlowPopup
            onClose={() => {
              closePopup();
            }}
          />
        );
      } catch (error) {
        console.error('Failed to create user', error);
        // 실패 시 처리된 주소에서 제거 (재시도 가능하도록)
        setProcessedAddresses(prev => {
          const newSet = new Set(prev);
          newSet.delete(safeSmartAccount.address);
          return newSet;
        });
      } finally {
        setIsCreatingUser(false);
      }
    },
    [
      safeSmartAccount?.address,
      isCreatingUser,
      processedAddresses,
      refetch,
      createUser,
      openPopup,
      closePopup,
    ]
  );

  useEffect(() => {
    const { forceShowWelcome, referralCode } = getUrlParams();

    if (forceShowWelcome) {
      if (!isOpen) {
        openPopup(
          <WelcomeFlowPopup
            onClose={() => {
              closePopup();
            }}
          />
        );
      }
      return;
    }

    if (!isSignedIn || isLoading) return;
    if (isOpen) return;
    if (isCreatingUser) return; // 사용자 생성 중이면 대기
    const isNewUser = error && error.message.includes('404');
    const currentAddress = safeSmartAccount?.address;

    if (isNewUser && currentAddress && !processedAddresses.has(currentAddress)) {
      handleCreateNewUser(referralCode);
    }
  }, [
    isSignedIn,
    isLoading,
    error,
    isOpen,
    isCreatingUser,
    safeSmartAccount?.address,
    processedAddresses,
    handleCreateNewUser,
    closePopup,
    openPopup,
  ]);

  return null;
}
