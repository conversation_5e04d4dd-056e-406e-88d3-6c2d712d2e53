import { useCallback } from 'react';
import { generateTransferURL } from '@/lib/format';

interface QRCodeProps {
  address: string;
  width?: number;
  height?: number;
  className?: string;
}

export default function QRCode({
  address,
  width = 200,
  height = 200,
  className = 'flex items-center justify-center',
}: QRCodeProps) {
  const qrRefCallback = useCallback(
    (node: HTMLDivElement | null) => {
      if (node && address) {
        // 이미 QR 코드가 생성되었는지 확인
        if (node.hasChildNodes()) {
          return;
        }

        const usdcTransferURL = generateTransferURL(address);

        // Import QRCodeStyling dynamically for client-side rendering
        import('qr-code-styling').then(({ default: QRCodeStyling }) => {
          // 다시 한번 체크 (비동기로 인한 중복 방지)
          if (node.hasChildNodes()) {
            return;
          }

          const qrCode = new QRCodeStyling({
            width,
            height,
            type: 'svg',
            data: usdcTransferURL,
            // qrOptions: {
            //   typeNumber: 0,
            //   mode: 'Byte',
            //   errorCorrectionLevel: 'Q',
            // },
            image: '/assets/icons/base-network-logo.svg',
            imageOptions: {
              hideBackgroundDots: true,
              imageSize: 0.22,
              margin: 3,
            },
            dotsOptions: {
              color: '#000000', // Base blue color
              type: 'dots',
            },
            backgroundOptions: {
              color: '#ffffff',
            },
            cornersSquareOptions: {
              color: '#000000',
              type: 'extra-rounded',
            },
            cornersDotOptions: {
              color: '#000000',
              type: 'extra-rounded',
            },
          });

          qrCode.append(node);
        });
      }
    },
    [address, width, height]
  );

  if (!address) {
    return null;
  }

  return <div ref={qrRefCallback} className={className} />;
}
