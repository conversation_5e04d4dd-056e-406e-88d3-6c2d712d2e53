import React, { useCallback } from 'react';
import { cn } from '@repo/ui/lib/utils';
import { BaseTextarea } from './base.textarea';
import type { ComponentProps } from 'react';
import { allowedInputRegex } from '@/lib/utils';

// 글자수 제한 체크 함수
const truncateText = (text: string, maxLength: number): string => {
  if (getFormDataLength(text) <= maxLength) {
    return text;
  }

  // FormData 길이가 초과하면 한 글자씩 줄여가며 확인
  let truncated = text;
  while (getFormDataLength(truncated) > maxLength && truncated.length > 0) {
    truncated = truncated.slice(0, -1);
  }
  return truncated;
};

// FormData 전송 시 실제 길이 계산 함수 (\n -> \r\n 변환 고려)
const getFormDataLength = (text: string): number => {
  const newlineCount = (text.match(/\n/g) || []).length;
  return text.length + newlineCount; // \n 하나당 +1 (총 2글자가 됨)
};

// 글자수 표시 컴포넌트
const CharacterCount = ({
  current,
  max,
  className = '',
}: {
  current: number;
  max: number;
  className?: string;
}) => {
  const isNearLimit = current > max * 0.8;
  const isOverLimit = current > max;

  return (
    <div
      className={cn(
        'text-xs',
        {
          'text-red-500': isOverLimit,
          'text-yellow-600': isNearLimit && !isOverLimit,
          'text-gray-500': !isNearLimit && !isOverLimit,
        },
        className
      )}
    >
      {current}/{max}
    </div>
  );
};

interface TextareaWithCharacterCountProps
  extends Omit<ComponentProps<typeof BaseTextarea>, 'onChange'> {
  maxLength: number;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  characterCountClassName?: string;
  onValueChange?: (value: string) => void; // 값만 받는 추가 콜백
}

export function TextareaWithCharacterCount({
  className,
  maxLength,
  value = '',
  onChange,
  onValueChange,
  characterCountClassName,
  ...props
}: TextareaWithCharacterCountProps) {
  // 붙여넣기 이벤트 핸들러 생성 함수
  const createPasteHandler = useCallback((maxLength: number, setValue: (value: string) => void) => {
    return (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
      const pastedText = e.clipboardData.getData('text');
      if (!pastedText) return; // 빈 텍스트면 기본 동작 허용

      // 길이 체크 후 잘라내기
      if (pastedText.length > maxLength) {
        e.preventDefault(); // 긴 텍스트만 기본 동작 방지
        const truncatedText = truncateText(pastedText, maxLength);

        // 다음 이벤트 루프에서 값 설정
        setTimeout(() => {
          setValue(truncatedText);
        }, 0);
      }
      // 길이가 적절하면 기본 붙여넣기 동작 허용
    };
  }, []);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;

      // 빈 문자열이 아닐 때만 regex 검증
      if (value !== '' && !allowedInputRegex.test(value)) {
        return;
      }

      const truncatedValue = truncateText(value, maxLength);

      // 실제 textarea value를 업데이트
      e.target.value = truncatedValue;

      // onChange 콜백 호출
      if (onChange) {
        onChange(e);
      }

      // onValueChange 콜백 호출 (값만 전달)
      if (onValueChange) {
        onValueChange(truncatedValue);
      }
    },
    [maxLength, onChange, onValueChange]
  );

  const handlePaste = createPasteHandler(maxLength, newValue => {
    if (onValueChange) {
      onValueChange(newValue);
    }

    // onChange 콜백도 호출 (이벤트 객체 형태로)
    if (onChange) {
      const syntheticEvent = {
        target: { value: newValue },
      } as React.ChangeEvent<HTMLTextAreaElement>;
      onChange(syntheticEvent);
    }
  });

  return (
    <div className="relative">
      <textarea
        className={cn(
          'border-line bg-gray-2 placeholder:text-icon-gray py-space-12 text-size-xs px-space-12 flex min-h-[120px] w-full max-w-full items-center rounded-none border pr-16 text-wrap break-words',
          className
        )}
        value={value}
        onChange={handleChange}
        onPaste={handlePaste}
        maxLength={maxLength}
        {...props}
      />
      <CharacterCount
        current={getFormDataLength(value)}
        max={maxLength}
        className={cn('absolute right-3 bottom-3', characterCountClassName)}
      />
    </div>
  );
}
