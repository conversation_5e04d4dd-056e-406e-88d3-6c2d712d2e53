# Form Error UI 가이드

React Hook Form + Zod + Form 컴포넌트를 사용할 때 validation error에 대한 시각적 피드백을 제공하는 방법에 대한 가이드입니다.

## 기본 개념

### 필요한 패키지

- `react-hook-form`: 폼 상태 관리
- `zod`: 스키마 기반 validation
- `@repo/ui/components/form`: Form 컴포넌트들
- `@repo/ui/lib/utils`: `cn` 유틸리티 함수

### 핵심 원칙

- **일관성**: 모든 입력 필드에 동일한 error 스타일 적용
- **명확성**: 빨간색 테두리로 error 상태를 명확히 표시
- **접근성**: FormMessage 컴포넌트로 screen reader 지원

## 에러 메시지 결정 방식

에러 메시지는 다음과 같은 순서로 결정됩니다:

### 1. Zod 스키마에서 정의된 메시지

Zod 스키마에서 validation 규칙과 함께 에러 메시지를 정의할 수 있습니다:

```tsx
export const CreateMarketFormSchema = z.object({
  title: z
    .string()
    .min(2, 'Title must be at least 2 characters long')  // 커스텀 메시지
    .max(100, 'Title cannot exceed 100 characters'),     // 커스텀 메시지
  
  collateralAmount: z.number().min(0.01, 'Please enter a deposit amount'),
  
  category: z.string().min(2, 'Please select a category'),
  
  outcomes: z
    .array(z.string().min(1, 'Outcome cannot be empty'))
    .min(2, 'At least 2 outcomes are required')
    .max(10, 'Maximum 10 outcomes allowed'),
  
  broadcastURL: z.string().url('Please enter a valid URL format').optional(),
});
```

### 2. 복합 validation (refine)

복잡한 validation 로직의 경우 `refine` 메서드를 사용하여 커스텀 에러 메시지를 정의:

```tsx
export const CreateMarketFormSchema = z
  .object({
    predictionDeadline: z.number().min(1, 'Please set a prediction end time'),
    resultConfirmDeadline: z.number().min(1, 'Please set a result confirmation end time'),
  })
  .refine(
    data => {
      return data.resultConfirmDeadline > data.predictionDeadline;
    },
    {
      message: 'Result confirmation end time must be later than prediction end time',
      path: ['resultConfirmDeadline'], // 에러가 표시될 필드 지정
    }
  );
```

### 3. 배열 필드의 개별 항목 에러

배열 내 개별 항목에 대한 에러 메시지:

```tsx
outcomes: z
  .array(
    z.string()
      .min(1, 'Outcome cannot be empty')           // 개별 항목 에러
      .max(50, 'Outcome cannot exceed 50 characters')
  )
  .min(2, 'At least 2 outcomes are required')     // 배열 전체 에러
  .max(10, 'Maximum 10 outcomes allowed'),
```

### 4. FormMessage 컴포넌트의 역할

`FormMessage` 컴포넌트는 자동으로 해당 필드의 에러 메시지를 표시합니다:

```tsx
<FormField
  control={control}
  name="title"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Title</FormLabel>
      <FormControl>
        <BaseInput {...field} />
      </FormControl>
      <FormMessage /> {/* 자동으로 title 필드의 에러 메시지 표시 */}
    </FormItem>
  )}
/>
```

### 5. 에러 메시지 우선순위

1. **Zod validation 에러**: 스키마에서 정의된 메시지가 최우선
2. **React Hook Form 내장 에러**: required, pattern 등
3. **기본 브라우저 validation**: HTML5 validation 메시지

### 6. 실시간 에러 메시지 업데이트

에러 메시지는 다음 상황에서 업데이트됩니다:

- **입력 시**: `mode: 'onChange'` 설정 시 실시간 validation
- **포커스 아웃 시**: `mode: 'onBlur'` 설정 시
- **제출 시**: `mode: 'onSubmit'` (기본값)

```tsx
const form = useForm<CreateMarketFormValues>({
  resolver: zodResolver(CreateMarketFormSchema),
  mode: 'onChange', // 실시간 validation 활성화
  defaultValues: {
    title: '',
    // ...
  },
});
```

### 7. 커스텀 에러 메시지 예시

다양한 validation 타입별 에러 메시지 예시:

```tsx
const schema = z.object({
  // 문자열 길이 validation
  username: z
    .string()
    .min(2, 'Username must be at least 2 characters')
    .max(50, 'Username cannot exceed 50 characters'),
  
  // 이메일 validation
  email: z
    .string()
    .email('Please enter a valid email address'),
  
  // 숫자 범위 validation
  age: z
    .number()
    .min(18, 'You must be at least 18 years old')
    .max(120, 'Please enter a valid age'),
  
  // URL validation
  website: z
    .string()
    .url('Please enter a valid URL')
    .optional(),
  
  // 정규식 validation
  phone: z
    .string()
    .regex(/^\d{3}-\d{3}-\d{4}$/, 'Phone number must be in format ************'),
  
  // 배열 validation
  tags: z
    .array(z.string())
    .min(1, 'At least one tag is required')
    .max(5, 'Maximum 5 tags allowed'),
});
```

## 기본 구현 패턴

### 1. 필수 imports

```tsx
import { Control, useFormState } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { cn } from '@repo/ui/lib/utils';
```

### 2. useFormState 훅 사용

컴포넌트 내에서 form의 error 상태를 가져옵니다:

```tsx
export function MyFormSection({ control }: { control: Control<FormValues> }) {
  const { errors } = useFormState({ control });
  
  // ...
}
```

### 3. 기본 Input 필드 에러 처리

```tsx
<FormField
  control={control}
  name="fieldName"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Field Label</FormLabel>
      <FormControl>
        <BaseInput
          placeholder="Enter value"
          {...field}
          className={cn(errors.fieldName ? 'border-red-500 focus:border-red-500' : '')}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

## 컴포넌트별 구현 예시

### BaseInput 컴포넌트

```tsx
<BaseInput
  placeholder={PLACEHOLDER_TEXT['reference-url']}
  {...field}
  className={cn(errors.referenceURL ? 'border-red-500 focus:border-red-500' : '')}
/>
```

### BaseSelect 컴포넌트

```tsx
<BaseSelect onValueChange={field.onChange} value={field.value}>
  <BaseSelectTrigger
    className={cn(
      'bg-gray-2 w-full',
      errors.category ? 'border-red-500 focus:border-red-500' : ''
    )}
  >
    <BaseSelectValue placeholder={PLACEHOLDER_TEXT.category} />
  </BaseSelectTrigger>
  <BaseSelectContent>
    {/* options */}
  </BaseSelectContent>
</BaseSelect>
```

### TextareaWithCharacterCount 컴포넌트

```tsx
<TextareaWithCharacterCount
  placeholder={PLACEHOLDER_TEXT.description}
  value={field.value}
  maxLength={2000}
  onValueChange={field.onChange}
  className={cn(errors.description ? 'border-red-500 focus:border-red-500' : '')}
/>
```

### DollarInput 컴포넌트

```tsx
<DollarInput
  placeholder={PLACEHOLDER_TEXT.deposit}
  value={field.value || 0}
  onChange={field.onChange}
  minValue={minValue}
  maxValue={maxValue}
  className={cn(errors.collateralAmount ? 'border-red-500 focus:border-red-500' : '')}
/>
```

### InputWithCharacterCount 컴포넌트

```tsx
<InputWithCharacterCount
  placeholder={PLACEHOLDER_TEXT['market-title']}
  maxLength={100}
  value={field.value}
  onChange={e => {
    field.onChange(e.target.value);
    onTitleChange(e.target.value);
  }}
  className={cn(
    titleEthicalReviewFailed ? 'border-red-500 focus:border-red-500' : '',
    errors.title ? 'border-red-500 focus:border-red-500' : ''
  )}
/>
```

## 고급 패턴

### 1. 배열 필드 에러 처리

outcomes 같은 배열 필드의 개별 항목에 대한 에러 처리:

```tsx
{outcomes.map((outcome, index) => (
  <InputWithCharacterCount
    key={index}
    className={cn(
      duplicateOutcomes.includes(index)
        ? 'border-red-500 focus:border-red-500'
        : '',
      errors.outcomes?.[index] ? 'border-red-500 focus:border-red-500' : ''
    )}
    placeholder={PLACEHOLDER_TEXT['market-outcomes']}
    value={outcome}
    maxLength={50}
    onValueChange={value => onUpdateOutcome(index, value)}
  />
))}
```

### 2. 복합 조건 에러 처리

여러 조건을 조합한 에러 스타일링:

```tsx
className={cn(
  // 비즈니스 로직 에러 (예: ethical review 실패)
  titleEthicalReviewFailed ? 'border-red-500 focus:border-red-500' : '',
  // Form validation 에러
  errors.title ? 'border-red-500 focus:border-red-500' : ''
)}
```

### 3. 커스텀 validation 에러

store나 비즈니스 로직에서 발생하는 에러와 함께 처리:

```tsx
className={cn(
  // 커스텀 에러 (예: 중복 검사)
  duplicateOutcomes.includes(index) ? 'border-red-500 focus:border-red-500' : '',
  // Form validation 에러
  errors.outcomes?.[index] ? 'border-red-500 focus:border-red-500' : ''
)}
```

## 스타일링 규칙

### 에러 테두리 클래스

- `border-red-500`: 기본 상태의 빨간색 테두리
- `focus:border-red-500`: 포커스 상태의 빨간색 테두리

### cn 유틸리티 사용

```tsx
// ✅ 권장: cn 함수로 조건부 클래스 적용
className={cn(errors.fieldName ? 'border-red-500 focus:border-red-500' : '')}

// ❌ 비권장: 직접 문자열 조작
className={errors.fieldName ? 'border-red-500 focus:border-red-500' : ''}
```

## 접근성 고려사항

### FormMessage 컴포넌트

모든 FormField에는 반드시 `<FormMessage />` 컴포넌트를 포함하여 screen reader 사용자에게 에러 메시지를 제공합니다:

```tsx
<FormField
  control={control}
  name="fieldName"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Field Label</FormLabel>
      <FormControl>
        <BaseInput {...field} />
      </FormControl>
      <FormMessage /> {/* 필수 */}
    </FormItem>
  )}
/>
```

## 체크리스트

폼 에러 UI를 구현할 때 다음 사항들을 확인하세요:

- [ ] `useFormState` 훅으로 errors 상태 가져오기
- [ ] 모든 입력 컴포넌트에 에러 스타일 적용
- [ ] `cn` 유틸리티 함수 사용
- [ ] `border-red-500 focus:border-red-500` 클래스 적용
- [ ] `FormMessage` 컴포넌트 포함
- [ ] 배열 필드의 개별 항목 에러 처리
- [ ] 커스텀 validation과 form validation 조합 처리
- [ ] Zod 스키마에 명확한 에러 메시지 정의
- [ ] 복합 validation에 적절한 path 지정

## 실제 프로젝트 예시

이 가이드는 `app/(main)/create-prediction/` 페이지의 구현을 기반으로 작성되었습니다. 다음 컴포넌트들에서 실제 구현을 확인할 수 있습니다:

- `_components/deposit-section.tsx`
- `_components/urls-description-section.tsx`
- `_components/quest-section.tsx`
- `_components/datetime-section.tsx`
- `_components/category-tags-section.tsx`
