# Tooltip Component Usage Guide

This guide explains how to use the Tooltip component in the PredictGo frontend application.

## Import

```tsx
import { Tooltip, TooltipTrigger, TooltipContent } from '@repo/ui/components/tooltip';
```

## Basic Usage

The Tooltip component consists of three main parts:

1. **`Tooltip`** - The wrapper component
2. **`TooltipTrigger`** - The element that triggers the tooltip (what you hover over)
3. **`TooltipContent`** - The content that appears in the tooltip

### Simple Example

```tsx
<Tooltip>
  <TooltipTrigger>
    <button>Hover me</button>
  </TooltipTrigger>
  <TooltipContent>
    <p>This is a tooltip message</p>
  </TooltipContent>
</Tooltip>
```

## Real-World Examples from Codebase

### Example 1: Information Icon with Warning (MarketAuthor.tsx)

```tsx
<Tooltip>
  <TooltipTrigger>
    <SvgIcon name="RedAlertIcon" />
  </TooltipTrigger>
  <TooltipContent>
    <p>Minimum 10 predictions needed.</p>
    <p>All predicts refunded if not met.</p>
  </TooltipContent>
</Tooltip>
```

**Use case**: Providing additional context for warning indicators.

### Example 2: Help Information for Complex Features (predict-control-panel.tsx)

```tsx
<Tooltip>
  <TooltipTrigger>
    <SvgIcon name="RedAlertIcon" />
  </TooltipTrigger>
  <TooltipContent>
    <p>
      Maximum predictable volume per
      <br />
      outcome set by the channel owner
    </p>
  </TooltipContent>
</Tooltip>
```

**Use case**: Explaining complex business logic or rules to users.

## Best Practices

### 1. Content Structure

```tsx
// ✅ Good: Clear, concise content
<TooltipContent>
  <p>Short, helpful explanation</p>
</TooltipContent>

// ✅ Good: Multiple paragraphs for complex info
<TooltipContent>
  <p>First important point.</p>
  <p>Second important point.</p>
</TooltipContent>

// ✅ Good: Line breaks for formatting
<TooltipContent>
  <p>
    Line one
    <br />
    Line two
  </p>
</TooltipContent>
```

### 2. Trigger Elements

```tsx
// ✅ Good: Interactive elements (buttons, icons)
<TooltipTrigger>
  <SvgIcon name="InfoIcon" />
</TooltipTrigger>

<TooltipTrigger>
  <button className="info-button">?</button>
</TooltipTrigger>

// ✅ Good: Wrapping text that needs explanation
<TooltipTrigger>
  <span className="underlined-text">Technical term</span>
</TooltipTrigger>
```

### 3. Common Use Cases

#### Information Icons

```tsx
<div className="flex items-center gap-space-5">
  <span>Complex Feature</span>
  <Tooltip>
    <TooltipTrigger>
      <SvgIcon name="InfoIcon" />
    </TooltipTrigger>
    <TooltipContent>
      <p>Detailed explanation of the feature</p>
    </TooltipContent>
  </Tooltip>
</div>
```

#### Form Field Help

```tsx
<div className="form-field">
  <label className="flex items-center gap-space-5">
    Field Name
    <Tooltip>
      <TooltipTrigger>
        <SvgIcon name="HelpIcon" />
      </TooltipTrigger>
      <TooltipContent>
        <p>Instructions for filling this field</p>
      </TooltipContent>
    </Tooltip>
  </label>
  <input type="text" />
</div>
```

#### Status Indicators

```tsx
<div className="status-section">
  <span>Status: Active</span>
  <Tooltip>
    <TooltipTrigger>
      <SvgIcon name="RedAlertIcon" />
    </TooltipTrigger>
    <TooltipContent>
      <p>Warning: Action required within 24 hours</p>
    </TooltipContent>
  </Tooltip>
</div>
```

## Styling Integration

The tooltip component integrates with the existing design system. Common patterns:

```tsx
// Integration with spacing classes
<div className="gap-space-5 flex items-center">
  <span>Label</span>
  <Tooltip>
    <TooltipTrigger>
      <SvgIcon name="InfoIcon" />
    </TooltipTrigger>
    <TooltipContent>
      <p>Tooltip content</p>
    </TooltipContent>
  </Tooltip>
</div>

// Integration with text sizing
<h3 className="text-size-sm font-semibold">
  Section Title
  <Tooltip>
    <TooltipTrigger>
      <SvgIcon name="InfoIcon" className="ml-space-5" />
    </TooltipTrigger>
    <TooltipContent>
      <p>Section explanation</p>
    </TooltipContent>
  </Tooltip>
</h3>
```

## Accessibility Notes

- The tooltip component should handle keyboard navigation automatically
- Ensure trigger elements are focusable (buttons, links, or elements with `tabIndex`)
- Keep tooltip content concise and readable
- Don't rely solely on tooltips for critical information

## When NOT to Use Tooltips

❌ **Avoid tooltips for:**

- Critical information that users must see
- Long content (use modal/dropdown instead)
- Mobile-first interfaces (poor touch experience)
- Information that's obvious from context

✅ **Use tooltips for:**

- Supplementary information
- Icon explanations
- Technical term definitions
- Help text for form fields
- Status clarifications

## Component Architecture

```
Tooltip (wrapper)
├── TooltipTrigger (hover/focus target)
│   └── [Your trigger element]
└── TooltipContent (popup content)
    └── [Your tooltip content]
```

This structure ensures proper event handling and positioning for the tooltip functionality.
