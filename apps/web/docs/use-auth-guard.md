# useAuthGuard Hook Documentation

## Overview

`useAuthGuard` is a custom React hook that provides authentication protection for functions. It ensures that certain actions can only be executed by authenticated users, automatically redirecting unauthenticated users to the login flow.

## Purpose

- Protect functions from being executed by unauthenticated users
- Automatically trigger login flow when authentication is required
- Provide a clean, reusable pattern for authentication checks

## API

### Return Value

The hook returns an object with the following property:

- `withAuthCheck`: A higher-order function that wraps any function with authentication protection

### withAuthCheck

```typescript
withAuthCheck<T extends any[]>(fn: (...args: T) => void): (...args: T) => void
```

**Parameters:**

- `fn`: The function to be protected with authentication check

**Returns:**

- A wrapped function that performs authentication check before executing the original function

## Usage Examples

### Basic Usage

```tsx
import { useAuthGuard } from '@/hooks/use-auth-guard';

function MyComponent() {
  const { withAuthCheck } = useAuthGuard();

  const handleProtectedAction = withAuthCheck(() => {
    // This code only runs if user is authenticated
    console.log('User is authenticated, executing action...');
  });

  return (
    <button onClick={handleProtectedAction}>
      Protected Action
    </button>
  );
}
```

### With Parameters

```tsx
import { useAuthGuard } from '@/hooks/use-auth-guard';

function MarketComponent() {
  const { withAuthCheck } = useAuthGuard();

  const handleBuyShares = withAuthCheck((marketId: string, amount: number) => {
    // This function only executes if user is authenticated
    console.log(`Buying ${amount} shares for market ${marketId}`);
    // Execute buy logic here
  });

  return (
    <button onClick={() => handleBuyShares('market-123', 100)}>
      Buy Shares
    </button>
  );
}
```

### Form Submission

```tsx
import { useAuthGuard } from '@/hooks/use-auth-guard';

function CreatePredictionForm() {
  const { withAuthCheck } = useAuthGuard();

  const handleSubmit = withAuthCheck((formData: FormData) => {
    // Form submission only proceeds if user is authenticated
    console.log('Creating prediction with data:', formData);
    // Submit form logic here
  });

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);
      handleSubmit(formData);
    }}>
      {/* Form fields */}
      <button type="submit">Create Prediction</button>
    </form>
  );
}
```

### Multiple Protected Functions

```tsx
import { useAuthGuard } from '@/hooks/use-auth-guard';

function UserDashboard() {
  const { withAuthCheck } = useAuthGuard();

  const handleEditProfile = withAuthCheck(() => {
    // Edit profile logic
  });

  const handleDeleteAccount = withAuthCheck(() => {
    // Delete account logic
  });

  const handleWithdraw = withAuthCheck((amount: number) => {
    // Withdrawal logic
  });

  return (
    <div>
      <button onClick={handleEditProfile}>Edit Profile</button>
      <button onClick={handleDeleteAccount}>Delete Account</button>
      <button onClick={() => handleWithdraw(100)}>Withdraw $100</button>
    </div>
  );
}
```

## Behavior

### Authenticated User

- The wrapped function executes normally
- All parameters are passed through to the original function

### Unauthenticated User

- The `login()` function is called automatically
- The original function is **not** executed
- User is redirected to the authentication flow

## Implementation Details

The hook uses:

- `useGlobalStore` to check authentication status via `isSignedIn`
- `login()` function from `@/lib/auth` to initiate authentication
- `useCallback` for performance optimization to prevent unnecessary re-renders

## Best Practices

1. **Use for User Actions**: Apply to functions that require authentication (e.g., trading, creating content, profile updates)

2. **Don't Overuse**: Not every function needs authentication protection. Use only where necessary.

3. **Combine with UI State**: Consider showing different UI states based on authentication status:

```tsx
const isSignedIn = useGlobalStore(v => v.isSignedIn);
const { withAuthCheck } = useAuthGuard();

return (
  <div>
    {isSignedIn ? (
      <button onClick={withAuthCheck(handleProtectedAction)}>
        Protected Action
      </button>
    ) : (
      <button onClick={() => login()}>
        Login to Continue
      </button>
    )}
  </div>
);
```

4. **Error Handling**: The hook handles authentication, but you should still handle other errors in your protected functions:

```tsx
const handleProtectedAction = withAuthCheck(async () => {
  try {
    await someApiCall();
  } catch (error) {
    // Handle API errors
    console.error('Action failed:', error);
  }
});
```

## Related

- `@/lib/auth` - Authentication utilities
- `@/store/global.store` - Global state management
- `useGlobalStore` - Global state hook
