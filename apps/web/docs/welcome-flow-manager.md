# WelcomeFlowManager 분석 문서

## 개요

`WelcomeFlowManager`는 신규 사용자의 온보딩 프로세스를 자동으로 관리하는 핵심 컴포넌트입니다. 사용자의 첫 방문을 감지하고, 계정 생성부터 환영 플로우까지 전체 온보딩 경험을 조율합니다.

## 코어 로직

### 1. 신규 사용자 감지 메커니즘

```typescript
const isNewUser = error && error.message.includes('404');
```

- `useCurrentUser` 훅에서 404 에러 발생 시 신규 사용자로 판단
- `useUserWithAutoCreate`에서 사용자 조회 실패 시 명시적으로 404 에러 던짐

### 2. 자동 사용자 생성 프로세스

```typescript
const handleCreateNewUser = useCallback(async (referralCode?: string) => {
  // 중복 처리 방지 검사
  if (!safeSmartAccount?.address || isCreatingUser) return;
  if (processedAddresses.has(safeSmartAccount.address)) return;

  setIsCreatingUser(true);
  
  try {
    // 사용자 생성 (referralCode 포함 가능)
    const createUserData = referralCode ? { referralCode } : {};
    await createUser.mutateAsync(createUserData);
    
    // 처리 완료 기록
    setProcessedAddresses(prev => new Set(prev).add(safeSmartAccount.address));
    
    // 환영 플로우 시작
    openPopup(<WelcomeFlowPopup onClose={() => closePopup()} />);
  } catch (error) {
    // 실패 시 재시도 가능하도록 처리
    setProcessedAddresses(prev => {
      const newSet = new Set(prev);
      newSet.delete(safeSmartAccount.address);
      return newSet;
    });
  } finally {
    setIsCreatingUser(false);
  }
}, [dependencies]);
```

### 3. URL 파라미터 지원

```typescript
const getUrlParams = () => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      forceShowWelcome: urlParams.get('showWelcome') === 'true',
      referralCode: urlParams.get('referralCode') || undefined,
    };
  }
  return { forceShowWelcome: false, referralCode: undefined };
};
```

**지원 파라미터:**

- `?showWelcome=true`: 강제 환영 플로우 표시
- `?referralCode=ABC123`: 추천 코드와 함께 사용자 생성

### 4. 무한 요청 방지 메커니즘

```typescript
// 상태 관리
const [isCreatingUser, setIsCreatingUser] = useState(false);
const [processedAddresses, setProcessedAddresses] = useState<Set<string>>(new Set());

// 중복 처리 방지
if (isCreatingUser) return;
if (processedAddresses.has(currentAddress)) return;
```

## 관련 코드와의 연관성 분석

### 1. 인증 및 사용자 관리 시스템

#### useCurrentUser 훅 체인

```
WelcomeFlowManager
├── useCurrentUser()
│   ├── useGlobalStore() → safeSmartAccount, isSignedIn
│   ├── useAccount() → 지갑 연결 상태
│   └── useUserWithAutoCreate() → 사용자 조회/404 에러 처리
```

#### 사용자 생성 플로우

```
WelcomeFlowManager
├── useCreateUser() → userService.createUser()
│   └── CreateUserRequestBodySchema (partial)
│       └── { referralCode?: string }
```

### 2. 전역 상태 관리

#### GlobalStore 연동

```typescript
// store/global.store.ts
export const useGlobalStore = create(
  combine({
    isSignedIn: false,
    session: null as SIWESession | null,
    safeSmartAccount: undefined as SafeSmartAccount | undefined,
  })
);
```

**상태 흐름:**

1. 지갑 연결 → `safeSmartAccount` 설정
2. SIWE 인증 → `session` 설정
3. 두 조건 충족 → `isSignedIn: true`

### 3. 팝업 시스템 통합

#### Popup State 관리

```typescript
// popup.state.tsx
export const usePopupStore = create(
  combine({
    jsx: null as React.ReactNode | null,
    isOpen: false,
  })
);
```

**팝업 계층 구조:**

```
PopupProvider (Root Layout)
├── Popup Component
│   └── WelcomeFlowPopup
│       ├── WelcomePopup
│       ├── ChooseUsernamePopup
│       ├── ChooseEmailPopup
│       ├── FundYourAccountPopup
│       └── DepositPopup
```

### 4. 환영 플로우 단계

#### WelcomeFlowPopup 내부 단계

```typescript
type WelcomeFlowStep = 'welcome' | 'choose-username' | 'choose-email' | 'fund-account' | 'deposit';

// 단계별 컴포넌트 매핑
const stepComponents = {
  'welcome': WelcomePopup,
  'choose-username': ChooseUsernamePopup,
  'choose-email': ChooseEmailPopup,
  'fund-account': FundYourAccountPopup,
  'deposit': DepositPopup
};
```

### 5. 스마트 계정 시스템

#### SafeSmartAccount 생성 플로우

```
useSafeSmartAccount()
├── getWalletClient() → EOA 지갑
├── getSmartAccount() → Safe 스마트 계정 생성
└── setSafeSmartAccount() → 전역 상태 업데이트
```

## 아키텍처 다이어그램

```mermaid
graph TD
    A[사용자 접속] --> B{지갑 연결?}
    B -->|No| C[대기]
    B -->|Yes| D{SIWE 인증?}
    D -->|No| C
    D -->|Yes| E[useCurrentUser 실행]
    E --> F{사용자 존재?}
    F -->|Yes| G[정상 진행]
    F -->|No| H[404 에러 발생]
    H --> I[WelcomeFlowManager 감지]
    I --> J{이미 처리된 주소?}
    J -->|Yes| K[무시]
    J -->|No| L[사용자 생성 시작]
    L --> M[createUser.mutateAsync]
    M --> N{성공?}
    N -->|No| O[에러 처리 & 재시도 허용]
    N -->|Yes| P[처리 완료 기록]
    P --> Q[WelcomeFlowPopup 표시]
    Q --> R[온보딩 플로우 진행]
```

## 데이터 플로우

### 1. 입력 데이터

- **URL 파라미터**: `showWelcome`, `referralCode`
- **전역 상태**: `safeSmartAccount`, `isSignedIn`
- **사용자 조회 결과**: `error` (404 감지용)

### 2. 처리 과정

- **중복 방지**: `processedAddresses` Set으로 관리
- **상태 추적**: `isCreatingUser` 플래그
- **사용자 생성**: `useCreateUser` 뮤테이션

### 3. 출력 결과

- **팝업 표시**: `WelcomeFlowPopup` 렌더링
- **상태 업데이트**: 쿼리 캐시 무효화
- **에러 처리**: 콘솔 로그 및 재시도 허용

## 성능 최적화

### 1. 메모이제이션

```typescript
const handleCreateNewUser = useCallback(async (referralCode?: string) => {
  // ... 로직
}, [safeSmartAccount?.address, isCreatingUser, processedAddresses, createUser, openPopup, closePopup]);
```

### 2. 조건부 실행

```typescript
// 불필요한 실행 방지
if (!isSignedIn || isLoading) return;
if (isOpen) return;
if (isCreatingUser) return;
```

### 3. 의존성 최적화

```typescript
// 필요한 의존성만 포함
useEffect(() => {
  // ... 로직
}, [
  isSignedIn,
  isLoading,
  error,
  isOpen,
  isCreatingUser,
  safeSmartAccount?.address,
  processedAddresses,
  handleCreateNewUser,
]);
```

## 에러 처리 전략

### 1. 404 에러 감지

```typescript
const isNewUser = error && error.message.includes('404');
```

### 2. 사용자 생성 실패 처리

```typescript
} catch (error) {
  console.error('사용자 생성 중 오류 발생:', error);
  // 실패 시 처리된 주소에서 제거 (재시도 가능하도록)
  setProcessedAddresses(prev => {
    const newSet = new Set(prev);
    newSet.delete(safeSmartAccount.address);
    return newSet;
  });
}
```

### 3. 네트워크 에러 대응

- 자동 재시도 메커니즘
- 사용자 피드백 제공
- 상태 복구 로직

## 개선 가능성

### 1. 🔧 코드 품질 개선

#### A. 타입 안전성 강화

```typescript
// 현재
const error = useCurrentUser().error;
const isNewUser = error && error.message.includes('404');

// 개선안
interface UserQueryError {
  statusCode: number;
  message: string;
  isUserNotFound: boolean;
}

const isNewUser = error?.isUserNotFound ?? false;
```

#### B. 상수 추출

```typescript
// 현재
error.message.includes('404')

// 개선안
const ERROR_MESSAGES = {
  USER_NOT_FOUND: 'User not found (404)',
  REGISTRATION_REQUIRED: 'Registration required'
} as const;
```

#### C. 커스텀 훅 분리

```typescript
// 개선안
const useNewUserDetection = () => {
  const { error } = useCurrentUser();
  return useMemo(() => ({
    isNewUser: error?.message.includes('404') ?? false,
    shouldCreateUser: // ... 복잡한 로직
  }), [error]);
};
```

### 2. 🚀 기능 확장

#### A. 온보딩 단계 커스터마이징

```typescript
interface OnboardingConfig {
  steps: WelcomeFlowStep[];
  skipableSteps: WelcomeFlowStep[];
  requiredSteps: WelcomeFlowStep[];
}

const useOnboardingConfig = (userType: 'regular' | 'premium') => {
  return useMemo(() => ({
    regular: {
      steps: ['welcome', 'choose-username'],
      skipableSteps: ['choose-email', 'fund-account'],
      requiredSteps: ['welcome', 'choose-username']
    },
    premium: {
      steps: ['welcome', 'choose-username', 'choose-email', 'fund-account', 'deposit'],
      skipableSteps: [],
      requiredSteps: ['welcome', 'choose-username', 'choose-email']
    }
  })[userType], [userType]);
};
```

#### B. A/B 테스트 지원

```typescript
const useOnboardingVariant = () => {
  const variant = useMemo(() => {
    // A/B 테스트 로직
    return Math.random() > 0.5 ? 'variant-a' : 'variant-b';
  }, []);
  
  return {
    variant,
    shouldShowExtendedFlow: variant === 'variant-b'
  };
};
```

#### C. 진행률 추적

```typescript
interface OnboardingProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: WelcomeFlowStep[];
  startedAt: Date;
  estimatedTimeRemaining: number;
}

const useOnboardingProgress = () => {
  const [progress, setProgress] = useState<OnboardingProgress>();
  
  const updateProgress = useCallback((step: WelcomeFlowStep) => {
    // 진행률 업데이트 로직
  }, []);
  
  return { progress, updateProgress };
};
```

### 3. 🎯 사용자 경험 개선

#### A. 로딩 상태 개선

```typescript
// 현재
const [isCreatingUser, setIsCreatingUser] = useState(false);

// 개선안
interface OnboardingState {
  status: 'idle' | 'detecting' | 'creating' | 'completed' | 'error';
  progress: number;
  currentAction: string;
}

const useOnboardingState = () => {
  const [state, setState] = useState<OnboardingState>({
    status: 'idle',
    progress: 0,
    currentAction: ''
  });
  
  return { state, setState };
};
```

#### B. 에러 복구 메커니즘

```typescript
const useOnboardingRecovery = () => {
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<Error | null>(null);
  
  const retry = useCallback(async () => {
    if (retryCount < 3) {
      setRetryCount(prev => prev + 1);
      // 재시도 로직
    } else {
      // 수동 복구 옵션 제공
    }
  }, [retryCount]);
  
  return { retry, retryCount, lastError };
};
```

#### C. 오프라인 지원

```typescript
const useOfflineOnboarding = () => {
  const isOnline = useOnlineStatus();
  const [pendingActions, setPendingActions] = useState<OnboardingAction[]>([]);
  
  useEffect(() => {
    if (isOnline && pendingActions.length > 0) {
      // 대기 중인 액션들 실행
      processPendingActions();
    }
  }, [isOnline, pendingActions]);
  
  return { isOnline, pendingActions };
};
```

### 4. 📊 모니터링 및 분석

#### A. 온보딩 분석

```typescript
const useOnboardingAnalytics = () => {
  const trackStep = useCallback((step: WelcomeFlowStep, action: 'start' | 'complete' | 'skip') => {
    // 분석 데이터 전송
    analytics.track('onboarding_step', {
      step,
      action,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    });
  }, []);
  
  return { trackStep };
};
```

#### B. 성능 모니터링

```typescript
const useOnboardingPerformance = () => {
  const startTime = useRef<number>();
  
  const startMeasurement = useCallback(() => {
    startTime.current = performance.now();
  }, []);
  
  const endMeasurement = useCallback((step: string) => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      // 성능 데이터 기록
    }
  }, []);
  
  return { startMeasurement, endMeasurement };
};
```

### 5. 🔒 보안 강화

#### A. 입력 검증 강화

```typescript
const validateReferralCode = (code: string): boolean => {
  // 추천 코드 형식 검증
  return /^[A-Z0-9]{6,12}$/.test(code);
};

const sanitizeUrlParams = (params: URLSearchParams) => {
  // URL 파라미터 살균
  const sanitized = new URLSearchParams();
  
  for (const [key, value] of params) {
    if (ALLOWED_PARAMS.includes(key)) {
      sanitized.set(key, sanitizeString(value));
    }
  }
  
  return sanitized;
};
```

#### B. 레이트 리미팅

```typescript
const useRateLimit = (maxAttempts: number, windowMs: number) => {
  const attempts = useRef<number[]>([]);
  
  const isAllowed = useCallback(() => {
    const now = Date.now();
    attempts.current = attempts.current.filter(time => now - time < windowMs);
    
    if (attempts.current.length >= maxAttempts) {
      return false;
    }
    
    attempts.current.push(now);
    return true;
  }, [maxAttempts, windowMs]);
  
  return { isAllowed };
};
```

### 6. 🧪 테스트 개선

#### A. 단위 테스트 확장

```typescript
describe('WelcomeFlowManager', () => {
  describe('신규 사용자 감지', () => {
    it('404 에러 시 신규 사용자로 판단해야 함', () => {
      // 테스트 로직
    });
    
    it('이미 처리된 주소는 무시해야 함', () => {
      // 테스트 로직
    });
  });
  
  describe('사용자 생성', () => {
    it('referralCode와 함께 사용자를 생성해야 함', () => {
      // 테스트 로직
    });
    
    it('생성 실패 시 재시도가 가능해야 함', () => {
      // 테스트 로직
    });
  });
});
```

#### B. 통합 테스트

```typescript
describe('온보딩 플로우 통합 테스트', () => {
  it('전체 온보딩 플로우가 정상 동작해야 함', async () => {
    // E2E 테스트 로직
  });
});
```

## 결론

`WelcomeFlowManager`는 복잡한 사용자 온보딩 프로세스를 효과적으로 관리하는 핵심 컴포넌트입니다. 현재 구조는 안정적이고 확장 가능하지만, 위에서 제시한 개선사항들을 통해 더욱 견고하고 사용자 친화적인 시스템으로 발전시킬 수 있습니다.

특히 타입 안전성 강화, 에러 처리 개선, 성능 모니터링 추가 등은 우선순위가 높은 개선사항으로 권장됩니다.
