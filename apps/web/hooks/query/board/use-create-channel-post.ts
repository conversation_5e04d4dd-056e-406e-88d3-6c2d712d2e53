import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';
import { CreateChannelPostRequest } from '@/lib/api/board/board.schema.server';

export const useCreateChannelPost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateChannelPostRequest) => boardService.createChannelPost(data),
    onSuccess: (_, data) => {
      if ((data as any).channelId) {
        queryClient.invalidateQueries({
          queryKey: boardKeys.channelPosts((data as any).channelId),
        });
      }
      queryClient.invalidateQueries({
        queryKey: boardKeys.boards,
      });
    },
  });
};
