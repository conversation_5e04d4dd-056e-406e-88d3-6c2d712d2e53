import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';

export const useDeletePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (postId: string) => boardService.deletePost(postId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: boardKeys.boards,
      });
    },
  });
};
