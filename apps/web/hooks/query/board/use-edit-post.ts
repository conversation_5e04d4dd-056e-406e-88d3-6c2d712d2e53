import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';
import { EditChannelPostRequest } from '@/lib/api/board/board.schema.server';

export const useEditPost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ postId, data }: { postId: string; data: EditChannelPostRequest }) =>
      boardService.editPost(postId, data),
    onSuccess: (_, { postId }) => {
      queryClient.invalidateQueries({
        queryKey: boardKeys.post(postId),
      });
      queryClient.invalidateQueries({
        queryKey: boardKeys.boards,
      });
    },
  });
};
