import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';

export const useLikePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (postId: string) => boardService.likePost(postId),
    onSuccess: (_, postId) => {
      queryClient.invalidateQueries({
        queryKey: boardKeys.post(postId),
      });
      queryClient.invalidateQueries({
        queryKey: boardKeys.boards,
      });
    },
  });
};
