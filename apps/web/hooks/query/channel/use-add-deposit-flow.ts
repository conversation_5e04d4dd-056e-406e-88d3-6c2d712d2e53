import useSignAddDeposit from '@/hooks/use-sign-add-deposit';
import { ChannelPredictionDepositRequestBodySchema } from '@/lib/api/channel/channel.schema.server';
import { channelService } from '@/lib/api/channel/channel.service';
import { ApiError } from '@/lib/api/base-api.error';
import { ClientError } from '@/lib/error';
import { toAmount } from '@/lib/format';
import { useGlobalStore } from '@/store/global.store';
import { useMutation } from '@tanstack/react-query';
import { toast } from '@repo/ui/components/sonner';

export const useAddDepositFlow = (marketId: string) => {
  const { signAddDeposit } = useSignAddDeposit();
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  return useMutation({
    mutationFn: async (amount: number) => {
      if (!safeSmartAccountAddress) {
        throw ClientError.fromQuery('Safe smart account address is not found', {
          queryName: 'useAddDepositFlow',
        });
      }

      const signature = await signAddDeposit({
        depositor: safeSmartAccountAddress,
        marketId,
        amount: BigInt(toAmount(amount)),
      });

      const parsed = ChannelPredictionDepositRequestBodySchema.safeParse({
        marketId,
        amount: toAmount(amount),
        signature,
      });

      if (!parsed.success) {
        throw ClientError.fromQuery('Invalid request::' + parsed.error.message, {
          queryName: 'useAddDepositFlow',
        });
      }

      await channelService.addDeposit(parsed.data);
    },
    onError: error => {
      if (ApiError.isApiError(error)) {
        if (error.getDesc() === 'Channel collateral is not enough') {
          toast.error('Channel collateral is not enough');
        }
      }
    },
  });
};
