import { GetChannelMarketsRequestOptions } from '@/lib/api/channel/channel.schema.server';
import { channelService } from '@/lib/api/channel/channel.service';
import { useQuery } from '@tanstack/react-query';
import { mapMarketsResponseToMarketsProps } from '../market/market.mapper';
import { channelKeys } from '../query-keys';

export const useChannelMarkets = (channelId: string, options?: GetChannelMarketsRequestOptions) => {
  return useQuery({
    queryKey: [...channelKeys.channelMarkets(channelId), options],
    enabled: !!channelId,
    queryFn: async () => {
      const markets = await channelService.getChannelMarkets(channelId, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
