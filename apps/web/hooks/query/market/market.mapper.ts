import { marketService } from '@/lib/api/market/market.service';
import { formatUsdc } from '@/lib/format';
import { findByField, getDisputeAmount } from '@/lib/utils';

type GetMarketsReturnType = Awaited<ReturnType<typeof marketService.getMarkets>>;

export const mapMarketsResponseToMarketsProps = (response: GetMarketsReturnType) => {
  const markets = response.markets.map(market => {
    const getNextDeadline = () => {
      const status = market.status;
      switch (status) {
        case 'OPEN':
          return market.predictionDeadline;
        case 'REVIEWING':
          return market.resultConfirmDeadline;
        case 'DISPUTABLE':
          return market.disputeDeadline;
        case 'DISPUTED':
          return market.disputedAt ? market.disputedAt + 5 * 24 * 60 * 60 * 1000 : null; // 5일 후
        default:
          return null;
      }
    };
    const proposedOutcome = market.proposedOutcome;
    const marketProposeOutcomeOdds = proposedOutcome
      ? findByField(market.outcomes, 'outcome', proposedOutcome)?.estimatedOdds
      : null;

    return {
      marketId: market.id,
      marketTitle: market.title,
      marketAvatarImageUrl: market.imageUrl,
      marketNextDeadline: getNextDeadline(),
      marketPredictionDeadline: market.predictionDeadline,
      marketConfirmationDeadline: market.resultConfirmDeadline,
      marketStatus: market.status,
      marketParticipants: market.totalPredictor,
      marketTotalVolume: market.totalVolume,
      marketMaxOutcomeVolume: market.maxOutcomeVolume,
      marketStatusText: market.statusText,
      marketOutcomes: market.outcomes,
      marketProposedOutcome: market.proposedOutcome ?? null,
      marketProposeOutcomeOdds: marketProposeOutcomeOdds ?? null,

      channelId: market.channel.id,
      channelName: market.channel.name,
      channelAvatarImageUrl: market.channel.imageUrl,
    };
  });

  return {
    markets,
    totalLength: response.totalLength,
  };
};

export type MarketsProps = ReturnType<typeof mapMarketsResponseToMarketsProps>;
export type MarketListItem = MarketsProps['markets'][number];

type GetMarketReturnType = Awaited<ReturnType<typeof marketService.getMarketById>>;

export const mapMarketResponseToMarketProps = (market: GetMarketReturnType) => {
  const getNextDeadline = () => {
    const status = market.status;
    switch (status) {
      case 'OPEN':
        return market.predictionDeadline;
      case 'REVIEWING':
        return market.resultConfirmDeadline;
      case 'DISPUTABLE':
        return market.disputeDeadline;
      case 'DISPUTED':
        return market.disputedAt ? market.disputedAt + 5 * 24 * 60 * 60 * 1000 : null; // 5일 후
      default:
        return null;
    }
  };

  const disputeAmountBase = getDisputeAmount(market.outcomes);
  const marketDisputeAmountConfirmed = disputeAmountBase.multipliedBy(10);
  const marketDisputeAmountConfirmedFormatted = formatUsdc(marketDisputeAmountConfirmed.toString());
  const marketDisputedAmount = disputeAmountBase.multipliedBy(market.disputedAmount);
  const marketDisputedAmountFormatted = formatUsdc(marketDisputedAmount.toString());

  return {
    marketId: market.id,
    marketTitle: market.title,
    marketDescription: market.description,
    marketReferenceUrl: market.referenceURL,
    marketBroadcastUrl: market.broadcastURL,
    marketCompetitive: market.competitive,
    marketMinPredictCount: market.minPredictCount,

    marketMaxOutcomeVolume: market.maxOutcomeVolume,
    marketCreatedAt: market.createdAt,
    marketAvatarImageUrl: market.imageUrl,
    marketPredictionDeadline: market.predictionDeadline,
    marketConfirmationDeadline: market.resultConfirmDeadline,

    marketTotalVolume: market.totalVolume,
    marketParticipants: market.totalPredictor,
    marketPredictCount: market.predictCount,
    marketStatus: market.status,
    marketStatusText: market.statusText,
    marketOutcomes: market.outcomes,

    marketOutcomeProposedAt: market.outcomeProposedAt,
    marketProposedOutcome: market.proposedOutcome,
    marketDisputedAt: market.disputedAt,
    marketDisputeAmount: market.disputedAmount,
    marketFinalizedAt: market.finalizedAt,
    marketDisputeAmountBase: disputeAmountBase,
    marketDisputeAmountBaseFormatted: formatUsdc(disputeAmountBase.toString()),

    marketDisputeAmountConfirmed: {
      raw: marketDisputeAmountConfirmed,
      formatted: marketDisputeAmountConfirmedFormatted,
    },

    marketDisputedAmount: {
      raw: marketDisputedAmount,
      formatted: marketDisputedAmountFormatted,
    },

    marketNextDeadline: getNextDeadline(),
    channelId: market.channel.id,
    channelName: market.channel.name,
    channelAvatarImageUrl: market.channel.imageUrl,
  };
};

export type MarketProps = ReturnType<typeof mapMarketResponseToMarketProps>;

// export const getMarketTopOutcomeVolume = (outcomes: MarketOutcome[]) => {
//   let maxOutcomeIndex = 0;
//   for (let i = 0; i < outcomes.length; i++) {
//     const outcome = outcomes[i]!;
//     if (outcome.rawVolume > outcomes[maxOutcomeIndex]!.rawVolume) {
//       maxOutcomeIndex = i;
//     }
//   }
//   return outcomes[maxOutcomeIndex]!;
// };
