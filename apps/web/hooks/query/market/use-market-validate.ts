import { useMutation } from '@tanstack/react-query';
import { marketService } from '@/lib/api/market/market.service';
import { ValidateRequestBody } from '@/lib/api/market/market.schema.server';

export const useMarketValidate = () => {
  return useMutation({
    mutationFn: (data: ValidateRequestBody) => marketService.validateMarket(data),
    onError: error => {
      console.error(error);
    },
  });
};
