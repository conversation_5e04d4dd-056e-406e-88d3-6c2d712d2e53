import type { GetMarketsByCategoryRequestOptions } from '@/lib/api/market/market.schema.server';
import { marketService } from '@/lib/api/market/market.service';
import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { mapMarketsResponseToMarketsProps } from './market.mapper';

export const useMarketsByCategory = (
  category: string,
  options?: GetMarketsByCategoryRequestOptions
) => {
  return useQuery({
    enabled: !!category,
    queryKey: [...marketKeys.markets, category, options],
    queryFn: async () => {
      const markets = await marketService.getMarketsByCategory(category, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
