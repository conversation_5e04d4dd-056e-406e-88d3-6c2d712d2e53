import { PortfolioPositionsQueryOptions } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { useQuery } from '@tanstack/react-query';
import { portfolioKeys } from '../query-keys';
import { mapPortfolioPositionsResponseToPositionsProps } from './portfolio.mapper';

export type { PortfolioPositionItem } from './portfolio.mapper';

export const usePortfolioPositions = (options?: PortfolioPositionsQueryOptions) => {
  return useQuery({
    queryKey: portfolioKeys.positions(options),
    queryFn: async () => {
      const response = await portfolioService.getPositions(options);
      return mapPortfolioPositionsResponseToPositionsProps(response);
    },
  });
};
