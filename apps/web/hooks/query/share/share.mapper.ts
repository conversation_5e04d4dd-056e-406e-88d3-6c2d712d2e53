import { shareService } from '@/lib/api/share/share.service';
import { toRelativeTime } from '@/lib/utils';

type GetMySharesReturnType = Awaited<ReturnType<typeof shareService.getMyShares>>;

export const mapMySharesResponseToShareRewardsProps = (response: GetMySharesReturnType) => {
  const shares = response.shares.map(share => {
    return {
      id: share.market.id,
      prediction: {
        id: share.market.id,
        title: share.market.title,
        avatar: share.market.imageUrl,
      },
      winnings: share.rawWinnings.toNumber(),
      shareBonus: share.rawShareableAmount.toNumber(),
      winningsFormatted: share.winningsFormatted,
      shareBonusFormatted: share.shareableAmountFormatted,
      status: share.isShared ? 'Completed' : 'Share',
      sharedAt: share.sharedAt,
      localSharedAt: share.localSharedAt,
      relativeSharedTime: share.sharedAt ? toRelativeTime(share.sharedAt) : null,
    };
  });

  return {
    shares,
    totalLength: response.totalLength,
  };
};

export type ShareRewardsProps = ReturnType<typeof mapMySharesResponseToShareRewardsProps>;
export type ShareRewardItem = ShareRewardsProps['shares'][number];
