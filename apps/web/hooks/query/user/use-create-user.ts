import { useMutation, useQueryClient } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';
import { CreateUserRequestBody } from '@/lib/api/user/user.schema.server';

export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserRequestBody) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: userKeys.currentUser(),
      });
    },
  });
};
