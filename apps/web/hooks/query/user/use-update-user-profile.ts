import { userService } from '@/lib/api/user/user.service';
import { useMutation } from '@tanstack/react-query';

export const useUpdateUserProfile = (options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  return useMutation({
    mutationFn: (formData: FormData) => userService.updateUserProfile(formData),
    onSuccess: () => {
      options?.onSuccess?.();
    },
    onError: error => {
      options?.onError?.(error);
    },
  });
};
