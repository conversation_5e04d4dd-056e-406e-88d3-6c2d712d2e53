import { useQuery } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';
import { ApiError } from '@/lib/api/base-api.error';
import { Address } from 'viem';

export const useUserWithAutoCreate = (proxyAddress: Address | undefined, isSignedIn: boolean) => {
  return useQuery({
    retry: false,
    queryKey: [...userKeys.user(proxyAddress), 'auto-create'] as const,
    queryFn: async ({ queryKey }) => {
      const [, proxyAddress] = queryKey;

      try {
        // 먼저 사용자 정보 조회 시도
        const user = await userService.getUserInfo(proxyAddress!);
        return user;
      } catch (error: unknown) {
        // 404 에러인 경우 사용자 생성 후 다시 조회
        if (error instanceof ApiError && error.statusCode === 404) {
          // 404 에러를 다시 던져서 WelcomeFlowManager에서 감지할 수 있도록 함
          const userNotFoundError = new Error('User not found (404) - Registration required');
          userNotFoundError.cause = error;
          throw userNotFoundError;
        }

        throw error;
      }
    },
    enabled: isSignedIn && !!proxyAddress,
  });
};

/**
 * 사용자 생성 후 정보를 다시 조회하는 함수
 */
export const createUserAndRefetch = async (proxyAddress: Address) => {
  // referralCode 가져오기 (URL params 또는 localStorage에서)
  const referralCode = (() => {
    if (typeof window !== 'undefined') {
      const urlReferralCode = new URLSearchParams(window.location.search).get('referralCode');
      const storageReferralCode = localStorage.getItem('referralCode');
      const finalReferralCode = urlReferralCode || storageReferralCode || '';

      return finalReferralCode;
    }
    return '';
  })();

  try {
    // 사용자 생성
    await userService.createUser({ referralCode });

    // 생성 후 다시 사용자 정보 조회
    const newUser = await userService.getUserInfo(proxyAddress);

    return newUser;
  } catch (createError: unknown) {
    throw createError;
  }
};
