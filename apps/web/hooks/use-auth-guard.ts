import { useCallback } from 'react';
import { login } from '@/lib/auth';
import { useGlobalStore } from '@/store/global.store';

export function useAuthGuard() {
  const isSignedIn = useGlobalStore(v => v.isSignedIn);

  const withAuthCheck = useCallback(
    <T extends any[]>(fn: (...args: T) => void) =>
      (...args: T) => {
        if (!isSignedIn) {
          login();
          return;
        }
        fn(...args);
      },
    [isSignedIn]
  );

  return { withAuthCheck };
}
