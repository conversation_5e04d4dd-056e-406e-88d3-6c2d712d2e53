import { useState } from 'react';

interface UseFileDragAndDropOptions {
  /** 비활성화 여부 */
  disabled?: boolean;
  /** 파일 드롭 시 호출되는 콜백 */
  onFileDrop: (file: File) => void;
}

/**
 * 드래그 앤 드롭으로 파일을 업로드할 때 필요한 상태와 이벤트 핸들러를 제공합니다.
 *
 * @example
 * const { isDragging, handleDragOver, handleDragLeave, handleDrop } = useFileDragAndDrop({
 *   disabled,
 *   onFileDrop: file => {
 *     // 파일 처리 로직
 *   },
 * });
 *
 * <div onDragOver={handleDragOver} onDragLeave={handleDragLeave} onDrop={handleDrop} />
 */
export function useFileDragAndDrop({ disabled = false, onFileDrop }: UseFileDragAndDropOptions) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent<HTMLElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (disabled) return;

    const file = e.dataTransfer.files?.[0];
    if (file) {
      onFileDrop(file);
    }
  };

  return {
    isDragging,
    handleDragOver,
    handleDragLeave,
    handleDrop,
  };
}
