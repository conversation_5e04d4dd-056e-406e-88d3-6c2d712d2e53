import { useState, useEffect, useRef } from 'react';

export interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

interface UseCountdownOptions {
  onComplete?: () => void;
}
const MILLIS_IN_SECOND = 1000;
const MILLIS_IN_MINUTE = 60 * MILLIS_IN_SECOND;
const MILLIS_IN_HOUR = 60 * MILLIS_IN_MINUTE;
const MILLIS_IN_DAY = 24 * MILLIS_IN_HOUR;

/**
 * A custom hook that provides countdown functionality using requestAnimationFrame
 * @param endTime The end time for the countdown (timestamp in milliseconds)
 * @param options Additional options for the countdown
 * @returns Object containing remaining time and completion status
 */
export function useCountdown(endTime: number, options: UseCountdownOptions = {}) {
  const [remainingTime, setRemainingTime] = useState<TimeRemaining | null>(null);
  const requestRef = useRef<number | null>(null);
  const { onComplete } = options;

  const calculateTimeRemaining = (): TimeRemaining | null => {
    const now = Date.now();
    const difference = endTime - now;

    if (difference <= 0) {
      if (onComplete) {
        onComplete();
      }
      return null;
    }

    const days = Math.floor(difference / MILLIS_IN_DAY);
    const hours = Math.floor((difference % MILLIS_IN_DAY) / MILLIS_IN_HOUR);
    const minutes = Math.floor((difference % MILLIS_IN_HOUR) / MILLIS_IN_MINUTE);
    const seconds = Math.floor((difference % MILLIS_IN_MINUTE) / MILLIS_IN_SECOND);

    return { days, hours, minutes, seconds };
  };

  const animate = () => {
    setRemainingTime(calculateTimeRemaining());
    requestRef.current = requestAnimationFrame(animate);
  };

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [endTime]); // Re-run effect if endTime changes

  return {
    remainingTime,
    isComplete: !remainingTime,
  };
}

export default useCountdown;
