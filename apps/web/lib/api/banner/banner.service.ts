import { env } from '@/lib/env';
import { BaseFetcher } from '@repo/shared/lib/base-fetcher';
import { BannerResponseSchema, type BannerResponse } from '@repo/shared/schemas/banner.schema';

export class BannerService extends BaseFetcher {
  constructor() {
    super();
  }

  async getBannerData(): Promise<BannerResponse> {
    try {
      const data = await this.kyInstance
        .get(`${env.NEXT_PUBLIC_STATIC_ASSETS_URL}/config/banners.json`)
        .json();
      const validatedData = BannerResponseSchema.safeParse(data);

      if (!validatedData.success) {
        throw new Error(`BannerService.getBannerData failed: ${validatedData.error.message}`);
      }
      return validatedData.data;
    } catch (e) {
      console.error('Failed to fetch banner data', e);
      throw e;
    }
  }
}

export const bannerService = new BannerService();
