import { z } from 'zod';
import { extractBodyFromResponse, formatZodError } from './utils';
import { HTTPError } from '@repo/shared/lib/base-fetcher';

enum ApiErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
}

export class ApiError extends Error {
  constructor(
    message: string,
    public code: ApiErrorCode,
    public statusCode?: number,
    public originalError?: unknown,
    public endpoint?: string,
    public data?: unknown
  ) {
    super(message, { cause: originalError });

    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.originalError = originalError;
    this.endpoint = endpoint;
    this.data = data;
  }

  getMessage() {
    return this.message;
  }

  getDesc() {
    return (this.data as { desc?: string })?.desc;
  }

  getErrorMessage() {}

  /**
   * 사용자에게 표시할 메시지를 반환합니다.
   * 잔고 부족인 경우 "Insufficient balance"만 반환합니다.
   */
  getDisplayMessage() {
    if (this.isInsufficientBalance()) {
      return 'Insufficient balance';
    }

    // 'API_ERROR::' 접두사 제거
    let message = this.message;
    if (message.startsWith('API_ERROR::')) {
      message = message.replace('API_ERROR::', '');
    }
    if (message.startsWith('VALIDATION_ERROR::')) {
      message = message.replace('VALIDATION_ERROR::', '');
    }

    return message;
  }

  /**
   * 잔고 부족 에러인지 확인합니다.
   */
  isInsufficientBalance(): boolean {
    if (this.code === ApiErrorCode.INSUFFICIENT_BALANCE) {
      return true;
    }

    // 메시지에서 잔고 부족 키워드 검사
    const message = this.message.toLowerCase();
    const insufficientKeywords = [
      'insufficient balance',
      'insufficient funds',
      'not enough balance',
      'balance too low',
      'insufficient usdc',
    ];

    return insufficientKeywords.some(keyword => message.includes(keyword));
  }

  toJson() {
    return {
      message: this.message,
      statusCode: this.statusCode,
      originalError: this.originalError,
      endpoint: this.endpoint,
      code: this.code,
      data: this.data,
    };
  }

  static fromValidationError(error: z.ZodError, endpoint: string, data: unknown) {
    const details = formatZodError(error);
    console.error('fromValidationError::', details);
    return new ApiError(
      `VALIDATION_ERROR::${details}`,
      ApiErrorCode.VALIDATION_ERROR,
      400,
      error,
      endpoint,
      data
    );
  }

  static fromErrorResponse({
    errorJson,
    statusCode,
    endpoint,
  }: {
    errorJson: unknown;
    statusCode: number;
    endpoint: string;
  }) {
    const message = (errorJson as { desc: string })?.desc || 'Unknown error';

    // 잔고 부족 에러 감지
    const isInsufficientBalance =
      message.toLowerCase().includes('insufficient') ||
      message.toLowerCase().includes('balance') ||
      message.toLowerCase().includes('funds');

    const code = isInsufficientBalance ? ApiErrorCode.INSUFFICIENT_BALANCE : ApiErrorCode.API_ERROR;

    return new ApiError(`API_ERROR::${message}`, code, statusCode, errorJson, endpoint, errorJson);
  }

  static async fromHTTPError(error: HTTPError<unknown>, endpoint: string) {
    const body = await extractBodyFromResponse(error.response);
    return this.fromErrorResponse({
      errorJson: body,
      statusCode: error.response.status,
      endpoint,
    });
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }
}
