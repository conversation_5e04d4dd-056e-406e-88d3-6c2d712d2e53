import { BaseFetcher, HTTPError, type Options } from '@repo/shared/lib/base-fetcher';
import { env } from '../env';
import { BaseResponseShapeSchema } from './base.schema';
import { ApiError } from './base-api.error';

// eslint-disable-next-line turbo/no-undeclared-env-vars
const IS_LOCAL_DEV = process.env.NODE_ENV === 'development';

export class ApiClient extends BaseFetcher {
  constructor(path: string = '') {
    const prefixUrl = env.NEXT_PUBLIC_API_URL + path;
    super({
      prefixUrl,
      timeout: 30000,
      credentials: 'include',
      hooks: {
        afterResponse: [
          async (request, _, response) => {
            if (IS_LOCAL_DEV) {
              let requestBody = null;
              if (request.body) {
                const clonedRequest = request.clone();
                try {
                  // FormData인지 확인
                  if (clonedRequest.headers.get('content-type')?.includes('multipart/form-data')) {
                    requestBody = '[FormData]';
                  } else {
                    requestBody = await clonedRequest.json();
                  }
                } catch {
                  // JSON 파싱 실패 시 텍스트로 시도
                  try {
                    requestBody = await request.clone().text();
                  } catch {
                    requestBody = '[Unable to parse body]';
                  }
                }
              }

              const debugInfo = {
                request: {
                  method: request.method,
                  url: request.url,
                  headers: Object.fromEntries(request.headers.entries()),
                  body: requestBody,
                },
                response: {
                  status: response.status,
                  headers: Object.fromEntries(response.headers.entries()),
                  url: response.url,
                  body: await response.clone().json(),
                },
              };
              console.info('🔍 API::' + new URL(request.url).pathname + '::', debugInfo);
            }
            return response;
          },
        ],
        beforeError: [
          error => {
            console.log('beforeError::', error);
            return error;
          },
        ],
      },
    });
  }

  private async processResponse(data: unknown): Promise<unknown> {
    const { result } = BaseResponseShapeSchema.parse(data);
    return result;
  }

  private async executeRequest(requestFn: () => Promise<unknown>, url: string): Promise<unknown> {
    try {
      const data = await requestFn();
      return this.processResponse(data);
    } catch (error) {
      if (error instanceof HTTPError) {
        const errorJson = await error.response.json().catch(() => null);
        throw ApiError.fromErrorResponse({
          errorJson,
          statusCode: error.response.status,
          endpoint: url,
        });
      }
      throw error;
    }
  }

  async get(url: string = '', options: Options = {}) {
    return this.executeRequest(() => this.kyInstance.get(url, options).json(), url);
  }

  async post(url: string, options: Options = {}) {
    return this.executeRequest(() => this.kyInstance.post(url, options).json(), url);
  }

  async put(url: string, options: Options = {}) {
    return this.executeRequest(() => this.kyInstance.put(url, options).json(), url);
  }

  async delete(url: string, options: Options = {}) {
    return this.executeRequest(() => this.kyInstance.delete(url, options).json(), url);
  }

  async patch(url: string, options: Options = {}) {
    return this.executeRequest(() => this.kyInstance.patch(url, options).json(), url);
  }
}
