import { ApiClient } from '@/lib/api/base-api';
import { ApiError } from '../base-api.error';
import {
  CommentResponseSchema,
  CommentsResponseSchema,
  CreateChannelPostRequest,
  CreateCommentRequest,
  EditChannelPostRequest,
  LikeResponseSchema,
  PostResponseSchema,
  PostsResponseSchema,
  ReportPostRequest,
  SuccessResponseSchema,
} from './board.schema.server';
import { transformCommentsResponse } from './board.transform';

export class BoardService {
  static BASE_PATH = '/data-api/v1/board';
  static ROUTES = {
    GET: {
      '/comment/{parentId}': (parentId: string) => `comment/${parentId}`,
      '/post/{postId}': (postId: string) => `post/${postId}`,
      '/post/channel/{channelId}': (channelId: string) => `post/channel/${channelId}`,
    },
    POST: {
      '/comment/{parentId}': (parentId: string) => `comment/${parentId}`,
      '/like/{postId}': (postId: string) => `like/${postId}`,
      '/post/channel': 'post/channel',
      '/post/pin/{postId}': (postId: string) => `post/pin/${postId}`,
      '/post/unpin/{postId}': (postId: string) => `post/unpin/${postId}`,
      '/report': 'report',
    },
    PATCH: {
      '/post/{postId}': (postId: string) => `post/${postId}`,
    },
    DELETE: {
      '/post/{postId}': (postId: string) => `post/${postId}`,
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(BoardService.BASE_PATH);
  }

  async getComments(
    parentId: string,
    options?: {
      page?: number;
      limit?: number;
      order?: 'latest' | 'likes';
      withPositions?: 'true' | 'false';
      onlyPredictors?: 'true' | 'false';
    }
  ) {
    const searchParams = new URLSearchParams();

    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.order !== undefined) searchParams.set('order', options.order);
    if (options?.withPositions !== undefined)
      searchParams.set('withPositions', options.withPositions);
    if (options?.onlyPredictors !== undefined)
      searchParams.set('onlyPredictors', options.onlyPredictors);

    const response = await this.api.get(BoardService.ROUTES.GET['/comment/{parentId}'](parentId), {
      searchParams,
    });

    const result = CommentsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.GET['/comment/{parentId}'](parentId),
        response
      );
    }

    return transformCommentsResponse(result.data);
  }

  /**
   * 댓글 생성
   */
  async createComment(parentId: string, data: CreateCommentRequest) {
    const response = await this.api.post(
      BoardService.ROUTES.POST['/comment/{parentId}'](parentId),
      { json: data }
    );

    const result = CommentResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/comment/{parentId}'](parentId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 좋아요/좋아요 취소
   */
  async likePost(postId: string) {
    const response = await this.api.post(BoardService.ROUTES.POST['/like/{postId}'](postId));

    const result = LikeResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/like/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 조회
   */
  async getPost(postId: string) {
    const response = await this.api.get(BoardService.ROUTES.GET['/post/{postId}'](postId));

    const result = PostResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.GET['/post/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 수정
   */
  async editPost(postId: string, data: EditChannelPostRequest) {
    const response = await this.api.patch(BoardService.ROUTES.PATCH['/post/{postId}'](postId), {
      json: data,
    });

    const result = PostResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.PATCH['/post/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 삭제
   */
  async deletePost(postId: string) {
    const response = await this.api.delete(BoardService.ROUTES.DELETE['/post/{postId}'](postId));

    const result = SuccessResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.DELETE['/post/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 포스트 생성
   */
  async createChannelPost(data: CreateChannelPostRequest) {
    const response = await this.api.post(BoardService.ROUTES.POST['/post/channel'], {
      json: data,
    });

    const result = PostResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/post/channel'],
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 포스트 목록 조회
   */
  async getChannelPosts(channelId: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(
      BoardService.ROUTES.GET['/post/channel/{channelId}'](channelId),
      { searchParams }
    );

    const result = PostsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.GET['/post/channel/{channelId}'](channelId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 핀
   */
  async pinPost(postId: string) {
    const response = await this.api.post(BoardService.ROUTES.POST['/post/pin/{postId}'](postId));

    const result = PostResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/post/pin/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 언핀
   */
  async unpinPost(postId: string) {
    const response = await this.api.post(BoardService.ROUTES.POST['/post/unpin/{postId}'](postId));

    const result = PostResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/post/unpin/{postId}'](postId),
        response
      );
    }
    return result.data;
  }

  /**
   * 포스트 신고
   */
  async reportPost(data: ReportPostRequest) {
    const response = await this.api.post(BoardService.ROUTES.POST['/report'], {
      json: data,
    });

    const result = SuccessResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        BoardService.ROUTES.POST['/report'],
        response
      );
    }
    return result.data;
  }
  /**
   * 포스트 좋아요 토글 편의 메서드
   */
  async togglePostLike(postId: string) {
    try {
      return await this.likePost(postId);
    } catch (error) {
      console.error('Toggle post like error:', error);
      throw error;
    }
  }

  /**
   * 포스트 핀 토글 편의 메서드
   */
  async togglePostPin(postId: string, isPinned: boolean) {
    try {
      if (isPinned) {
        return await this.unpinPost(postId);
      } else {
        return await this.pinPost(postId);
      }
    } catch (error) {
      console.error('Toggle post pin error:', error);
      throw error;
    }
  }
}

export const boardService = new BoardService();
