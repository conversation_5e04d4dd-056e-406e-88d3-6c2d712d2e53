import { z } from 'zod';

export const zodTimestampDateSchema = z.string().transform(val => new Date(val));

export enum BroadcastType {
  JACKPOT = 'JACKPOT',
  SHARE = 'SHARE',
}

export const broadcastSchema = z.object({
  type: z.nativeEnum(BroadcastType).describe('Broadcast type'),
  nickname: z.string().describe('user nickname'),
  amount: z.coerce.bigint().describe('amount'),
  market: z.object({
    id: z.string().describe('Market ID'),
    title: z.string().describe('Market title'),
    imageUrl: z.string().nullish().describe('Market image URL'),
  }),
  timestamp: zodTimestampDateSchema.describe('timestamp'),
});

export const BroadcastsResponseSchema = z.object({
  broadcasts: z.array(broadcastSchema),
  totalLength: z.number(),
});

export type Broadcast = z.infer<typeof broadcastSchema>;
export type BroadcastsResponse = z.infer<typeof BroadcastsResponseSchema>;
