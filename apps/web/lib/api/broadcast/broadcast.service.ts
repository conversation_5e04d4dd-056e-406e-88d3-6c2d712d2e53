import { ApiClient } from '@/lib/api/base-api';
import { BroadcastsResponseSchema, BroadcastsResponse } from './broadcast.schema';
import { transformBroadcastsResponse } from './broadcast.transform';

export class BroadcastService {
  static BASE_PATH = '/data-api/v1';
  static ROUTES = {
    GET: {
      '/broadcast': 'broadcast',
    },
  };

  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(BroadcastService.BASE_PATH);
  }

  private async getRealBroadcasts(): Promise<BroadcastsResponse> {
    try {
      const response = await this.api.get(BroadcastService.ROUTES.GET['/broadcast']);
      const result = BroadcastsResponseSchema.safeParse(response);
      if (result.success) {
        return result.data;
      }
      return { broadcasts: [], totalLength: 0 };
    } catch (error) {
      throw error;
    }
  }

  private async getMockBroadcasts(): Promise<BroadcastsResponse> {
    try {
      const response = await fetch('/mock/activity-notifications.json');
      const data = await response.json();
      const result = BroadcastsResponseSchema.safeParse(data);
      if (result.success) {
        return result.data;
      }
      return { broadcasts: [], totalLength: 0 };
    } catch (error) {
      return { broadcasts: [], totalLength: 0 };
    }
  }

  async getBroadcasts() {
    const [realData, mockData] = await Promise.all([
      this.getRealBroadcasts(),
      this.getMockBroadcasts(),
    ]);

    const allBroadcasts = [...(realData?.broadcasts || []), ...(mockData?.broadcasts || [])];

    const combinedResponse: BroadcastsResponse = {
      broadcasts: allBroadcasts,
      totalLength: (realData?.totalLength || 0) + (mockData?.totalLength || 0),
    };

    return transformBroadcastsResponse(combinedResponse);
  }
}

export const broadcastService = new BroadcastService();
