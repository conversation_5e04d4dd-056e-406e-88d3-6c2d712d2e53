import { formatVolume } from '@repo/shared/utils/number-format';
import {
  Broadcast,
  BroadcastsResponse,
  BroadcastType as BroadcastTypeEnum,
} from './broadcast.schema';

export type TransformedBroadcast = Broadcast & {
  message: string;
};

export const transformBroadcast = (broadcast: Broadcast): TransformedBroadcast => {
  let message = '';
  if (broadcast.type === BroadcastTypeEnum.JACKPOT) {
    message = `"**${broadcast.nickname}**" just won **${formatVolume(
      broadcast.amount
    )}** on '${broadcast.market.title}' in the market!`;
  } else if (broadcast.type === BroadcastTypeEnum.SHARE) {
    message = `**${formatVolume(broadcast.amount)}** ROI earned through **${
      broadcast.nickname
    }'s** insight on **${broadcast.market.title}**`;
  }
  return {
    ...broadcast,
    message,
  };
};

export const transformBroadcastsResponse = (response: BroadcastsResponse) => {
  return {
    ...response,
    broadcasts: response.broadcasts.map(transformBroadcast),
  };
};
