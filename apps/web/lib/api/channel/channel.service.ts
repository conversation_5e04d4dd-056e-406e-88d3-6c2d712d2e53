import { ApiClient } from '@/lib/api/base-api';
import {
  ChannelActivePredictionsResponseSchema,
  ChannelBriefInfosResponseSchema,
  ChannelCollateralHistoryResponseSchema,
  ChannelCollateralResponseSchema,
  ChannelHistoryPredictionsResponseSchema,
  ChannelInfoResponseSchema,
  ChannelPredictionDepositRequestBody,
  ChannelRewardsHistoryResponseSchema,
  ChannelRewardsResponseSchema,
  ChannelSubscribeRequest,
  ChannelUnsubscribeRequest,
  GetChannelMarketsRequestOptions,
  GetInChannelLeaderboardResponseSchema,
  LeaderboardType,
  MarketOrder,
  MarketStatus,
  PopularChannelResponseSchema,
  RewardsOrder,
  SuccessResponseSchema,
  UpdateChannelRequestBody,
} from './channel.schema.server';
import {
  transformChannelInfo,
  transformChannelCollateral,
  transformChannelLeaderboard,
  transformActivePredictionsResponse,
  transformHistoryPredictionsResponse,
  transformChannelRewards,
  transformRewardsHistoryResponse,
  transformCollateralHistoryResponse,
  transformPopularChannelsResponse,
} from './channel.transform';
import { userService } from '../user/user.service';
import { ApiError } from '../base-api.error';
import {
  GetMarketsRequestOptions,
  GetMarketsResponseSchema,
  MarketStatusEnum,
} from '../market/market.schema.server';
import { transformGetMarketsResponse } from '../market/market.transform';

export class ChannelService {
  static BASE_PATH = '/data-api/v1/channel/';
  static ROUTES = {
    GET: {
      '/{channelId}': (channelId: string) => `${channelId}`,
      '/{channelId}/leaderboard/{type}': (channelId: string, type: string) =>
        `${channelId}/leaderboard/${type}`,
      '/{channelId}/markets': (channelId: string) => `${channelId}/markets`,
      '/collateral': 'collateral',
      '/collateral/history': 'collateral/history',
      '/popular': 'popular',
      '/predictions/active': 'predictions/active',
      '/predictions/history': 'predictions/history',
      '/rewards': 'rewards',
      '/rewards/history': 'rewards/history',
      '/search': 'search',
    },
    POST: {
      '/market/deposit': 'market/deposit',
      '/subscribe': 'subscribe',
      '/unsubscribe': 'unsubscribe',
    },
    PATCH: {
      '/': '',
    },
  };

  private api: ApiClient;
  constructor() {
    this.api = new ApiClient(ChannelService.BASE_PATH);
  }

  /**
   * 채널 정보 업데이트
   */
  async updateChannel(data: UpdateChannelRequestBody) {
    const formData = new FormData();

    // FormData에 필드 추가
    if (data.name !== undefined) {
      formData.append('name', data.name);
    }

    if (data.description !== undefined) {
      formData.append('description', data.description);
    }

    if (data.image !== undefined) {
      formData.append('image', data.image);
    }

    if (data.banner !== undefined) {
      formData.append('banner', data.banner);
    }

    if (data.channelSns !== undefined) {
      formData.append('channelSns', JSON.stringify(data.channelSns));
    }

    const response = await this.api.patch(ChannelService.ROUTES.PATCH['/'], {
      body: formData,
    });

    const result = SuccessResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, ChannelService.ROUTES.PATCH['/'], response);
    }
    return result.data;
  }

  /**
   * 채널 ID로 채널 정보 조회
   */
  async getChannelById(channelId: string) {
    const response = await this.api.get(ChannelService.ROUTES.GET['/{channelId}'](channelId));

    const result = ChannelInfoResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/{channelId}'](channelId),
        response
      );
    }

    return transformChannelInfo(result.data);
  }

  /**
   * 채널 ID로 채널 정보와 사용자 정보 함께 조회
   */
  async getChannelByIdWithUser(channelId: string) {
    try {
      const [channelInfo, userInfo] = await Promise.all([
        this.getChannelById(channelId),
        userService.getUserInfo(channelId),
      ]);

      return {
        channel: channelInfo,
        user: userInfo,
      };
    } catch (error) {
      console.error('Get channel with user error:', error);
      throw error;
    }
  }

  /**
   * 채널 내 리더보드 조회
   */
  async getChannelLeaderboard(channelId: string, type: LeaderboardType) {
    const response = await this.api.get(
      ChannelService.ROUTES.GET['/{channelId}/leaderboard/{type}'](channelId, type)
    );

    const result = GetInChannelLeaderboardResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/{channelId}/leaderboard/{type}'](channelId, type),
        response
      );
    }
    return transformChannelLeaderboard(result.data);
  }

  /**
   * 채널 내 마켓 목록 조회
   */
  async getChannelMarkets(channelId: string, options?: GetChannelMarketsRequestOptions) {
    const searchParams = new URLSearchParams();
    if (options?.status !== undefined && options.status !== 'ALL')
      searchParams.set('status', options.status);

    if (options?.order !== undefined) searchParams.set('order', options.order);
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(
      ChannelService.ROUTES.GET['/{channelId}/markets'](channelId),
      {
        searchParams,
      }
    );

    const result = GetMarketsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/{channelId}/markets'](channelId),
        response
      );
    }

    const excludedStatuses = new Set<MarketStatusEnum>([
      'CANCELLED_WITH_UNMET',
      'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
    ]);

    const filteredMarkets = result.data.markets.filter(market => {
      return !excludedStatuses.has(market.status);
    });

    return transformGetMarketsResponse({
      totalLength: result.data.totalLength,
      markets: filteredMarkets,
    });
  }

  /**
   * 채널 담보 통계 조회
   */
  async getCollateral() {
    const response = await this.api.get(ChannelService.ROUTES.GET['/collateral']);
    const result = ChannelCollateralResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/collateral'],
        response
      );
    }

    return transformChannelCollateral(result.data);
  }

  /**
   * 채널 담보 이력 조회
   */
  async getCollateralHistory(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(ChannelService.ROUTES.GET['/collateral/history'], {
      searchParams,
    });

    const result = ChannelCollateralHistoryResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/collateral/history'],
        response
      );
    }
    return transformCollateralHistoryResponse(result.data);
  }

  async addDeposit(data: ChannelPredictionDepositRequestBody) {
    const response = await this.api.post(ChannelService.ROUTES.POST['/market/deposit'], {
      json: data,
    });

    const result = SuccessResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.POST['/market/deposit'],
        response
      );
    }
    return result.data;
  }

  /**
   * 인기 채널 목록 조회
   */
  async getPopularChannels() {
    const response = await this.api.get(ChannelService.ROUTES.GET['/popular']);

    const result = PopularChannelResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/popular'],
        response
      );
    }
    return transformPopularChannelsResponse(result.data);
  }

  async getActivePredictions(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(ChannelService.ROUTES.GET['/predictions/active'], {
      searchParams,
    });

    const result = ChannelActivePredictionsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/predictions/active'],
        response
      );
    }
    return transformActivePredictionsResponse(result.data);
  }

  async getPredictionHistory(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(ChannelService.ROUTES.GET['/predictions/history'], {
      searchParams,
    });

    const result = ChannelHistoryPredictionsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/predictions/history'],
        response
      );
    }
    return transformHistoryPredictionsResponse(result.data);
  }

  /**
   * 리워드 조회
   */
  async getRewards() {
    const response = await this.api.get(ChannelService.ROUTES.GET['/rewards']);

    const result = ChannelRewardsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/rewards'],
        response
      );
    }

    return transformChannelRewards(result.data);
  }

  /**
   * 리워드 이력 조회
   */
  async getRewardsHistory(options?: { page?: number; limit?: number; order?: RewardsOrder }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.order !== undefined) searchParams.set('order', options.order);

    const response = await this.api.get(ChannelService.ROUTES.GET['/rewards/history'], {
      searchParams,
    });

    const result = ChannelRewardsHistoryResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/rewards/history'],
        response
      );
    }

    return transformRewardsHistoryResponse(result.data);
  }

  /**
   * 채널 검색
   */
  async searchChannels(query: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    searchParams.set('query', query);
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(ChannelService.ROUTES.GET['/search'], { searchParams });

    const result = ChannelBriefInfosResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.GET['/search'],
        response
      );
    }

    return result.data;
  }

  /**
   * 채널 구독
   */
  async subscribeChannel(data: ChannelSubscribeRequest) {
    const response = await this.api.post(ChannelService.ROUTES.POST['/subscribe'], {
      json: data,
    });

    const result = SuccessResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.POST['/subscribe'],
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 구독 해제
   */
  async unsubscribeChannel(data: ChannelUnsubscribeRequest) {
    const response = await this.api.post(ChannelService.ROUTES.POST['/unsubscribe'], {
      json: data,
    });

    const result = SuccessResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ChannelService.ROUTES.POST['/unsubscribe'],
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 구독/구독해제를 토글하는 편의 메서드
   */
  async toggleChannelSubscription(channelId: string, isCurrentlySubscribed: boolean) {
    try {
      if (isCurrentlySubscribed) {
        return await this.unsubscribeChannel({ channelId });
      } else {
        return await this.subscribeChannel({ channelId });
      }
    } catch (error) {
      console.error('Toggle channel subscription error:', error);
      throw error;
    }
  }
}

export const channelService = new ChannelService();
