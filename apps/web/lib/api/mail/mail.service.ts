import { ApiClient } from '../base-api';
import { apiCall } from '../utils';
import { MailConfirmResponseSchema, MailRequestResponseSchema } from './mail.schema.server';

const mailApi = new ApiClient('/mail/v1');

export const getEmailConfirmVerification = (verificationId: string) => {
  const path = `confirm/${verificationId}`;
  return apiCall(mailApi.get(path), MailConfirmResponseSchema, path);
};

export const requestEmailVerification = (email: string) => {
  const path = 'request';
  return apiCall(mailApi.post(path, { json: { email } }), MailRequestResponseSchema, path);
};
