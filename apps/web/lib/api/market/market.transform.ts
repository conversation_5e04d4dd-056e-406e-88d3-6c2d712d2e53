import { DEFAULT_MARKET_AVATAR_URL, MARKET_STATUS_TEXT_MAP } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import { createAmountField, getMiliseconds, toRelativeTime } from '@/lib/utils';
import BigNumber from 'bignumber.js';
import type {
  GetMarketsResponseType,
  MarketActivitiesResponseSchema,
  MarketActivitySchema,
  MarketServerSchema,
  MarketTopPredictorsResponseSchema,
} from './market.schema.server';

export const transformMarket = (data: MarketServerSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    channel: {
      ...data.channel,
      imageUrl: data.channel.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },

    outcomes: data.outcomes.map(outcome => {
      const estimatedOdds = parseFloat(outcome.estimatedOdds);
      return {
        ...outcome,
        estimatedOdds: estimatedOdds.toFixed(2),
        estimatedOddsFormatted: (estimatedOdds / 100).toFixed(2),
        volume: createAmountField(outcome.volume),
      };
    }),

    totalVolume: createAmountField(data.totalVolume),
    collateralAmount: createAmountField(data.collateralAmount),
    maxOutcomeVolume: createAmountField(data.maxOutcomeVolume),

    statusText: MARKET_STATUS_TEXT_MAP[data.status],

    predictionDeadline: getMiliseconds(data.predictionDeadline),
    resultConfirmDeadline: getMiliseconds(data.resultConfirmDeadline),
    outcomeProposedAt: data.outcomeProposedAt ? getMiliseconds(data.outcomeProposedAt) : null,
    disputedAt: data.disputedAt ? getMiliseconds(data.disputedAt) : null,
    finalizedAt: data.finalizedAt ? getMiliseconds(data.finalizedAt) : null,
    createdAt: getMiliseconds(data.createdAt),
    disputeDeadline: data.disputeDeadline ? getMiliseconds(data.disputeDeadline) : null,
    disputedAmount: data.disputedAmount,
  };
};

export type MarketTransformed = ReturnType<typeof transformMarket>;
export type MarketOutcome = ReturnType<typeof transformMarket>['outcomes'][number];

export const transformGetMarketsResponse = (data: GetMarketsResponseType) => {
  return {
    ...data,
    markets: data.markets.map(transformMarket),
  };
};

export const transformMarketTopPredictorsResponse = (data: MarketTopPredictorsResponseSchema) => {
  return {
    ...data,
    topPredictors: data.topPredictors.map(predictor => ({
      ...predictor,
      imageUrl: predictor.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
      formattedAmount: formatUsdc(predictor.amount),
      rawAmount: BigNumber(predictor.amount),

      // raw: {
      //   amount: BigNumber(predictor.amount),
      // },

      // formatted: {
      //   amount: formatUsdc(predictor.amount),
      // },
    })),
  };
};

const transformMarketActivity = (data: MarketActivitySchema) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigNumber(data.amount),
    relativeTime: toRelativeTime(data.timestamp) + ' ago',

    // raw: {
    //   amount: BigNumber(data.amount),
    // },

    // formatted: {
    //   amount: formatUsdc(data.amount),
    // },
  };
};

export const transformMarketActivitiesResponse = (data: MarketActivitiesResponseSchema) => {
  return {
    ...data,
    activities: data.activities.map(transformMarketActivity),
  };
};
