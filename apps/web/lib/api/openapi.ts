import { makeApi, Zodios, type ZodiosOptions } from '@zodios/core';
import { z } from 'zod';

const PredictReqDto = z
  .object({
    marketId: z.string().min(66).max(66),
    outcome: z.string().min(1).max(50),
    amount: z.number().int(),
  })
  .passthrough();
const UserOperationDto = z
  .object({
    paymaster: z.unknown().optional(),
    sender: z.unknown(),
    callData: z.unknown(),
    signature: z.unknown(),
    paymasterData: z.unknown().optional(),
    maxFeePerGas: z.number().int(),
    maxPriorityFeePerGas: z.number().int(),
    nonce: z.number().int(),
    paymasterPostOpGasLimit: z.number().int().optional(),
    callGasLimit: z.number().int(),
    preVerificationGas: z.number().int(),
    verificationGasLimit: z.number().int(),
    paymasterVerificationGasLimit: z.number().int().optional(),
    factory: z.unknown().optional(),
    factoryData: z.unknown().optional(),
  })
  .passthrough();
const FinalizeReqDto = z.object({ marketId: z.string().min(66).max(66) }).passthrough();
const RedeemReqDto = z
  .object({ marketIds: z.array(z.string().min(66).max(66)).min(1).max(20) })
  .passthrough();
const DepositChannelCollateralReqDto = z.object({ amount: z.number().int() }).passthrough();
const WithdrawChannelCollateralReqDto = z.object({ amount: z.number().int() }).passthrough();
const DepositDisputeCollateralReqDto = z
  .object({
    marketId: z.string().min(66).max(66),
    amount: z.number().int(),
    description: z.string().max(1000).optional(),
    referenceURL: z.union([z.string(), z.literal('')]).optional(),
    files: z.array(z.instanceof(File)).max(3).optional(),
  })
  .passthrough();
const ClaimReqDto = z.discriminatedUnion('distributionType', [
  z
    .object({
      distributionType: z.literal(2),
      category: z.enum(['ChannelLeader', 'Maker', 'PlatformFee']),
    })
    .passthrough(),
  z
    .object({
      distributionType: z.literal(1),
      category: z.enum(['Channel', 'ChannelDispute', 'Dispute']),
    })
    .passthrough(),
  z
    .object({
      distributionType: z.literal(0),
      category: z.enum(['CommissionReward', 'FeeRebate', 'Share']),
    })
    .passthrough(),
]);
const WithdrawReqDto = z.object({ to: z.string(), amount: z.number().int() }).passthrough();
const GoResDto = z.object({ hash: z.string() }).passthrough();
const ReferralBenefitsResDto = z
  .object({
    benefits: z.array(
      z
        .object({
          level: z.number(),
          commissionRewardRatio: z.number(),
          feeRebateRatio: z.number(),
          accumulatedProfit: z.number().int(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const ReferralDashboardResDto = z
  .object({ totalCommissionReward: z.number().int(), totalFeeRebate: z.number().int() })
  .passthrough();
const ReferralLeaderboardResDto = z
  .object({
    leaderboard: z.array(
      z
        .object({
          address: z.string(),
          commissionReward: z.number().int(),
          nickname: z.string(),
          invitees: z.number(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const ReferralBenefitResDto = z
  .object({
    level: z.number(),
    commissionRewardRatio: z.number(),
    feeRebateRatio: z.number(),
    accumulatedProfit: z.number().int(),
    totalInvitees: z.number(),
    bindReferralCode: z.string().optional(),
    referralCode: z.string(),
    profit: z.number().int(),
  })
  .passthrough();
const RewardDetailsResDto = z
  .object({
    rewards: z.array(
      z
        .object({ timestamp: z.string().datetime({ offset: true }), amount: z.number().int() })
        .passthrough()
    ),
    totalLength: z.number(),
  })
  .passthrough();
const ClaimableReferralResDto = z
  .object({ commissionReward: z.number().int(), feeRebate: z.number().int() })
  .passthrough();
const MarketActivitiesResDto = z
  .object({
    activities: z.array(
      z
        .object({
          market: z
            .object({ id: z.string(), title: z.string(), imageUrl: z.string().nullable() })
            .passthrough(),
          user: z
            .object({ address: z.string(), nickname: z.string(), imageUrl: z.string().nullable() })
            .passthrough(),
          outcomes: z.array(
            z.object({ outcome: z.string(), outcomeOrder: z.number() }).passthrough()
          ),
          amount: z.number().int(),
          timestamp: z.string().datetime({ offset: true }),
          transactionHash: z.string(),
          type: z.string(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const LikeResDto = z.object({ id: z.string(), isLiked: z.boolean() }).passthrough();
const PostResDto = z
  .object({
    id: z.string(),
    refId: z.string(),
    title: z.string().min(1).max(100),
    content: z.string().min(1).max(2000),
    createdAt: z.string().datetime({ offset: true }),
    updatedAt: z.string().datetime({ offset: true }),
    views: z.number().int().gte(0),
    likes: z.number().int().gte(0),
    commentCount: z.number().int().gte(0),
    author: z
      .object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    isLiked: z.boolean(),
    isPinned: z.boolean(),
  })
  .passthrough();
const EditChannelPostReqDto = z
  .object({ title: z.string().min(1).max(100), content: z.string().min(1).max(2000) })
  .partial()
  .passthrough();
const SuccessResDto = z.object({ success: z.boolean() }).passthrough();
const PostsResDto = z
  .object({
    posts: z.array(
      z
        .object({
          id: z.string(),
          refId: z.string(),
          title: z.string().min(1).max(100),
          content: z.string().min(1).max(2000),
          createdAt: z.string().datetime({ offset: true }),
          updatedAt: z.string().datetime({ offset: true }),
          views: z.number().int().gte(0),
          likes: z.number().int().gte(0),
          commentCount: z.number().int().gte(0),
          author: z
            .object({
              address: z.string(),
              nickname: z.string(),
              imageUrl: z.union([z.string(), z.literal('')]).optional(),
            })
            .passthrough(),
          isLiked: z.boolean(),
          isPinned: z.boolean(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const CreateChannelPostReqDto = z
  .object({ title: z.string().min(1).max(100), content: z.string().min(1).max(2000) })
  .passthrough();
const CommentsResDto = z
  .object({
    comments: z.array(
      z
        .object({
          id: z.string(),
          parentId: z.string(),
          content: z.string().min(1).max(1000),
          createdAt: z.string().datetime({ offset: true }),
          author: z
            .object({
              address: z.string(),
              nickname: z.string(),
              imageUrl: z.union([z.string(), z.literal('')]).optional(),
            })
            .passthrough(),
          likes: z.number().int().gte(0),
          commentCount: z.number().int().gte(0),
          isLiked: z.boolean(),
          positions: z
            .array(
              z
                .object({
                  outcome: z.string(),
                  outcomeOrder: z.number().int(),
                  value: z.number().int(),
                })
                .passthrough()
            )
            .optional(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const CreateCommentReqDto = z.object({ content: z.string().min(1).max(1000) }).passthrough();
const CommentResDto = z
  .object({
    id: z.string(),
    parentId: z.string(),
    content: z.string().min(1).max(1000),
    createdAt: z.string().datetime({ offset: true }),
    author: z
      .object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    likes: z.number().int().gte(0),
    commentCount: z.number().int().gte(0),
    isLiked: z.boolean(),
    positions: z
      .array(
        z
          .object({ outcome: z.string(), outcomeOrder: z.number().int(), value: z.number().int() })
          .passthrough()
      )
      .optional(),
  })
  .passthrough();
const GetPostReqDto = z.object({ postId: z.string() }).passthrough();
const CategoriesResDto = z
  .object({
    categories: z.array(
      z
        .object({
          name: z.string().min(1).max(50),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const CategoryPageResDto = z
  .object({
    category: z
      .object({
        name: z.string().min(1).max(50),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    liveMarketCount: z.number().int().gte(0),
    totalVolume: z.number().int(),
  })
  .passthrough();
const ChannelBriefInfosResDto = z
  .object({
    channels: z.array(
      z
        .object({
          id: z.string(),
          name: z.string(),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const PopularChannelResDto = z
  .object({
    channels: z.array(
      z
        .object({
          id: z.string(),
          name: z.string(),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
          volume: z.number().int(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const MarketsResDto = z
  .object({
    markets: z.array(
      z
        .object({
          id: z.string().min(66).max(66),
          title: z.string().min(1).max(100),
          description: z.string().min(1).max(2000),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
          channel: z
            .object({ id: z.string(), name: z.string(), imageUrl: z.string().nullable() })
            .passthrough(),
          maker: z.string(),
          category: z.string().min(1).max(50),
          minPredictCount: z.number(),
          collateralAmount: z.number().int(),
          totalVolume: z.number().int(),
          competitive: z.number(),
          disputedAmount: z.number(),
          broadcastURL: z.union([z.string(), z.literal('')]).optional(),
          referenceURL: z.union([z.string(), z.literal('')]),
          predictionDeadline: z.string().datetime({ offset: true }),
          resultConfirmDeadline: z.string().datetime({ offset: true }),
          disputeDeadline: z.string().datetime({ offset: true }),
          proposedOutcome: z.string().optional(),
          finalOutcome: z.string().optional(),
          outcomeProposedAt: z.string().datetime({ offset: true }).optional(),
          disputedAt: z.string().datetime({ offset: true }).optional(),
          finalizedAt: z.string().datetime({ offset: true }).optional(),
          createdAt: z.string().datetime({ offset: true }),
          totalPredictor: z.number(),
          predictCount: z.number(),
          outcomes: z.array(
            z
              .object({
                marketId: z.string(),
                outcome: z.string(),
                order: z.number().int(),
                volume: z.number().int(),
                estimatedOdds: z.string(),
              })
              .passthrough()
          ),
          maxOutcomeVolume: z.number().int(),
          status: z.enum([
            'OPEN',
            'REVIEWING',
            'DISPUTABLE',
            'DISPUTED',
            'CLOSED_WITHOUT_DISPUTE',
            'CLOSED_WITH_DISPUTE_ACCEPTED',
            'CLOSED_WITH_DISPUTE_REJECTED',
            'CANCELLED_WITH_UNMET',
            'CANCELLED_WITH_INVALID',
            'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
          ]),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const GetInChannelLeaderboardResDto = z
  .object({
    rankings: z.array(
      z
        .object({
          rank: z.number(),
          address: z.string(),
          nickname: z.string(),
          pnl: z.number().int(),
          volume: z.number().int(),
        })
        .passthrough()
    ),
    myRank: z
      .object({
        rank: z.number(),
        address: z.string(),
        nickname: z.string(),
        pnl: z.number().int(),
        volume: z.number().int(),
      })
      .passthrough()
      .optional(),
  })
  .passthrough();
const ChannelActivePredictionsResDto = z
  .object({
    markets: z.array(
      z
        .object({
          id: z.string().min(66).max(66),
          title: z.string().min(1).max(100),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
          totalVolume: z.number().int(),
          deposit: z.number().int(),
          status: z.enum([
            'OPEN',
            'REVIEWING',
            'DISPUTABLE',
            'DISPUTED',
            'CLOSED_WITHOUT_DISPUTE',
            'CLOSED_WITH_DISPUTE_ACCEPTED',
            'CLOSED_WITH_DISPUTE_REJECTED',
            'CANCELLED_WITH_UNMET',
            'CANCELLED_WITH_INVALID',
            'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
          ]),
          createdAt: z.string().datetime({ offset: true }),
          finalizedAt: z.string().datetime({ offset: true }).optional(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ChannelHistoryPredictionsResDto = z
  .object({
    markets: z.array(
      z
        .object({
          id: z.string().min(66).max(66),
          title: z.string().min(1).max(100),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
          finalizedAt: z.string().datetime({ offset: true }),
          rewards: z
            .object({ prediction: z.number().int(), dispute: z.number().int() })
            .passthrough(),
          outcomes: z.object({ initial: z.string(), final: z.string() }).passthrough(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ChannelCollateralStatResDto = z
  .object({ available: z.number().int(), tied: z.number().int(), isLocked: z.boolean() })
  .passthrough();
const ChannelCollateralHistoryResDto = z
  .object({
    histories: z.array(
      z
        .object({
          type: z.string(),
          category: z.string().optional(),
          amount: z.number().int(),
          timestamp: z.string().datetime({ offset: true }),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ChannelRewardsResDto = z
  .object({ totalRewards: z.number().int(), claimableRewards: z.number().int() })
  .passthrough();
const ChannelRewardsHistoryResDto = z
  .object({
    histories: z.array(
      z
        .object({
          amount: z.number().int(),
          timestamp: z.string().datetime({ offset: true }),
          claimedAt: z.string().datetime({ offset: true }).optional(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ChannelIdReqDto = z.object({ channelId: z.string() }).passthrough();
const ChannelInfoResDto = z
  .object({
    id: z.string(),
    name: z.string(),
    imageUrl: z.union([z.string(), z.literal('')]).optional(),
    bannerUrl: z.union([z.string(), z.literal('')]).optional(),
    description: z.string().optional(),
    subscribers: z.number().int().gte(0),
    totalVolume: z.number().int(),
    totalMarkets: z.number().int().gte(0),
    channelSns: z
      .array(
        z
          .object({
            snsType: z.enum([
              'youtube',
              'twitter',
              'telegram',
              'facebook',
              'discord',
              'tiktok',
              'instagram',
              'abstract',
            ]),
            snsUrl: z.union([z.string(), z.literal('')]),
          })
          .passthrough()
      )
      .optional(),
    isLeaderLive: z.boolean(),
    isSubscribed: z.boolean(),
  })
  .passthrough();
const ChannelPredictionDepositReqDto = z
  .object({ marketId: z.string().min(66).max(66), amount: z.number().int(), signature: z.string() })
  .passthrough();
const UpdateChannelReqDto = z
  .object({
    name: z
      .string()
      .min(1)
      .max(20)
      .regex(/^[a-zA-Z0-9\s]+$/),
    description: z.string().max(255),
    image: z.instanceof(File),
    banner: z.instanceof(File),
    channelSns: z.union([
      z
        .object({
          snsType: z.enum([
            'youtube',
            'twitter',
            'telegram',
            'facebook',
            'discord',
            'tiktok',
            'instagram',
            'abstract',
          ]),
          snsUrl: z.union([z.string(), z.literal('')]),
        })
        .passthrough(),
      z.array(
        z
          .object({
            snsType: z.enum([
              'youtube',
              'twitter',
              'telegram',
              'facebook',
              'discord',
              'tiktok',
              'instagram',
              'abstract',
            ]),
            snsUrl: z.union([z.string(), z.literal('')]),
          })
          .passthrough()
      ),
    ]),
  })
  .partial()
  .passthrough();
const GetPredictLeaderboardResDto = z
  .object({
    results: z.array(
      z
        .object({
          address: z.string(),
          nickname: z.string(),
          imageUrl: z.string().nullish(),
          value: z.string(),
          rank: z.number(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const GetPredictLeaderboardByAddressResDto = z
  .object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullish(),
    value: z.string(),
    rank: z.number(),
  })
  .passthrough();
const GetChannelLeaderboardResDto = z
  .object({
    results: z.array(
      z
        .object({
          channelId: z.string(),
          name: z.string(),
          imageUrl: z.string().nullish(),
          value: z.string(),
          rank: z.number(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const GetChannelLeaderboardByAddressResDto = z
  .object({
    channelId: z.string(),
    name: z.string(),
    imageUrl: z.string().nullish(),
    value: z.string(),
    rank: z.number(),
  })
  .passthrough();
const CreateMarketReqDto = z
  .object({
    channelId: z.string(),
    title: z.string().min(1).max(100),
    description: z.string().min(1).max(2000),
    predictionDeadline: z.string().datetime({ offset: true }),
    resultConfirmDeadline: z.string().datetime({ offset: true }),
    disputedPeriod: z.literal('30m'),
    category: z.string().min(1).max(50),
    collateralAmount: z.number().int(),
    outcomes: z.array(z.string().min(1).max(50)).min(2).max(10),
    tags: z.union([z.string(), z.array(z.string().min(1).max(20))]).optional(),
    broadcastURL: z.union([z.string(), z.literal('')]).optional(),
    referenceURL: z.union([z.string(), z.literal('')]),
    image: z.instanceof(File).optional(),
    validationId: z.string().uuid(),
    signature: z.string(),
  })
  .passthrough();
const MarketResDto = z
  .object({
    id: z.string().min(66).max(66),
    title: z.string().min(1).max(100),
    description: z.string().min(1).max(2000),
    imageUrl: z.union([z.string(), z.literal('')]).optional(),
    channel: z
      .object({ id: z.string(), name: z.string(), imageUrl: z.string().nullable() })
      .passthrough(),
    maker: z.string(),
    category: z.string().min(1).max(50),
    minPredictCount: z.number(),
    collateralAmount: z.number().int(),
    totalVolume: z.number().int(),
    competitive: z.number(),
    disputedAmount: z.number(),
    broadcastURL: z.union([z.string(), z.literal('')]).optional(),
    referenceURL: z.union([z.string(), z.literal('')]),
    predictionDeadline: z.string().datetime({ offset: true }),
    resultConfirmDeadline: z.string().datetime({ offset: true }),
    disputeDeadline: z.string().datetime({ offset: true }),
    proposedOutcome: z.string().optional(),
    finalOutcome: z.string().optional(),
    outcomeProposedAt: z.string().datetime({ offset: true }).optional(),
    disputedAt: z.string().datetime({ offset: true }).optional(),
    finalizedAt: z.string().datetime({ offset: true }).optional(),
    createdAt: z.string().datetime({ offset: true }),
    totalPredictor: z.number(),
    predictCount: z.number(),
    outcomes: z.array(
      z
        .object({
          marketId: z.string(),
          outcome: z.string(),
          order: z.number().int(),
          volume: z.number().int(),
          estimatedOdds: z.string(),
        })
        .passthrough()
    ),
    maxOutcomeVolume: z.number().int(),
    status: z.enum([
      'OPEN',
      'REVIEWING',
      'DISPUTABLE',
      'DISPUTED',
      'CLOSED_WITHOUT_DISPUTE',
      'CLOSED_WITH_DISPUTE_ACCEPTED',
      'CLOSED_WITH_DISPUTE_REJECTED',
      'CANCELLED_WITH_UNMET',
      'CANCELLED_WITH_INVALID',
      'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
    ]),
  })
  .passthrough();
const MarketBriefInfosResDto = z
  .object({
    markets: z.array(
      z
        .object({
          id: z.string().min(66).max(66),
          title: z.string(),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
        })
        .passthrough()
    ),
  })
  .passthrough();
const MarketTopPredictorsResDto = z
  .object({
    marketId: z.string().min(66).max(66),
    outcome: z.string().min(1).max(50),
    topPredictors: z.array(
      z
        .object({
          address: z.string(),
          nickname: z.string(),
          imageUrl: z.string().nullable(),
          amount: z.number().int(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ValidateMarketReqDto = z.object({ title: z.string().min(1).max(100) }).passthrough();
const ValidateMarketResDto = z.union([
  z.object({ isEthical: z.literal(true), validationId: z.string().uuid() }).passthrough(),
  z.object({ isEthical: z.literal(false), reason: z.string() }).passthrough(),
]);
const ProposeReqDto = z
  .object({
    marketId: z.string().min(66).max(66),
    outcome: z.string().min(1).max(50),
    signature: z.string(),
  })
  .passthrough();
const ReportMarketReqDto = z
  .object({ marketId: z.string().min(66).max(66), reason: z.string().max(500).optional() })
  .passthrough();
const UserPortfolioResDto = z
  .object({
    positionsValue: z.number().int(),
    profit: z
      .object({
        day: z.number().int(),
        week: z.number().int(),
        month: z.number().int(),
        total: z.number().int(),
      })
      .passthrough(),
  })
  .passthrough();
const PositionsResDto = z
  .object({
    positions: z.array(
      z
        .object({
          market: z
            .object({
              id: z.string(),
              title: z.string(),
              imageUrl: z.string().nullable(),
              status: z
                .enum([
                  'OPEN',
                  'REVIEWING',
                  'DISPUTABLE',
                  'DISPUTED',
                  'CLOSED_WITHOUT_DISPUTE',
                  'CLOSED_WITH_DISPUTE_ACCEPTED',
                  'CLOSED_WITH_DISPUTE_REJECTED',
                  'CANCELLED_WITH_UNMET',
                  'CANCELLED_WITH_INVALID',
                  'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
                ])
                .optional(),
            })
            .passthrough(),
          user: z
            .object({ address: z.string(), nickname: z.string(), imageUrl: z.string().nullable() })
            .passthrough(),
          outcome: z.string(),
          outcomeOrder: z.number(),
          value: z.number().int(),
          estimatedOdds: z.string(),
          estimatedWin: z.number().int(),
          updatedAt: z.string().datetime({ offset: true }),
          redeemedAt: z.string().datetime({ offset: true }).nullish(),
          voidedAt: z.string().datetime({ offset: true }).nullish(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const ClaimableDisputeResDto = z.object({ claimable: z.number().int() }).passthrough();
const PortfolioDisputesResDto = z
  .object({
    disputes: z.array(
      z
        .object({
          market: z
            .object({
              id: z.string().min(66).max(66),
              title: z.string().min(1).max(100),
              imageUrl: z.string().nullish(),
              status: z.enum([
                'OPEN',
                'REVIEWING',
                'DISPUTABLE',
                'DISPUTED',
                'CLOSED_WITHOUT_DISPUTE',
                'CLOSED_WITH_DISPUTE_ACCEPTED',
                'CLOSED_WITH_DISPUTE_REJECTED',
                'CANCELLED_WITH_UNMET',
                'CANCELLED_WITH_INVALID',
                'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
              ]),
            })
            .passthrough(),
          amount: z.number().int(),
          reward: z.number().int().optional(),
          timestamp: z.string().datetime({ offset: true }),
          claimedAt: z.string().datetime({ offset: true }).optional(),
        })
        .passthrough()
    ),
    totalLength: z.number(),
  })
  .passthrough();
const UserSubscriptionsResDto = z
  .object({
    channels: z.array(
      z
        .object({
          id: z.string(),
          name: z.string(),
          imageUrl: z.string().optional(),
          leader: z
            .object({ address: z.string(), nickname: z.string(), imageUrl: z.string().optional() })
            .passthrough(),
          sns: z
            .array(
              z
                .object({
                  snsType: z.enum([
                    'youtube',
                    'twitter',
                    'telegram',
                    'facebook',
                    'discord',
                    'tiktok',
                    'instagram',
                    'abstract',
                  ]),
                  snsUrl: z.union([z.string(), z.literal('')]),
                })
                .passthrough()
            )
            .optional(),
          subscribers: z.number(),
          isSubscribed: z.boolean(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const UserInfoResDto = z
  .object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullable(),
    email: z.string().nullable(),
    bio: z.string(),
    createdAt: z.string().datetime({ offset: true }),
  })
  .passthrough();
const UserStatsResDto = z
  .object({
    address: z.string(),
    positionsValue: z.number().int(),
    profit: z.number().int(),
    volume: z.number().int(),
    tradedMarketsCount: z.number(),
  })
  .passthrough();
const UserActivitiesResDto = z
  .object({
    address: z.string(),
    activities: z.array(
      z
        .object({
          id: z.number(),
          market: z
            .object({ id: z.string(), title: z.string(), imageUrl: z.string().nullable() })
            .passthrough()
            .nullable(),
          type: z.string(),
          outcome: z.string().nullable(),
          value: z.number().int().nullable(),
          transactionHash: z.string(),
          timestamp: z.string().datetime({ offset: true }),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const CreateUserReqDto = z.object({ referralCode: z.string() }).partial().passthrough();
const UpdateUserProfileReqDto = z
  .object({
    image: z.instanceof(File),
    nickname: z
      .string()
      .min(1)
      .max(42)
      .regex(/^[a-zA-Z0-9]*$/),
    bio: z.string().max(256),
  })
  .partial()
  .passthrough();
const BroadcastResDto = z
  .object({
    broadcasts: z.array(
      z
        .object({
          type: z.enum(['JACKPOT', 'SHARE']),
          nickname: z.string(),
          amount: z.number().int(),
          market: z
            .object({ id: z.string(), title: z.string(), imageUrl: z.string().nullish() })
            .passthrough(),
          timestamp: z.string().datetime({ offset: true }),
        })
        .passthrough()
    ),
    totalLength: z.number(),
  })
  .passthrough();
const ShareDashboardResDto = z
  .object({ totalShareReward: z.number().int(), totalShared: z.number() })
  .passthrough();
const MyShareDashboardResDto = z
  .object({
    claimableAmount: z.number().int(),
    unLockableShareRewards: z.number().int(),
    totalClaimed: z.number().int(),
  })
  .passthrough();
const GetSharesResDto = z
  .object({
    shares: z.array(
      z
        .object({
          market: z
            .object({ id: z.string(), title: z.string(), imageUrl: z.string().optional() })
            .passthrough(),
          winnings: z.number().int(),
          shareableAmount: z.number().int(),
          sharedAt: z.string().datetime({ offset: true }).optional(),
        })
        .passthrough()
    ),
    totalLength: z.number(),
  })
  .passthrough();
const ShareReqDto = z.object({ marketId: z.string(), signature: z.string() }).passthrough();
const GetOddsResDto = z.object({ odds: z.number() }).passthrough();

export const schemas = {
  PredictReqDto,
  UserOperationDto,
  FinalizeReqDto,
  RedeemReqDto,
  DepositChannelCollateralReqDto,
  WithdrawChannelCollateralReqDto,
  DepositDisputeCollateralReqDto,
  ClaimReqDto,
  WithdrawReqDto,
  GoResDto,
  ReferralBenefitsResDto,
  ReferralDashboardResDto,
  ReferralLeaderboardResDto,
  ReferralBenefitResDto,
  RewardDetailsResDto,
  ClaimableReferralResDto,
  MarketActivitiesResDto,
  LikeResDto,
  PostResDto,
  EditChannelPostReqDto,
  SuccessResDto,
  PostsResDto,
  CreateChannelPostReqDto,
  CommentsResDto,
  CreateCommentReqDto,
  CommentResDto,
  GetPostReqDto,
  CategoriesResDto,
  CategoryPageResDto,
  ChannelBriefInfosResDto,
  PopularChannelResDto,
  MarketsResDto,
  GetInChannelLeaderboardResDto,
  ChannelActivePredictionsResDto,
  ChannelHistoryPredictionsResDto,
  ChannelCollateralStatResDto,
  ChannelCollateralHistoryResDto,
  ChannelRewardsResDto,
  ChannelRewardsHistoryResDto,
  ChannelIdReqDto,
  ChannelInfoResDto,
  ChannelPredictionDepositReqDto,
  UpdateChannelReqDto,
  GetPredictLeaderboardResDto,
  GetPredictLeaderboardByAddressResDto,
  GetChannelLeaderboardResDto,
  GetChannelLeaderboardByAddressResDto,
  CreateMarketReqDto,
  MarketResDto,
  MarketBriefInfosResDto,
  MarketTopPredictorsResDto,
  ValidateMarketReqDto,
  ValidateMarketResDto,
  ProposeReqDto,
  ReportMarketReqDto,
  UserPortfolioResDto,
  PositionsResDto,
  ClaimableDisputeResDto,
  PortfolioDisputesResDto,
  UserSubscriptionsResDto,
  UserInfoResDto,
  UserStatsResDto,
  UserActivitiesResDto,
  CreateUserReqDto,
  UpdateUserProfileReqDto,
  BroadcastResDto,
  ShareDashboardResDto,
  MyShareDashboardResDto,
  GetSharesResDto,
  ShareReqDto,
  GetOddsResDto,
};

const endpoints = makeApi([
  {
    method: 'get',
    path: '/v1/activity/predict',
    alias: 'ActivityController_getPredictActivities',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: MarketActivitiesResDto,
  },
  {
    method: 'get',
    path: '/v1/board/comment/:parentId',
    alias: 'CommentController_getComments',
    requestFormat: 'json',
    parameters: [
      {
        name: 'parentId',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['latest', 'likes']).optional().default('latest'),
      },
      {
        name: 'withPositions',
        type: 'Query',
        schema: z.enum(['true', 'false']).optional().default('false'),
      },
      {
        name: 'onlyPredictors',
        type: 'Query',
        schema: z.enum(['true', 'false']).optional().default('false'),
      },
    ],
    response: CommentsResDto,
  },
  {
    method: 'post',
    path: '/v1/board/comment/:parentId',
    alias: 'CommentController_createComment',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ content: z.string().min(1).max(1000) }).passthrough(),
      },
      {
        name: 'parentId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: CommentResDto,
  },
  {
    method: 'post',
    path: '/v1/board/like/:postId',
    alias: 'LikeController_likePost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: LikeResDto,
  },
  {
    method: 'get',
    path: '/v1/board/post/:postId',
    alias: 'PostController_getPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: PostResDto,
  },
  {
    method: 'patch',
    path: '/v1/board/post/:postId',
    alias: 'PostController_editPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: EditChannelPostReqDto,
      },
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: PostResDto,
  },
  {
    method: 'delete',
    path: '/v1/board/post/:postId',
    alias: 'PostController_deletePost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'post',
    path: '/v1/board/post/channel',
    alias: 'PostController_createChannelPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: CreateChannelPostReqDto,
      },
    ],
    response: PostResDto,
  },
  {
    method: 'get',
    path: '/v1/board/post/channel/:channelId',
    alias: 'PostController_getChannelPosts',
    requestFormat: 'json',
    parameters: [
      {
        name: 'channelId',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: PostsResDto,
  },
  {
    method: 'post',
    path: '/v1/board/post/pin/:postId',
    alias: 'PostController_pinPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: PostResDto,
  },
  {
    method: 'post',
    path: '/v1/board/post/unpin/:postId',
    alias: 'PostController_unpinPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'postId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: PostResDto,
  },
  {
    method: 'post',
    path: '/v1/board/report',
    alias: 'ReportController_reportChannelPost',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ postId: z.string() }).passthrough(),
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/broadcast',
    alias: 'BroadcastController_getBroadcast',
    requestFormat: 'json',
    response: BroadcastResDto,
  },
  {
    method: 'get',
    path: '/v1/category/:category',
    alias: 'CategoryController_getCategoryStat',
    requestFormat: 'json',
    parameters: [
      {
        name: 'category',
        type: 'Path',
        schema: z.string().min(1).max(50),
      },
    ],
    response: CategoryPageResDto,
  },
  {
    method: 'get',
    path: '/v1/category/:category/tags',
    alias: 'CategoryController_getTagsInCategory',
    requestFormat: 'json',
    parameters: [
      {
        name: 'category',
        type: 'Path',
        schema: z.string().min(1).max(50),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: z.void(),
  },
  {
    method: 'get',
    path: '/v1/category/categories',
    alias: 'CategoryController_getCategories',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: CategoriesResDto,
  },
  {
    method: 'patch',
    path: '/v1/channel',
    alias: 'ChannelController_updateChannel',
    requestFormat: 'form-data',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: UpdateChannelReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/channel/:channelId',
    alias: 'ChannelController_getChannelById',
    requestFormat: 'json',
    parameters: [
      {
        name: 'channelId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: ChannelInfoResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/:channelId/leaderboard/:type',
    alias: 'ChannelController_getMyInChannelRanking',
    requestFormat: 'json',
    parameters: [
      {
        name: 'channelId',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'type',
        type: 'Path',
        schema: z.enum(['VOLUME', 'PROFIT']),
      },
    ],
    response: GetInChannelLeaderboardResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/:channelId/markets',
    alias: 'ChannelController_getChannelMarkets',
    requestFormat: 'json',
    parameters: [
      {
        name: 'channelId',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'status',
        type: 'Query',
        schema: z.enum(['LIVE', 'ENDED']).optional().default('LIVE'),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['VOLUME', 'NEWEST']).optional().default('VOLUME'),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: MarketsResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/collateral',
    alias: 'ChannelController_getCollateral',
    requestFormat: 'json',
    response: ChannelCollateralStatResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/collateral/history',
    alias: 'ChannelController_getCollateralHistory',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: ChannelCollateralHistoryResDto,
  },
  {
    method: 'post',
    path: '/v1/channel/market/deposit',
    alias: 'ChannelController_deposit',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: ChannelPredictionDepositReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/channel/popular',
    alias: 'ChannelController_getPopularChannels',
    requestFormat: 'json',
    response: PopularChannelResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/predictions/active',
    alias: 'ChannelController_getActivePredictions',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: ChannelActivePredictionsResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/predictions/history',
    alias: 'ChannelController_getHistoryPredictions',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: ChannelHistoryPredictionsResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/rewards',
    alias: 'ChannelController_getRewards',
    requestFormat: 'json',
    response: ChannelRewardsResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/rewards/history',
    alias: 'ChannelController_getRewardsHistory',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['VALUE', 'NEWEST']).optional().default('VALUE'),
      },
    ],
    response: ChannelRewardsHistoryResDto,
  },
  {
    method: 'get',
    path: '/v1/channel/search',
    alias: 'ChannelController_searchChannels',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'query',
        type: 'Query',
        schema: z.string().min(2).max(50),
      },
    ],
    response: ChannelBriefInfosResDto,
  },
  {
    method: 'post',
    path: '/v1/channel/subscribe',
    alias: 'ChannelController_subscribeChannel',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ channelId: z.string() }).passthrough(),
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'post',
    path: '/v1/channel/unsubscribe',
    alias: 'ChannelController_unsubscribeChannel',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ channelId: z.string() }).passthrough(),
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/leaderboard/channel/:period',
    alias: 'LeaderboardController_getChannelLeaderboard',
    requestFormat: 'json',
    parameters: [
      {
        name: 'period',
        type: 'Path',
        schema: z.enum(['DAY', 'WEEK', 'MONTH', 'ALL']),
      },
    ],
    response: GetChannelLeaderboardResDto,
  },
  {
    method: 'get',
    path: '/v1/leaderboard/channel/:period/:channelId',
    alias: 'LeaderboardController_getChannelLeaderboardByChannelId',
    requestFormat: 'json',
    parameters: [
      {
        name: 'period',
        type: 'Path',
        schema: z.enum(['DAY', 'WEEK', 'MONTH', 'ALL']),
      },
      {
        name: 'channelId',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: GetChannelLeaderboardByAddressResDto,
  },
  {
    method: 'get',
    path: '/v1/leaderboard/predict/:type/:period',
    alias: 'LeaderboardController_getLeaderboard',
    requestFormat: 'json',
    parameters: [
      {
        name: 'type',
        type: 'Path',
        schema: z.enum(['VOLUME', 'PROFIT']),
      },
      {
        name: 'period',
        type: 'Path',
        schema: z.enum(['DAY', 'WEEK', 'MONTH', 'ALL']),
      },
    ],
    response: GetPredictLeaderboardResDto,
  },
  {
    method: 'get',
    path: '/v1/leaderboard/predict/:type/:period/:address',
    alias: 'LeaderboardController_getLeaderboardByAddress',
    requestFormat: 'json',
    parameters: [
      {
        name: 'type',
        type: 'Path',
        schema: z.enum(['VOLUME', 'PROFIT']),
      },
      {
        name: 'period',
        type: 'Path',
        schema: z.enum(['DAY', 'WEEK', 'MONTH', 'ALL']),
      },
      {
        name: 'address',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: GetPredictLeaderboardByAddressResDto,
  },
  {
    method: 'get',
    path: '/v1/market',
    alias: 'MarketController_getMarkets',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'filter',
        type: 'Query',
        schema: z.enum(['LIVE', 'DISPUTABLE']).optional(),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z
          .enum(['VOLUME', 'NEWEST', 'ENDING_SOON', 'COMPETITIVE'])
          .optional()
          .default('VOLUME'),
      },
      {
        name: 'tag',
        type: 'Query',
        schema: z.string().optional(),
      },
    ],
    response: MarketsResDto,
  },
  {
    method: 'post',
    path: '/v1/market',
    alias: 'MarketController_createMarket',
    requestFormat: 'form-data',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: CreateMarketReqDto,
      },
    ],
    response: MarketResDto,
  },
  {
    method: 'get',
    path: '/v1/market/:marketId',
    alias: 'MarketController_getMarketById',
    requestFormat: 'json',
    parameters: [
      {
        name: 'marketId',
        type: 'Path',
        schema: z.string().min(66).max(66),
      },
    ],
    response: MarketResDto,
  },
  {
    method: 'get',
    path: '/v1/market/activities/:marketId/:outcome',
    alias: 'MarketController_getMarketActivities',
    requestFormat: 'json',
    parameters: [
      {
        name: 'marketId',
        type: 'Path',
        schema: z.string().min(66).max(66),
      },
      {
        name: 'outcome',
        type: 'Path',
        schema: z.string().min(1).max(50),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: MarketActivitiesResDto,
  },
  {
    method: 'get',
    path: '/v1/market/category/:category',
    alias: 'MarketController_getMarketsInCategory',
    requestFormat: 'json',
    parameters: [
      {
        name: 'category',
        type: 'Path',
        schema: z.string().min(1).max(50),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'filter',
        type: 'Query',
        schema: z.enum(['LIVE', 'DISPUTABLE']).optional(),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z
          .enum(['VOLUME', 'NEWEST', 'ENDING_SOON', 'COMPETITIVE'])
          .optional()
          .default('VOLUME'),
      },
      {
        name: 'tag',
        type: 'Query',
        schema: z.string().optional(),
      },
    ],
    response: MarketsResDto,
  },
  {
    method: 'post',
    path: '/v1/market/propose',
    alias: 'MarketController_propose',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: ProposeReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'post',
    path: '/v1/market/report',
    alias: 'MarketController_report',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: ReportMarketReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/market/search',
    alias: 'MarketController_searchMarkets',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'query',
        type: 'Query',
        schema: z.string().min(2).max(50),
      },
    ],
    response: MarketBriefInfosResDto,
  },
  {
    method: 'get',
    path: '/v1/market/top-predictors/:marketId/:outcome',
    alias: 'MarketController_getTopPredictors',
    requestFormat: 'json',
    parameters: [
      {
        name: 'marketId',
        type: 'Path',
        schema: z.string().min(66).max(66),
      },
      {
        name: 'outcome',
        type: 'Path',
        schema: z.string().min(1).max(50),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: MarketTopPredictorsResDto,
  },
  {
    method: 'post',
    path: '/v1/market/validate',
    alias: 'MarketController_validateMarketCreation',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ title: z.string().min(1).max(100) }).passthrough(),
      },
    ],
    response: ValidateMarketResDto,
  },
  {
    method: 'get',
    path: '/v1/portfolio',
    alias: 'PortfolioController_getUserPortfolio',
    requestFormat: 'json',
    response: UserPortfolioResDto,
  },
  {
    method: 'get',
    path: '/v1/portfolio/activities',
    alias: 'PortfolioController_getActivities',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'filter',
        type: 'Query',
        schema: z.enum(['Predict', 'Redeem', 'Void']).optional(),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['NEWEST', 'OLDEST']),
      },
    ],
    response: MarketActivitiesResDto,
  },
  {
    method: 'get',
    path: '/v1/portfolio/dispute',
    alias: 'PortfolioController_getDisputes',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['NEWEST', 'VALUE']).optional().default('NEWEST'),
      },
    ],
    response: PortfolioDisputesResDto,
  },
  {
    method: 'get',
    path: '/v1/portfolio/dispute/claimable',
    alias: 'PortfolioController_getClaimableDispute',
    requestFormat: 'json',
    response: z.object({ claimable: z.number().int() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/portfolio/positions',
    alias: 'PortfolioController_getPositions',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'filter',
        type: 'Query',
        schema: z.enum(['LIVE', 'ENDED']).optional(),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['VOLUME', 'NEWEST']),
      },
    ],
    response: PositionsResDto,
  },
  {
    method: 'post',
    path: '/v1/predict/claim',
    alias: 'PredictController_claim',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: ClaimReqDto,
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/deposit-channel-collateral',
    alias: 'PredictController_depositChannelCollateral',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ amount: z.number().int() }).passthrough(),
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/deposit-dispute-collateral',
    alias: 'PredictController_depositDisputeCollateral',
    requestFormat: 'form-data',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: DepositDisputeCollateralReqDto,
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/finalize',
    alias: 'PredictController_finalize',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ marketId: z.string().min(66).max(66) }).passthrough(),
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/go',
    alias: 'PredictController_go',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: UserOperationDto,
      },
    ],
    response: z.object({ hash: z.string() }).passthrough(),
  },
  {
    method: 'post',
    path: '/v1/predict/predict',
    alias: 'PredictController_predict',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: PredictReqDto,
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/redeem',
    alias: 'PredictController_redeem',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: RedeemReqDto,
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/withdraw',
    alias: 'PredictController_withdraw',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: WithdrawReqDto,
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'post',
    path: '/v1/predict/withdraw-channel-collateral',
    alias: 'PredictController_withdrawChannelCollateral',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ amount: z.number().int() }).passthrough(),
      },
    ],
    response: UserOperationDto,
  },
  {
    method: 'get',
    path: '/v1/referral/benefit',
    alias: 'ReferralController_getReferralBenefitByProxyAddress',
    requestFormat: 'json',
    response: ReferralBenefitResDto,
  },
  {
    method: 'get',
    path: '/v1/referral/benefits',
    alias: 'ReferralController_getReferralBenefits',
    requestFormat: 'json',
    response: ReferralBenefitsResDto,
  },
  {
    method: 'get',
    path: '/v1/referral/claimable',
    alias: 'ReferralController_getClaimableReferral',
    requestFormat: 'json',
    response: ClaimableReferralResDto,
  },
  {
    method: 'get',
    path: '/v1/referral/details',
    alias: 'ReferralController_getReferralDetails',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'type',
        type: 'Query',
        schema: z.enum(['CommissionReward', 'FeeRebate']),
      },
    ],
    response: RewardDetailsResDto,
  },
  {
    method: 'get',
    path: '/v1/referral/leaderboard',
    alias: 'ReferralController_getReferralLeaderboard',
    requestFormat: 'json',
    response: ReferralLeaderboardResDto,
  },
  {
    method: 'get',
    path: '/v1/referral/total',
    alias: 'ReferralController_getReferralDashboard',
    requestFormat: 'json',
    response: ReferralDashboardResDto,
  },
  {
    method: 'get',
    path: '/v1/share',
    alias: 'ShareController_getMyShare',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
      {
        name: 'order',
        type: 'Query',
        schema: z.enum(['VALUE', 'NEWEST']).optional().default('VALUE'),
      },
    ],
    response: GetSharesResDto,
  },
  {
    method: 'post',
    path: '/v1/share',
    alias: 'ShareController_share',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: ShareReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/share/dashboard',
    alias: 'ShareController_getMyShareDashboard',
    requestFormat: 'json',
    response: MyShareDashboardResDto,
  },
  {
    method: 'get',
    path: '/v1/share/dashboard/main',
    alias: 'ShareController_getShareDashboard',
    requestFormat: 'json',
    response: ShareDashboardResDto,
  },
  {
    method: 'get',
    path: '/v1/share/odds/:marketId',
    alias: 'ShareController_getOdds',
    requestFormat: 'json',
    parameters: [
      {
        name: 'marketId',
        type: 'Path',
        schema: z.string().min(66).max(66),
      },
    ],
    response: z.object({ odds: z.number() }).passthrough(),
  },
  {
    method: 'post',
    path: '/v1/user',
    alias: 'UserController_createUser',
    requestFormat: 'json',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: z.object({ referralCode: z.string() }).partial().passthrough(),
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/user/:address',
    alias: 'UserController_getUserInfo',
    requestFormat: 'json',
    parameters: [
      {
        name: 'address',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: UserInfoResDto,
  },
  {
    method: 'get',
    path: '/v1/user/:address/activities',
    alias: 'UserController_getUserActivities',
    requestFormat: 'json',
    parameters: [
      {
        name: 'address',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: UserActivitiesResDto,
  },
  {
    method: 'get',
    path: '/v1/user/:address/positions',
    alias: 'UserController_getUserPositions',
    requestFormat: 'json',
    parameters: [
      {
        name: 'address',
        type: 'Path',
        schema: z.string(),
      },
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: PositionsResDto,
  },
  {
    method: 'get',
    path: '/v1/user/:address/stats',
    alias: 'UserController_getUserStats',
    requestFormat: 'json',
    parameters: [
      {
        name: 'address',
        type: 'Path',
        schema: z.string(),
      },
    ],
    response: UserStatsResDto,
  },
  {
    method: 'patch',
    path: '/v1/user/profile',
    alias: 'UserController_updateUserProfile',
    requestFormat: 'form-data',
    parameters: [
      {
        name: 'body',
        type: 'Body',
        schema: UpdateUserProfileReqDto,
      },
    ],
    response: z.object({ success: z.boolean() }).passthrough(),
  },
  {
    method: 'get',
    path: '/v1/user/subscription',
    alias: 'UserController_getUserSubscription',
    requestFormat: 'json',
    parameters: [
      {
        name: 'page',
        type: 'Query',
        schema: z.number().int().gte(0).optional().default(0),
      },
      {
        name: 'limit',
        type: 'Query',
        schema: z.number().int().gt(0).lte(50).optional().default(50),
      },
    ],
    response: UserSubscriptionsResDto,
  },
]);

export const api = new Zodios(endpoints);

export function createApiClient(baseUrl: string, options?: ZodiosOptions) {
  return new Zodios(baseUrl, endpoints, options);
}
