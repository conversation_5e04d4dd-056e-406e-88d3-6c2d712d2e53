import { z } from 'zod';

const GetMarketsOrderSchema = z.enum(['VOLUME', 'NEWEST', 'ENDING_SOON', 'COMPETITIVE']);

export type GetMarketsOrderEnum = z.infer<typeof GetMarketsOrderSchema>;

export const GetMarketFilterServerSchema = z.enum(['LIVE', 'DISPUTABLE']);

export type GetMarketFilterServerEnum = z.infer<typeof GetMarketFilterServerSchema>;

export const GetMarketFilterWillAllSchema = GetMarketFilterServerSchema.or(z.literal('ALL'));

export type GetMarketFilterWithAllEnum = z.infer<typeof GetMarketFilterWillAllSchema>;

export const GetMarketsRequestOptionsSchema = z.object({
  page: z.number().default(0).optional(),
  limit: z.number().default(50).optional(),
  filter: GetMarketFilterWillAllSchema.optional(),
  order: GetMarketsOrderSchema.default('VOLUME').optional(),
});

export type GetMarketsRequestOptions = z.infer<typeof GetMarketsRequestOptionsSchema>;
