import { z } from 'zod';
import { MarketStatusEnumSchema } from '../market/market.schema.server';

export const PortfolioActivitiesQueryOptionsSchema = z.object({
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
  filter: z.enum(['All', 'Predict', 'Redeem', 'Void']),
  order: z.enum(['NEWEST', 'OLDEST']),
});

export const PortfolioDisputeQueryOptionsSchema = z.object({
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
  order: z.enum(['NEWEST', 'VALUE']).optional().default('NEWEST'),
});

export const PortfolioPositionsQueryOptionsSchema = z.object({
  page: z.number().default(0).optional(),
  limit: z.number().default(50).optional(),
  filter: z.enum(['LIVE', 'ENDED']).optional(),
  order: z.enum(['VOLUME', 'NEWEST']),
});

export type PortfolioActivitiesQueryOptions = z.infer<typeof PortfolioActivitiesQueryOptionsSchema>;
export type PortfolioDisputeQueryOptions = z.infer<typeof PortfolioDisputeQueryOptionsSchema>;

export type PortfolioPositionsQueryOptions = z.infer<typeof PortfolioPositionsQueryOptionsSchema>;

export const PortfolioPositionsOrderQuerySchema = z.enum(['VOLUME', 'NEWEST']);

export const PortfolioPositionsFilterQuerySchema = z.enum(['LIVE', 'ENDED']).optional();

export type PortfolioPositionsOrderQuery = z.infer<typeof PortfolioPositionsOrderQuerySchema>;
export type PortfolioPositionsFilterQuery = z.infer<typeof PortfolioPositionsFilterQuerySchema>;

// Response Schemas from OpenAPI
export const UserPortfolioResponseSchema = z.object({
  positionsValue: z.string(),
  profit: z.object({
    day: z.string(),
    week: z.string(),
    month: z.string(),
    total: z.string(),
  }),
});

export const ClaimableDisputeResponseSchema = z.object({
  claimable: z.string(),
});

// Additional Portfolio specific schemas
export const PortfolioProfitSchema = z.object({
  day: z.string(),
  week: z.string(),
  month: z.string(),
  total: z.string(),
});

export const PortfolioSummarySchema = z.object({
  positionsValue: z.string(),
  profit: PortfolioProfitSchema,
});

export const PortfolioPostionItemSchema = z.object({
  market: z.object({
    id: z.string(),
    title: z.string(),
    imageUrl: z.string().nullable(),
    status: MarketStatusEnumSchema,
  }),
  user: z.object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullable(),
  }),
  outcome: z.string(),
  outcomeOrder: z.number(),
  value: z.string(),
  estimatedOdds: z.string(),
  estimatedWin: z.string(),
  updatedAt: z.string(),
  redeemedAt: z.string().optional(),
  refundedAt: z.string().optional(),
});

export const PositionsResponseSchema = z.object({
  positions: z.array(PortfolioPostionItemSchema),
  totalLength: z.number().int().gte(0),
});

export const PortfolioDisputeSchema = z.object({
  id: z.string(),
  marketId: z.string(),
  marketTitle: z.string(),
  marketImageUrl: z.string().nullable(),
  disputeAmount: z.number().int(),
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED']),
  reason: z.string(),
  createdAt: z.string(),
  resolvedAt: z.string().optional(),
});

export const PortfolioPositionsResponseSchema = z.object({
  positions: z.array(
    z.object({
      market: z
        .object({
          id: z.string(),
          title: z.string(),
          imageUrl: z.string().nullable(),
          status: MarketStatusEnumSchema,
        })
        .passthrough(),
      user: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullable(),
      }),
      outcome: z.string(),
      outcomeOrder: z.number(),
      value: z.string(),
      estimatedOdds: z.string(),
      estimatedWin: z.string(),
      updatedAt: z.string(),
      redeemedAt: z.string().optional(),
      voidedAt: z.string().optional(),
    })
  ),
  totalLength: z.number(),
});

export const PortfolioActivitySchema = z.object({
  market: z.object({
    id: z.string(),
    title: z.string(),
    imageUrl: z.string().nullable(),
  }),
  user: z.object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullable(),
  }),
  outcomes: z.array(
    z.object({
      outcome: z.string(),
      outcomeOrder: z.number(),
    })
  ),
  amount: z.string(),
  transactionHash: z.string(),
  timestamp: z.string(),
  type: z.string(),
});

export const PortfolioActivitiesResponseSchema = z.object({
  activities: z.array(PortfolioActivitySchema),
  totalLength: z.number(),
});

export const PortfolioDisputesResponseSchema = z.object({
  disputes: z.array(
    z.object({
      market: z
        .object({
          id: z.string(),
          title: z.string(),
          imageUrl: z.string().nullish(),
          status: MarketStatusEnumSchema,
        })
        .passthrough(),
      amount: z.string(),
      reward: z.string().optional(),
      timestamp: z.string(),
      claimedAt: z.string().optional(),
    })
  ),
  totalLength: z.number(),
});

// Type exports
export type UserPortfolioResponse = z.infer<typeof UserPortfolioResponseSchema>;
export type ClaimableDisputeResponse = z.infer<typeof ClaimableDisputeResponseSchema>;
export type PortfolioPositionsResponse = z.infer<typeof PortfolioPositionsResponseSchema>;
export type PortfolioDisputesResponse = z.infer<typeof PortfolioDisputesResponseSchema>;
export type PortfolioActivitiesResponse = z.infer<typeof PortfolioActivitiesResponseSchema>;

// Schema types for transforms
export type PortfolioPosition = z.infer<typeof PortfolioPostionItemSchema>;
export type PortfolioActivity = z.infer<typeof PortfolioActivitySchema>;
export type PortfolioDispute = z.infer<typeof PortfolioDisputeSchema>;

// Filter and order type unions
export type ActivityFilter = 'Predict' | 'Redeem' | 'Void';
export type ActivityOrder = 'NEWEST' | 'OLDEST';
export type DisputeOrder = 'NEWEST' | 'VALUE';
export type PositionFilter = 'LIVE' | 'ENDED';
export type PositionOrder = 'VOLUME' | 'NEWEST';

export const getPortfolioPositionsQuerySchema = z.object({
  // ... existing code ...
});
