import { z } from 'zod';

export const ReferralBenefitResponseSchema = z.object({
  level: z.number(),
  commissionRewardRatio: z.number(),
  feeRebateRatio: z.number(),
  accumulatedProfit: z.string(),
  totalInvitees: z.number(),
  referralCode: z.string(),
  profit: z.string(),
  bindReferralCode: z.string().optional(),
});

export const GetReferralDetailsRequestSchema = z.object({
  page: z.number().int().gte(0).default(0).optional(),
  limit: z.number().int().gt(0).lte(50).default(50).optional(),
  type: z.enum(['CommissionReward', 'FeeRebate']),
});

export const ReferralBenefitsResponseSchema = z.object({
  benefits: z.array(
    z.object({
      level: z.number(),
      commissionRewardRatio: z.number(),
      feeRebateRatio: z.number(),
      accumulatedProfit: z.string(),
    })
  ),
});

export const ReferralTotalResponseSchema = z.object({
  totalCommissionReward: z.string(),
  totalFeeRebate: z.string(),
});

export const ReferralLeaderboardResponseSchema = z.object({
  leaderboard: z.array(
    z.object({
      address: z.string(),
      commissionReward: z.string(),
      nickname: z.string(),
      invitees: z.number(),
    })
  ),
});

export const RewardDetailsResponseSchema = z.object({
  rewards: z.array(
    z
      .object({
        timestamp: z.string(),
        amount: z.string(),
      })
      .passthrough()
  ),
  totalLength: z.number(),
});

export const ReferralClaimableResponseSchema = z.object({
  commissionReward: z.string(),
  feeRebate: z.string(),
});
export type ReferralClaimableResponse = z.infer<typeof ReferralClaimableResponseSchema>;
export type GetReferralDetailsRequest = z.infer<typeof GetReferralDetailsRequestSchema>;
export type ReferralBenefitsResponse = z.infer<typeof ReferralBenefitsResponseSchema>;
export type ReferralDashboardResponse = z.infer<typeof ReferralTotalResponseSchema>;
export type ReferralLeaderboardResponse = z.infer<typeof ReferralLeaderboardResponseSchema>;
export type RewardDetailsResponse = z.infer<typeof RewardDetailsResponseSchema>;
export type ReferralBenefitResponse = z.infer<typeof ReferralBenefitResponseSchema>;

// Schema types for transforms
export type ReferralBenefit = z.infer<typeof ReferralBenefitsResponseSchema>['benefits'][number];
export type ReferralLeaderboardEntry = z.infer<
  typeof ReferralLeaderboardResponseSchema
>['leaderboard'][number];
export type RewardDetail = z.infer<typeof RewardDetailsResponseSchema>['rewards'][number];
