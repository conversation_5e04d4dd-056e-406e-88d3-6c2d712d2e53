import { formatUsdc } from '@/lib/format';
import { toLocalDate, toPercentage } from '@/lib/utils';
import type {
  ReferralBenefitResponse,
  ReferralBenefitsResponse,
  ReferralDashboardResponse,
  ReferralLeaderboardResponse,
  RewardDetailsResponse,
  ReferralBenefit,
  ReferralLeaderboardEntry,
  RewardDetail,
  ReferralClaimableResponse,
} from './referral.schema.server';
import BigNumber from 'bignumber.js';

export const transformReferralBenefitResponse = (data: ReferralBenefitResponse) => {
  return {
    ...data,

    profit: {
      raw: BigNumber(data.profit),
      formatted: formatUsdc(data.profit),
    },

    accumulatedProfit: {
      raw: BigNumber(data.accumulatedProfit),
      formatted: formatUsdc(data.accumulatedProfit),
    },
  };
};

export const transformReferralBenefit = (data: ReferralBenefit) => {
  return {
    ...data,
    commissionRewardRatio: toPercentage(data.commissionRewardRatio),
    feeRebateRatio: toPercentage(data.feeRebateRatio),
    accumulatedProfit: formatUsdc(data.accumulatedProfit),
  };
};

export const transformReferralBenefitsResponse = (data: ReferralBenefitsResponse) => {
  return {
    ...data,
    benefits: data.benefits.map(transformReferralBenefit),
  };
};

export const transformReferralTotalResponse = (data: ReferralDashboardResponse) => {
  const totalCommissionReward = BigNumber(data.totalCommissionReward);
  const totalFeeRebate = BigNumber(data.totalFeeRebate);
  const totalReward = totalCommissionReward.plus(totalFeeRebate);
  return {
    ...data,

    totalCommissionReward: {
      raw: totalCommissionReward,
      formatted: formatUsdc(data.totalCommissionReward),
    },

    totalFeeRebate: {
      raw: totalFeeRebate,
      formatted: formatUsdc(data.totalFeeRebate),
    },

    totalReward: {
      raw: totalReward,
      formatted: formatUsdc(totalReward.toString()),
    },
  };
};

export const transformReferralLeaderboardEntry = (data: ReferralLeaderboardEntry) => {
  return {
    ...data,
    commissionReward: formatUsdc(data.commissionReward),
    rawCommissionReward: BigNumber(data.commissionReward),
  };
};

export const transformReferralLeaderboardResponse = (data: ReferralLeaderboardResponse) => {
  return {
    ...data,
    leaderboard: data.leaderboard.map(transformReferralLeaderboardEntry),
  };
};

export const transformRewardDetail = (data: RewardDetail) => {
  return {
    ...data,
    amountFormatted: formatUsdc(data.amount),
    localTimestamp: toLocalDate(data.timestamp),
  };
};

export const transformRewardDetailsResponse = (data: RewardDetailsResponse) => {
  return {
    ...data,
    rewards: data.rewards.map(transformRewardDetail),
  };
};

export const transformReferralClaimableResponse = (data: ReferralClaimableResponse) => {
  const commissionReward = BigNumber(data.commissionReward);
  const feeRebate = BigNumber(data.feeRebate);
  const totalReward = commissionReward.plus(feeRebate);
  return {
    ...data,

    commissionReward: {
      raw: commissionReward,
      formatted: formatUsdc(data.commissionReward),
    },
    feeRebate: {
      raw: feeRebate,
      formatted: formatUsdc(data.feeRebate),
    },

    totalReward: {
      raw: totalReward,
      formatted: formatUsdc(totalReward.toString()),
    },
  };
};
