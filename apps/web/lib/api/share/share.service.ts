import { ApiClient } from '@/lib/api/base-api';
import { ApiError } from '../base-api.error';
import {
  ShareDashboardResponseSchema,
  MyShareDashboardResponseSchema,
  GetSharesResponseSchema,
  ShareRequestSchema,
  SuccessResponseSchema,
  type ShareRequest,
  type GetSharesRequest,
} from './share.schema.server';
import {
  transformShareDashboardResponse,
  transformMyShareDashboardResponse,
  transformGetSharesResponse,
} from './share.transform';

export class ShareService {
  static BASE_PATH = '/data-api/v1/share';
  static ROUTES = {
    GET: {
      '/': '',
      '/dashboard': 'dashboard',
      '/dashboard/main': 'dashboard/main',
    },
    POST: {
      '/': '',
    },
  };

  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(ShareService.BASE_PATH);
  }

  /**
   * Get my shares with pagination and sorting
   */
  async getMyShares(options: GetSharesRequest = {}) {
    const searchParams = new URLSearchParams();
    if (options.page !== undefined) searchParams.set('page', options.page.toString());
    if (options.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options.order !== undefined) searchParams.set('order', options.order);

    const response = await this.api.get(ShareService.ROUTES.GET['/'], {
      searchParams,
    });

    const result = GetSharesResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(result.error, ShareService.ROUTES.GET['/'], response);
    }
    return transformGetSharesResponse(result.data);
  }

  /**
   * Get my share dashboard data
   */
  async getMyShareDashboard() {
    const response = await this.api.get(ShareService.ROUTES.GET['/dashboard']);
    const result = MyShareDashboardResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ShareService.ROUTES.GET['/dashboard'],
        response
      );
    }
    return transformMyShareDashboardResponse(result.data);
  }

  /**
   * Get main share dashboard data (global stats)
   */
  async getShareDashboardMain() {
    const response = await this.api.get(ShareService.ROUTES.GET['/dashboard/main']);
    const result = ShareDashboardResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ShareService.ROUTES.GET['/dashboard/main'],
        response
      );
    }
    return transformShareDashboardResponse(result.data);
  }

  /**
   * Share a market prediction
   */
  async shareMarket(shareData: ShareRequest) {
    // Validate request data
    const validatedData = ShareRequestSchema.safeParse(shareData);
    if (!validatedData.success) {
      throw ApiError.fromValidationError(
        validatedData.error,
        ShareService.ROUTES.POST['/'],
        shareData
      );
    }

    const response = await this.api.post(ShareService.ROUTES.POST['/'], {
      json: validatedData.data,
    });

    const result = SuccessResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(result.error, ShareService.ROUTES.POST['/'], response);
    }
    return result.data;
  }
}

export const shareService = new ShareService();
