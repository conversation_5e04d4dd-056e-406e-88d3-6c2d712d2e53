import { formatUsdc } from '@/lib/format';
import { createAmountField, toLocalDate } from '@/lib/utils';
import { DEFAULT_MARKET_AVATAR_URL } from '@/lib/constants';
import BigNumber from 'bignumber.js';
import type {
  ShareDashboardResponse,
  MyShareDashboardResponse,
  GetSharesResponse,
  ShareItem,
  ShareMarket,
} from './share.schema.server';

export const transformShareMarket = (data: ShareMarket) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
  };
};

export const transformShareItem = (data: ShareItem) => {
  return {
    ...data,
    market: transformShareMarket(data.market),
    winningsFormatted: formatUsdc(data.winnings),
    shareableAmountFormatted: formatUsdc(data.shareableAmount),
    rawWinnings: BigNumber(data.winnings),
    rawShareableAmount: BigNumber(data.shareableAmount),
    localSharedAt: data.sharedAt ? toLocalDate(data.sharedAt) : null,
    isShared: !!data.sharedAt,
  };
};

export const transformShareDashboardResponse = (data: ShareDashboardResponse) => {
  return {
    ...data,
    totalShareReward: createAmountField(data.totalShareReward),
  };
};

export type ShareDashboardResponseTransformed = ReturnType<typeof transformShareDashboardResponse>;

export const transformMyShareDashboardResponse = (data: MyShareDashboardResponse) => {
  return {
    ...data,
    claimableAmountFormatted: formatUsdc(data.claimableAmount),
    unLockableShareRewardsFormatted: formatUsdc(data.unLockableShareRewards),
    totalClaimedFormatted: formatUsdc(data.totalClaimed),
  };
};

export const transformGetSharesResponse = (data: GetSharesResponse) => {
  return {
    ...data,
    shares: data.shares.map(transformShareItem),
  };
};
