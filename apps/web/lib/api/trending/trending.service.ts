import { env } from '@/lib/env';
import { BaseFetcher } from '@repo/shared/lib/base-fetcher';
import {
  TrendingKeywordsResponseSchema,
  type TrendingKeywordsResponse,
} from '@repo/shared/schemas/trending.schema';

export class TrendingService extends BaseFetcher {
  constructor() {
    super();
  }

  async getTrendingKeywords(): Promise<TrendingKeywordsResponse> {
    try {
      const data = await this.kyInstance
        .get(`${env.NEXT_PUBLIC_STATIC_ASSETS_URL}/config/trending-keywords.json`)
        .json();
      const validatedData = TrendingKeywordsResponseSchema.safeParse(data);

      if (!validatedData.success) {
        throw new Error(
          `TrendingService.getTrendingKeywords failed: ${validatedData.error.message}`
        );
      }
      return validatedData.data;
    } catch (e) {
      console.error('Failed to fetch trending keywords data', e);
      throw e;
    }
  }
}

export const trendingService = new TrendingService();
