import { ApiClient } from '@/lib/api/base-api';
import {
  CreateUserRequestBody,
  UserActivitiesResponseSchema,
  UserInfoResponseSchema,
  UserPositionsResponseSchema,
  UserStatsResponseSchema,
  UserSubscriptionsResponseSchema,
} from './user.schema.server';
import {
  transformUserInfoResponse,
  transformUserActivitiesResponse,
  transformUserPositionsResponse,
  transformUserStatsResponse,
  transformUserSubscriptionsResponse,
} from './user.transform';
import { z } from 'zod';
import { ApiError } from '../base-api.error';

export class UserService {
  static BASE_PATH = '/data-api/v1/user';
  static ROUTES = {
    GET: {
      '/{address}': (address: string) => `${address}`,
      '/{address}/activities': (address: string) => `${address}/activities`,
      '/{address}/positions': (address: string) => `${address}/positions`,
      '/{address}/stats': (address: string) => `${address}/stats`,
      '/subscription': `subscription`,
    },
    POST: {
      '/': ``,
    },
    PATCH: {
      '/profile': `profile`,
    },
  };
  private api: ApiClient;
  constructor() {
    this.api = new ApiClient(UserService.BASE_PATH);
  }

  /**
   * 새로운 사용자 생성
   */
  async createUser(data: CreateUserRequestBody) {
    const response = await this.api.post(UserService.ROUTES.POST['/'], {
      json: data,
    });

    const result = z
      .object({
        success: z.boolean(),
      })
      .safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, UserService.ROUTES.POST['/'], response);
    }

    return result.data;
  }

  /**
   * 사용자 정보 조회
   */
  async getUserInfo(address: string) {
    const response = await this.api.get(UserService.ROUTES.GET['/{address}'](address));

    const result = UserInfoResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        UserService.ROUTES.GET['/{address}'](address),
        response
      );
    }
    return transformUserInfoResponse(result.data);
  }

  /**
   * 사용자 활동 내역 조회
   */
  async getUserActivities(address: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(UserService.ROUTES.GET['/{address}/activities'](address), {
      searchParams,
    });

    const result = UserActivitiesResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        UserService.ROUTES.GET['/{address}/activities'](address),
        response
      );
    }

    return transformUserActivitiesResponse(result.data);
  }

  /**
   * 사용자 포지션 조회
   */
  async getUserPositions(address: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(UserService.ROUTES.GET['/{address}/positions'](address), {
      searchParams,
    });

    const result = UserPositionsResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        UserService.ROUTES.GET['/{address}/positions'](address),
        response
      );
    }
    return transformUserPositionsResponse(result.data);
  }

  /**
   * 사용자 통계 조회
   */
  async getUserStats(address: string) {
    const response = await this.api.get(UserService.ROUTES.GET['/{address}/stats'](address));
    const result = UserStatsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        UserService.ROUTES.GET['/{address}/stats'](address),
        response
      );
    }
    return transformUserStatsResponse(result.data);
  }

  /**
   * 사용자 프로필 업데이트
   */
  async updateUserProfile(formData: FormData) {
    const response = await this.api.patch(UserService.ROUTES.PATCH['/profile'], {
      body: formData,
    });
    return response;
  }

  /**
   * 사용자 구독 정보 조회
   */
  async getUserSubscriptions(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(UserService.ROUTES.GET['/subscription'], { searchParams });
    const result = UserSubscriptionsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        UserService.ROUTES.GET['/subscription'],
        response
      );
    }
    return transformUserSubscriptionsResponse(result.data);
  }

  async isUserExist(address: string) {
    try {
      await this.getUserInfo(address);
      return true;
    } catch (error) {
      console.error('Get user info error:', error);
      return false;
    }
  }
}

export const userService = new UserService();
