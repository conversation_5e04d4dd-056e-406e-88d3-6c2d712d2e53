import { DEFAULT_MARKET_AVATAR_URL, DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc, formatNumberFull, formatProfitLoss, formatNumberCompact } from '@/lib/format';
import { toPercentage } from '@/lib/utils';
import type {
  UserInfoResponse,
  UserActivitiesResponse,
  UserPositionsResponse,
  UserStatsResponse,
  UserSubscriptionsResponse,
  UserActivity,
  UserPosition,
  UserSubscription,
} from './user.schema.server';

export const transformUserInfoResponse = (data: UserInfoResponse) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
  };
};

export const transformUserActivity = (data: UserActivity) => {
  return {
    ...data,
    market: data.market
      ? {
          ...data.market,
          imageUrl: data.market.imageUrl ?? DEFAULT_USER_AVATAR_URL,
        }
      : null,
    value: data.value ? formatUsdc(data.value) : null,
  };
};

export const transformUserActivitiesResponse = (data: UserActivitiesResponse) => {
  return {
    ...data,
    activities: data.activities.map(transformUserActivity),
  };
};

export const transformUserPosition = (data: UserPosition) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    value: formatUsdc(data.value),
    estimatedOdds: toPercentage(data.estimatedOdds),
    estimatedWin: formatUsdc(data.estimatedWin),
  };
};

export const transformUserPositionsResponse = (data: UserPositionsResponse) => {
  return {
    ...data,
    positions: data.positions.map(transformUserPosition),
  };
};

export const transformUserStatsResponse = (data: UserStatsResponse) => {
  // formatUsdc를 사용하여 숫자 값으로 변환한 후 각 타입에 맞는 포맷팅 적용
  const positionsValue = parseFloat(formatUsdc(data.positionsValue));
  const profit = parseFloat(formatUsdc(data.profit));
  const volume = parseFloat(formatUsdc(data.volume));
  const marketTraded = data.tradedMarketsCount; // 이미 숫자 타입

  return {
    ...data,
    stats: {
      // Positions Value: 숫자 타입 B (전체 노출 및 천단위 컴마 표시)
      positionsValue: formatNumberFull(positionsValue),

      // Profit / loss: 숫자 타입 B + 마이너스 표시
      profitLoss: formatProfitLoss(profit),

      // Volume traded: 숫자 타입 B (전체 노출 및 천단위 컴마 표시)
      volumeTraded: formatNumberFull(volume),

      // Market traded: 숫자 타입 D (전체 노출, 천단위 컴마 표시, 소수점 없음)
      marketTraded: marketTraded,
    },
    // 기존 포맷도 유지 (호환성을 위해)
    positionsValueFormatted: formatUsdc(data.positionsValue),
    profitFormatted: formatUsdc(data.profit),
    volumeFormatted: formatUsdc(data.volume),
  };
};

export const transformUserSubscription = (data: UserSubscription) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    subscribers: formatNumberCompact(data.subscribers),
    leader: {
      ...data.leader,
      imageUrl: data.leader.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
  };
};

export const transformUserSubscriptionsResponse = (data: UserSubscriptionsResponse) => {
  return {
    ...data,
    channels: data.channels.map(transformUserSubscription),
  };
};
