import { z } from 'zod';
import { ApiError } from './base-api.error';

export function formatZodError(error: z.ZodError) {
  return error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
}

export async function extractBodyFromResponse(response: Response) {
  try {
    const json = await response.json();
    return json;
  } catch (error) {
    console.error('Failed to extract body from response:', error);
    return response.body;
  }
}

export const validate =
  <T extends z.ZodType>(schema: T) =>
  (response: unknown, path: string): z.infer<T> => {
    const result = schema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(result.error, path, response);
    }
    return result.data;
  };

export const apiCall = async <T extends z.ZodType>(
  request: Promise<unknown>,
  responseSchema: T,
  path: string
): Promise<z.infer<T>> => {
  const response = await request;
  return validate(responseSchema)(response, path);
};
