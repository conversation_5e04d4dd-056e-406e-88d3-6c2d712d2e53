import { disconnect } from '@wagmi/core';
import { wagmiConfig } from './web3/wagmi';
import { useGlobalStore } from '@/store/global.store';
import { queryClient } from '@/components/providers/tanstack-query-client-provider';
import { userKeys, portfolioKeys, safeSmartAccountKeys } from '@/hooks/query/query-keys';
import { appKitModal } from '@/components/providers/wagmi-provider';

/**
 * 로그인 함수 - AppKit 모달을 열어 지갑 연결 및 SIWE 인증을 처리
 */
export const login = async () => {
  try {
    // AppKit 모달 열기
    appKitModal.setPreferredAccountType('eoa', 'eip155');
    appKitModal.open();
  } catch (error) {
    console.error('Failed to open login modal:', error);
    throw error;
  }
};

/**
 * 로그아웃 함수 - 지갑 연결 해제 및 세션 정리
 */
export const logout = async () => {
  try {
    // wagmi disconnect
    await disconnect(wagmiConfig);
    // AppKit 계정 타입 초기화
    appKitModal.setPreferredAccountType('eoa', 'eip155');
  } catch (error) {
    console.error('Failed to disconnect:', error);
  } finally {
    // 로컬 스토어 및 쿼리 캐시 정리
    useGlobalStore.getState().clearSession();
    clearAuthCache();
  }
};

/**
 * 강제 로그아웃 함수
 * 로그아웃 후 0.8초 뒤에 새로고침
 */
export const logoutAndReload = async () => {
  await logout();
  clearAuthCache();
  setTimeout(() => {
    window.location.reload();
  }, 800);
};

/**
 * 인증 상태 확인 함수
 */
export const getAuthStatus = () => {
  const { isSignedIn, session } = useGlobalStore.getState();
  return {
    isSignedIn,
    session,
    isAuthenticated: isSignedIn && !!session,
  };
};

/**
 * 인증 캐시 클리어 함수
 */
export const clearAuthCache = () => {
  queryClient.removeQueries({ queryKey: userKeys.all });
  queryClient.removeQueries({ queryKey: portfolioKeys.all });
  queryClient.removeQueries({ queryKey: safeSmartAccountKeys.all });
};

/**
 * 전체 인증 상태 리셋 함수 (개발용)
 */
export const resetAuth = async () => {
  try {
    await logout();
    clearAuthCache();
    console.log('Authentication state has been reset');
  } catch (error) {
    console.error('Failed to reset authentication:', error);
    throw error;
  }
};
