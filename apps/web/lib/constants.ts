type ValueOf<T> = T[keyof T];

export const MARKET_STATUS_TEXT_MAP = {
  OPEN: 'Live',
  REVIEWING: 'Awaiting Result',
  DISPUTABLE: 'Disputable',
  DISPUTED: 'Under Review',
  CLOSED_WITHOUT_DISPUTE: 'Resolved',
  CLOSED_WITH_DISPUTE_ACCEPTED: 'Resolved',
  CLOSED_WITH_DISPUTE_REJECTED: 'Resolved',
  CANCELLED_WITH_UNMET: 'Void',
  CANCELLED_WITH_INVALID: 'Void',
  CANCELLED_WITH_OUTCOME_NOT_PROPOSED: 'Void',
};

export const PREDICTION_STATUS_TEXT_MAP = {
  OPEN: 'Live',
  REVIEWING: 'Awaiting Result',
  DISPUTABLE: 'Disputable',
  DISPUTED: 'Under Review',
  CLOSED_WITHOUT_DISPUTE: 'Resolved',
  CLOSED_WITH_DISPUTE_ACCEPTED: 'Lose a dispute',
  CLOSED_WITH_DISPUTE_REJECTED: 'Win a dispute',
  CANCELLED_WITH_UNMET: 'Void',
  CANCELLED_WITH_INVALID: 'Void',
  CANCELLED_WITH_OUTCOME_NOT_PROPOSED: 'Void',
} as const;

export type PredictionStatusTextValue = ValueOf<typeof PREDICTION_STATUS_TEXT_MAP>;

export const TEXT_SIZE_CSS_VARS = {
  xxs8: 'var(--text-size-xxs8)',
  xxs10: 'var(--text-size-xxs10)',
  xxs: 'var(--text-size-xxs)',
  xs: 'var(--text-size-xs)',
  sm13: 'var(--text-size-sm13)',
  sm: 'var(--text-size-sm)',
  base15: 'var(--text-size-base15)',
  base: 'var(--text-size-base)',
  lg: 'var(--text-size-lg)',
  xl: 'var(--text-size-xl)',
  '2xl': 'var(--text-size-2xl)',
  '3xl': 'var(--text-size-3xl)',
  '4xl': 'var(--text-size-4xl)',
  '5xl': 'var(--text-size-5xl)',
  '6xl': 'var(--text-size-6xl)',
  '7xl': 'var(--text-size-7xl)',
  '8xl': 'var(--text-size-8xl)',
  '9xl': 'var(--text-size-9xl)',
};

export const INNER_LINKS = {
  HOME: '/',
  MAIN: {
    ADDRESS_POSITIONS: (address: string) => `/${address}/positions`,
    ARCADE: {
      ACTIVITY: '/arcade/activity',
      RANKS: '/arcade/ranks',
      EXPERT_LOUNGE: '/arcade/expert-lounge',
      WEEKLY_QUIZ: '/arcade/weekly-quiz',
    },
    CHANNELS: {
      DETAIL: (channelId: string) => `/channels/${channelId}`,
      LEADERBOARD: (channelId: string) => `/channels/${channelId}/leaderboard`,
      POSTS: {
        ROOT: (channelId: string) => `/channels/${channelId}/posts`,
        DETAIL: (channelId: string, postId: string) => `/channels/${channelId}/posts/${postId}`,
        EDIT: (channelId: string, postId: string) => `/channels/${channelId}/posts/${postId}/edit`,
        NEW: (channelId: string) => `/channels/${channelId}/posts/new`,
      },
    },
    CREATE_PREDICTION: '/create-prediction',
    MARKETS: {
      ROOT: '/markets',
      ALL: '/markets/all',
      DETAIL: (marketId: string) => `/markets/${marketId}`,
    },
    PORTFOLIO: '/portfolio',
    REWARDS: {
      REFERRAL: '/rewards/referral',
      SHARE_BONUS: '/rewards/share-bonus',
    },
  },
  PROFILE: {
    CHANNELS: {
      DEPOSITS: '/profile/channels/deposits',
      PREDICTIONS: '/profile/channels/predictions',
      REWARDS: '/profile/channels/rewards',
      SETTING: '/profile/channels/setting',
    },
    POSITIONS: '/profile/positions',
    REFERRAL: '/profile/referral',
    SETTINGS: {
      EXPORT: '/profile/settings/export',
      PROFILE: '/profile/settings/profile',
    },
    SHARE_BONUS: '/profile/share-bonus',
    SUBSCRIPTIONS: '/profile/subscriptions',
    DOCUMENTATION: '/profile/documentation',
    SUPPORT: '/profile/support',
  },
};

export const ICON_PATH = {
  EXAMPLE_DARK: '/icons/example_dark.svg',
  LEARN_MORE: '/icons/learn_more.svg',
  YOUTUBE: '/icons/youtube.svg',
  X: '/icons/x.svg',
  X_FOOTER: '/icons/x_footer.svg',
  WALLET: '/icons/wallet.svg',
  VOLUME: '/icons/volume.svg',
  VIEWS: '/icons/views.svg',
  USDC: '/icons/usdc.svg',
  TRENDING: '/icons/trending.svg',
  TIME: '/icons/time.svg',
  TIKTOK: '/icons/tiktok.svg',
  THREE_DOTS: '/icons/three_dots.svg',
  TELEGRAM: '/icons/telegram.svg',
  TELEGRAM_FOOTER: '/icons/telegram_footer.svg',
  SUPPORT: '/icons/support.svg',
  SUBSCRIPTION: '/icons/subscription.svg',
  SUBSCRIPTION_INVERSED: '/icons/subscription_inversed.svg',
  SORT_VOLUME: '/icons/sort_volume.svg',
  SORT_NEWEST: '/icons/sort_newest.svg',
  SORT_ENDINGSOON: '/icons/sort_endingsoon.svg',
  SORT_COMPETITIVE: '/icons/sort_competitive.svg',
  SILVER_TROPHY: '/icons/silver_trophy.svg',
  SHARE_BONUS: '/icons/share_bonus.svg',
  SETTINGS: '/icons/settings.svg',
  REPORT: '/icons/report.svg',
  REPLY: '/icons/reply.svg',
  REFERRAL: '/icons/referral.svg',
  RED_PIN: '/icons/red_pin.svg',
  RED_PIN_ROTATED: '/icons/red_pin_rotated.svg',
  RED_PIN_OUTLINE: '/icons/red_pin_outline.svg',
  RED_ALERT: '/icons/red_alert.svg',
  RANKS: '/icons/ranks.svg',
  QRCODE: '/icons/qrcode.svg',
  PROPOSE: '/icons/propose.svg',
  POSITIONS: '/icons/positions.svg',
  NOTICE: '/icons/notice.svg',
  NODE_PLUS: '/icons/node-plus.svg',
  NEWEST: '/icons/newest.svg',
  MEMBER: '/icons/member.svg',
  MEDIUM: '/icons/medium.svg',
  MARKET: '/icons/market.svg',
  MARKET_INVERSED: '/icons/market_inversed.svg',
  MAIL: '/icons/mail.svg',
  LOGOUT: '/icons/logout.svg',
  LIQUIDITY: '/icons/liquidity.svg',
  KEYWORD: '/icons/keyword.svg',
  INSTAGRAM: '/icons/instagram.svg',
  INSIGHT: '/icons/insight.svg',
  IMAGE_FILL: '/icons/image-fill.svg',
  HEART: '/icons/heart.svg',
  HEADPHONES: '/icons/headphones.svg',
  GOLD_TROPHY: '/icons/gold_trophy.svg',
  FACEBOOK: '/icons/facebook.svg',
  EXTERNAL_LINK: '/icons/external_link.svg',
  EXCLAMATION: '/icons/excalmation.svg',
  EXAMPLE: '/icons/example.svg',
  ENDING_SOON: '/icons/ending_soon.svg',
  EDIT: '/icons/edit.svg',
  DOLLAR: '/icons/dollar.svg',
  DOCUMENTATION: '/icons/documentation.svg',
  DISCORD: '/icons/discord.svg',
  DISCORD_FOOTER: '/icons/discord_footer.svg',
  DELETE: '/icons/delete.svg',
  CROWN: '/icons/crown.svg',
  COPYLINK: '/icons/copylink.svg',
  COPY: '/icons/copy.svg',
  COMPETITIVE: '/icons/competitive.svg',
  COMMENTS: '/icons/comments.svg',
  CLOCK: '/icons/clock.svg',
  CLOCK_OUTLINE: '/icons/clock_outline.svg',
  CLAIM: '/icons/claim.svg',
  CLAIM_DEPOSIT: '/icons/claim_deposit.svg',
  CHEVRON_NEXT: '/icons/chevron-next.svg',
  CHECK: '/icons/check.svg',
  CHAT: '/icons/chat.svg',
  CHANNEL: '/icons/channel.svg',
  CATEGORY: '/icons/category.svg',
  CALENDAR: '/icons/calendar.svg',
  BTN_ICON: '/icons/btn_icon.svg',
  BRONZE_TROPHY: '/icons/bronze_trophy.svg',
  BONUS_REWARD: '/icons/bonus_reward.svg',
  BOMB: '/icons/bomb.svg',
  AUDIO_SETTINGS: '/icons/audio-settings.svg',
  ALERT: '/icons/alert.svg',
  ADD_DEPOSIT_ICON: '/icons/add_deposit_icon.svg',
  ACTIVITY: '/icons/activity.svg',
  ALERT_RED_BIG: '/icons/alert-red-big.svg',
  ABSTRACT: '/icons/abstract.svg',
  ADD_FRIENDS: '/icons/add-friends.svg',
};

export type IconPathName = keyof typeof ICON_PATH;

export const DEFAULT_MARKET_AVATAR_URL = '/default/default-market-avatar.svg';
export const DEFAULT_USER_AVATAR_URL = '/default/default-avatar.svg';
export const DEFAULT_CHANNEL_BANNER_URL = '/default/default-channel-banner.svg';

export const EXTERNAL_LINKS = {
  TERMS_OF_USE:
    'https://drive.google.com/file/d/12hRzjzP-yRCcsI-z4gn6Ovy-ZQ9kstMd/view?usp=sharing',
  PRIVACY_POLICY: 'https://drive.google.com/file/d/1D09DrugECXDJFdJeZUJUs5NFnI7OAQiO/view',
  COOKIE_POLICY: 'https://www.google.com',
  DISCLAIMER: 'https://www.google.com',
  REFERRAL_PROGRAM: 'https://www.google.com',
  SUPPORT: 'https://www.google.com',
  DOCUMENTATION: 'https://predictgo.gitbook.io',
  DISCORD: 'https://discord.gg/your_server',
  X: 'https://x.com/your_handle',
  MEDIUM: 'https://medium.com/your_blog',
  TELEGRAM: 'https://github.com/your_repo',
  SERVICE_DOCS_REFERRAL:
    'https://predictgo.gitbook.io/predictgo-service-documentation/using-predictgo/rewards/predictgo-referral-program',
  SERVICE_DOCS_SHARE_BONUS:
    'https://predictgo.gitbook.io/predictgo-service-documentation/using-predictgo/rewards/share-bonus',
  SERVICE_DOCS_WHAT_IS_CHANNEL:
    'https://predictgo.gitbook.io/predictgo-service-documentation/using-predictgo/what-is-a-channel',
};

export const TOAST_MESSAGE = {
  INVALID_URL: 'Please enter a valid URL',
  CHANNEL_SETTING_UPDATE_SUCCESS: 'Channel updated successfully!',
  CHANNEL_SETTING_UPDATE_ERROR: 'Failed to update channel',
  CHANNEL_SETTING_UPDATE_NO_CHANGES: 'No changes to save',

  INVALID_IMAGE_TYPE: 'Only JPG or PNG files are allowed',
  INVALID_IMAGE_SIZE: 'File size must be less than 1MB',

  DUPLICATE_OUTCOME: 'Each outcome must be unique.',
  FORBIDDEN_OUTCOME: "You cannot use 'Void' as an Outcome",
  ETHICAL_REVIEW_SUCCESS: 'Ethical review completed.',

  // Prediction deadline validation messages
  PREDICTION_END_TIME_MIN:
    'The prediction end time must be at least 10 minutes after the current time.',
  PREDICTION_END_TIME_MAX: 'The prediction end time must be within 30 days from the current time.',

  // Result confirmation deadline validation messages
  RESULT_CONFIRMATION_TIME_MIN:
    'The result confirmation time must be at least 5 minutes after the prediction end time.',
  RESULT_CONFIRMATION_TIME_MAX:
    'The result confirmation time must be within 7 days after the prediction end time.',
};

export const SNS_ICON_PATH = {
  youtube: ICON_PATH.YOUTUBE,
  twitter: ICON_PATH.X,
  telegram: ICON_PATH.TELEGRAM,
  facebook: ICON_PATH.FACEBOOK,
  discord: ICON_PATH.DISCORD,
  tiktok: ICON_PATH.TIKTOK,
  instagram: ICON_PATH.INSTAGRAM,
  abstract: ICON_PATH.ABSTRACT,
};
