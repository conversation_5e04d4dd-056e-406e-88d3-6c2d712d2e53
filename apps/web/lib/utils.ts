import { concat, pad, toHex, type Hex } from 'viem';
import type { UserOperation } from 'viem/account-abstraction';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import BigNumber from 'bignumber.js';
import { MarketOutcome } from './api/market/market.transform';
import { formatUsdc } from './format';

dayjs.extend(relativeTime);

export function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text);
}

export function isBrowser() {
  if (typeof window === 'undefined') {
    return false;
  }
  return true;
}

export function createPaymasterAndData(userOp: UserOperation): Hex {
  return concat([
    userOp.paymaster as Hex,
    pad(toHex(userOp.paymasterVerificationGasLimit || BigInt(0)), {
      size: 16,
    }),
    pad(toHex(userOp.paymasterPostOpGasLimit || BigInt(0)), {
      size: 16,
    }),
    (userOp.paymasterData as Hex) || ('0x' as Hex),
  ]);
}

export function convertUserOpToBigInt(userOp: any) {
  return {
    ...userOp,
    maxFeePerGas: BigInt(userOp.maxFeePerGas),
    maxPriorityFeePerGas: BigInt(userOp.maxPriorityFeePerGas),
    nonce: BigInt(userOp.nonce),
    paymasterPostOpGasLimit: BigInt(userOp.paymasterPostOpGasLimit),
    callGasLimit: BigInt(userOp.callGasLimit),
    preVerificationGas: BigInt(userOp.preVerificationGas),
    verificationGasLimit: BigInt(userOp.verificationGasLimit),
    paymasterVerificationGasLimit: BigInt(userOp.paymasterVerificationGasLimit),
  };
}
export function toLocalDate(date: string | Date | number) {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

export function toRelativeTime(date: string | Date | number) {
  return dayjs(date).fromNow(true) + ' ago';
}

export const toPercentage = (value: number | string): string => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return (numValue * 100).toFixed(2) + '%';
};

export function toScannerUrl(txHash: string) {
  return `https://sepolia.basescan.org/tx/${txHash}`;
}

export function shortenNumber(num: number | string): string {
  const value = typeof num === 'string' ? Number(num) : num;

  const units = [
    { threshold: 1_000_000_000_000, suffix: 'T' },
    { threshold: 1_000_000_000, suffix: 'B' },
    { threshold: 1_000_000, suffix: 'M' },
    { threshold: 1_000, suffix: 'K' },
  ];

  const unit = units.find(u => value >= u.threshold);
  return unit ? (value / unit.threshold).toFixed(2) + unit.suffix : value.toFixed(2);
}

export const updateSearchParams = (
  updates: Record<string, string | number | null>,
  router: any,
  searchParams: URLSearchParams,
  pathname: string
) => {
  const newParams = new URLSearchParams(searchParams.toString());

  Object.entries(updates).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      newParams.set(key, value.toString());
    } else {
      newParams.delete(key);
    }
  });

  router.push(`${pathname}?${newParams.toString()}`);
};

export function normalizeOptions(options: Record<string, any> | undefined) {
  if (!options) return '';

  return Object.entries(options)
    .map(([key, value]) => `${key}:${value}`)
    .join('::k');
}

export function calculatePercentage(value: string | number, total: string | number): number {
  const valueNumber = typeof value === 'string' ? parseFloat(value) : value;
  const totalNumber = typeof total === 'string' ? parseFloat(total) : total;

  return (valueNumber / totalNumber) * 100;
}

export function getPercentage(value: string | number, total: string | number): string {
  const percentage = calculatePercentage(value, total);

  if (percentage < 1) {
    return '<1%';
  }

  return percentage.toFixed(2) + '%';
}

export const padNumber = (num: number) => num.toString().padStart(2, '0');

export function getEsimatedOdds(totalVolume: BigNumber, outcomeVolume: BigNumber) {
  if (totalVolume.isZero()) {
    return '0';
  }
  const volume = outcomeVolume || BigNumber(1);
  const winningRatio = 0.8;
  const losePool = totalVolume.minus(volume);
  const losePoolAfterFee = BigNumber(losePool).multipliedBy(winningRatio);
  const odds = losePoolAfterFee.dividedBy(volume).plus(1);
  return odds.toFixed(2);
}

const DISPUTE_COLLATERAL_MAX_APPLICATIONS = 10;

function getMaxDisputeCollateral(marketOutcomes: MarketOutcome[]): BigNumber {
  const maxVolume = marketOutcomes.reduce(
    (max, outcome) => (outcome.volume.raw.isGreaterThan(max) ? outcome.volume.raw : max),
    BigNumber(0)
  );

  const maxDisputeCollateral = maxVolume.dividedBy(2);
  const unitAmount = maxDisputeCollateral.dividedBy(DISPUTE_COLLATERAL_MAX_APPLICATIONS);
  return unitAmount.integerValue().multipliedBy(DISPUTE_COLLATERAL_MAX_APPLICATIONS);
}

function getDisputeAmountByOne(maxDisputeCollateral: BigNumber): BigNumber {
  return maxDisputeCollateral.dividedBy(DISPUTE_COLLATERAL_MAX_APPLICATIONS).integerValue();
}

export function getDisputeAmount(marketOutcomes: MarketOutcome[]) {
  const maxDisputeCollateral = getMaxDisputeCollateral(marketOutcomes);
  const disputeAmount = getDisputeAmountByOne(maxDisputeCollateral);
  return disputeAmount;
}

/**
 * 배열에서 특정 필드 값과 일치하는 첫 번째 객체를 찾습니다.
 * @param array - 검색할 배열
 * @param field - 검색할 필드명
 * @param value - 찾을 값
 * @returns 일치하는 객체 또는 undefined
 */
export function findByField<T, K extends keyof T>(
  array: T[],
  field: K,
  value: T[K]
): T | undefined {
  return array.find(item => item[field] === value);
}

export function createAmountField(amount: number | string | bigint) {
  return {
    raw: BigNumber(amount),
    formatted: formatUsdc(amount),
  };
}

export function getMiliseconds(date: string | Date | number) {
  if (!date) return 0;
  return new Date(date).getTime();
}
export const allowedInputRegex = /^[\x20-\x7E\p{Emoji}\n\r]+$/u;
