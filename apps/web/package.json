{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev:turbopack": "NODE_OPTIONS='--inspect' next dev --turbopack --port 3100", "dev": "next dev --turbopack --port 3100", "prebuild": "node scripts/generate-build-info.js", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,mdx,css,scss,html}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md,mdx,css,scss,html}\"", "oai:generate": "openapi-zod-client https://api.dq.predictgo.io/data-api/docs-json -o lib/api/openapi.ts"}, "dependencies": {"@hookform/resolvers": "5.0.1", "@microsoft/clarity": "1.0.0", "@next/env": "15.3.3", "@next/third-parties": "15.3.3", "@reown/appkit": "1.7.10", "@reown/appkit-adapter-wagmi": "1.7.10", "@reown/appkit-siwe": "1.7.10", "@repo/shared": "workspace:*", "@repo/ui": "workspace:*", "@tanstack/react-query": "5.80.6", "@tanstack/react-query-devtools": "5.80.6", "@tanstack/react-table": "^8.21.3", "@wagmi/core": "2.17.3", "@zodios/core": "^10.9.6", "bignumber.js": "^9.3.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "8.6.0", "embla-carousel-fade": "8.6.0", "lodash-es": "^4.17.21", "lucide-react": "0.486.0", "motion": "^12.12.1", "next": "15.3.3", "next-themes": "0.4.6", "permissionless": "0.2.47", "qr-code-styling": "1.9.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.0", "react-markdown": "10.1.0", "remeda": "^2.23.0", "tailwindcss": "4.1.0", "viem": "2.31.3", "wagmi": "2.15.6", "zod": "3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/vitest-config": "workspace:*", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@svgr/webpack": "^8.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@vitest/coverage-istanbul": "3.1.1", "eslint": "^9.27.0", "eslint-plugin-storybook": "0.12.0", "glob": "^11.0.1", "openapi-zod-client": "^1.18.3", "postcss-load-config": "6.0.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^8.6.12", "typescript": "^5.8.2", "vitest": "3.1.1"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}